import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { CheckoutStep } from '@/app/checkout';

interface ProgressIndicatorProps {
  currentStep: CheckoutStep;
  onStepPress: (step: CheckoutStep) => void;
}

export default function ProgressIndicator({ currentStep, onStepPress }: ProgressIndicatorProps) {
  const steps = [
    { id: 'delivery' as CheckoutStep, title: 'Delivery', number: 1 },
    { id: 'payment' as CheckoutStep, title: 'Payment', number: 2 },
    { id: 'confirmation' as CheckoutStep, title: 'Confirm', number: 3 },
  ];

  const getStepStatus = (stepId: CheckoutStep) => {
    const currentIndex = steps.findIndex(s => s.id === currentStep);
    const stepIndex = steps.findIndex(s => s.id === stepId);
    
    if (stepIndex < currentIndex) return 'completed';
    if (stepIndex === currentIndex) return 'active';
    return 'inactive';
  };

  return (
    <View style={styles.container}>
      {steps.map((step, index) => {
        const status = getStepStatus(step.id);
        const isLast = index === steps.length - 1;
        
        return (
          <View key={step.id} style={styles.stepContainer}>
            <TouchableOpacity
              style={styles.stepContent}
              onPress={() => onStepPress(step.id)}
              activeOpacity={0.7}
            >
              <View style={[
                styles.stepCircle,
                status === 'completed' && styles.stepCircleCompleted,
                status === 'active' && styles.stepCircleActive,
              ]}>
                {status === 'completed' ? (
                  <Text style={styles.checkmark}>✓</Text>
                ) : (
                  <Text style={[
                    styles.stepNumber,
                    status === 'active' && styles.stepNumberActive,
                  ]}>
                    {step.number}
                  </Text>
                )}
              </View>
              
              <Text style={[
                styles.stepTitle,
                status === 'active' && styles.stepTitleActive,
                status === 'completed' && styles.stepTitleCompleted,
              ]}>
                {step.title}
              </Text>
            </TouchableOpacity>
            
            {!isLast && (
              <View style={[
                styles.stepConnector,
                status === 'completed' && styles.stepConnectorCompleted,
              ]} />
            )}
          </View>
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 20,
    backgroundColor: '#FAFAFA',
  },
  stepContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepContent: {
    alignItems: 'center',
    flex: 1,
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  stepCircleActive: {
    backgroundColor: '#FF4444',
  },
  stepCircleCompleted: {
    backgroundColor: '#34A853',
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666666',
  },
  stepNumberActive: {
    color: '#FFFFFF',
  },
  checkmark: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  stepTitle: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
  stepTitleActive: {
    color: '#FF4444',
    fontWeight: '600',
  },
  stepTitleCompleted: {
    color: '#34A853',
    fontWeight: '500',
  },
  stepConnector: {
    height: 2,
    backgroundColor: '#E0E0E0',
    flex: 1,
    marginHorizontal: 8,
  },
  stepConnectorCompleted: {
    backgroundColor: '#34A853',
  },
});