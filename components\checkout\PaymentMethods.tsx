import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Modal,
  ScrollView,
  Alert,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { PaymentMethod } from '@/app/checkout';

interface PaymentMethodsProps {
  paymentMethods: PaymentMethod[];
  selectedPayment: string;
  onPaymentSelect: (paymentId: string) => void;
}

export default function PaymentMethods({ 
  paymentMethods, 
  selectedPayment, 
  onPaymentSelect 
}: PaymentMethodsProps) {
  const [showAddCardModal, setShowAddCardModal] = useState(false);
  const [showCvvModal, setShowCvvModal] = useState(false);
  const [cvvInput, setCvvInput] = useState('');
  const [cashChangeAmount, setCashChangeAmount] = useState('');
  
  const [newCard, setNewCard] = useState({
    number: '',
    expiry: '',
    cvv: '',
    name: '',
    saveCard: false,
  });

  const handlePaymentSelect = (paymentId: string) => {
    const selectedMethod = paymentMethods.find(m => m.id === paymentId);
    
    if (selectedMethod?.type === 'card' && selectedMethod.id !== 'new_card') {
      // Show CVV modal for existing cards
      setShowCvvModal(true);
      setCvvInput('');
    } else {
      onPaymentSelect(paymentId);
      Haptics.selectionAsync();
    }
  };

  const handleCvvSubmit = () => {
    if (cvvInput.length >= 3) {
      onPaymentSelect(selectedPayment);
      setShowCvvModal(false);
      setCvvInput('');
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } else {
      Alert.alert('Invalid CVV', 'Please enter a valid CVV code.');
    }
  };

  const formatCardNumber = (text: string) => {
    const cleaned = text.replace(/\s/g, '');
    const match = cleaned.match(/.{1,4}/g);
    return match ? match.join(' ') : cleaned;
  };

  const formatExpiry = (text: string) => {
    const cleaned = text.replace(/\D/g, '');
    if (cleaned.length >= 2) {
      return cleaned.substring(0, 2) + '/' + cleaned.substring(2, 4);
    }
    return cleaned;
  };

  const getCardIcon = (type?: string) => {
    switch (type) {
      case 'visa': return '💳';
      case 'mastercard': return '💳';
      case 'amex': return '💳';
      default: return '💳';
    }
  };

  const handleAddCard = () => {
    // Validate card details
    if (!newCard.number || !newCard.expiry || !newCard.cvv || !newCard.name) {
      Alert.alert('Error', 'Please fill in all card details.');
      return;
    }

    // Simulate adding card
    Alert.alert('Success', 'Card added successfully!');
    setShowAddCardModal(false);
    setNewCard({ number: '', expiry: '', cvv: '', name: '', saveCard: false });
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Payment Method</Text>
      
      <View style={styles.paymentMethods}>
        {paymentMethods.map((method) => (
          <TouchableOpacity
            key={method.id}
            style={[
              styles.paymentOption,
              selectedPayment === method.id && styles.paymentOptionSelected,
            ]}
            onPress={() => handlePaymentSelect(method.id)}
            activeOpacity={0.7}
          >
            <View style={styles.paymentContent}>
              <Text style={styles.paymentIcon}>{method.icon}</Text>
              <View style={styles.paymentDetails}>
                <Text style={[
                  styles.paymentTitle,
                  selectedPayment === method.id && styles.paymentTitleSelected,
                ]}>
                  {method.title}
                </Text>
                {method.subtitle && (
                  <Text style={styles.paymentSubtitle}>{method.subtitle}</Text>
                )}
              </View>
            </View>
            
            <View style={[
              styles.radioButton,
              selectedPayment === method.id && styles.radioButtonSelected,
            ]}>
              {selectedPayment === method.id && (
                <View style={styles.radioButtonInner} />
              )}
            </View>
          </TouchableOpacity>
        ))}
        
        {/* Add New Card Option */}
        <TouchableOpacity
          style={styles.addCardOption}
          onPress={() => setShowAddCardModal(true)}
          activeOpacity={0.7}
        >
          <Text style={styles.addCardIcon}>+</Text>
          <Text style={styles.addCardText}>Add New Card</Text>
        </TouchableOpacity>
      </View>

      {/* Cash Change Amount */}
      {selectedPayment === 'cash' && (
        <View style={styles.cashSection}>
          <Text style={styles.cashTitle}>Change Required?</Text>
          <TextInput
            style={styles.cashInput}
            placeholder="Enter amount (optional)"
            value={cashChangeAmount}
            onChangeText={setCashChangeAmount}
            keyboardType="numeric"
          />
          <Text style={styles.cashNote}>
            Leave empty if you have exact change
          </Text>
        </View>
      )}

      {/* Security Indicators */}
      <View style={styles.securitySection}>
        <View style={styles.securityIndicators}>
          <Text style={styles.securityIcon}>🔒</Text>
          <Text style={styles.securityText}>SSL Encrypted</Text>
        </View>
        <View style={styles.securityIndicators}>
          <Text style={styles.securityIcon}>🛡️</Text>
          <Text style={styles.securityText}>Secure Payment</Text>
        </View>
      </View>

      {/* CVV Modal */}
      <Modal
        visible={showCvvModal}
        animationType="slide"
        presentationStyle="formSheet"
        transparent
      >
        <View style={styles.modalOverlay}>
          <View style={styles.cvvModal}>
            <Text style={styles.cvvTitle}>Enter CVV</Text>
            <Text style={styles.cvvSubtitle}>
              Please enter the 3-digit security code from the back of your card
            </Text>
            
            <TextInput
              style={styles.cvvInput}
              placeholder="CVV"
              value={cvvInput}
              onChangeText={setCvvInput}
              keyboardType="numeric"
              maxLength={4}
              secureTextEntry
              autoFocus
            />
            
            <View style={styles.cvvButtons}>
              <TouchableOpacity
                style={styles.cvvCancelButton}
                onPress={() => setShowCvvModal(false)}
                activeOpacity={0.7}
              >
                <Text style={styles.cvvCancelText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.cvvConfirmButton,
                  cvvInput.length < 3 && styles.cvvConfirmButtonDisabled,
                ]}
                onPress={handleCvvSubmit}
                disabled={cvvInput.length < 3}
                activeOpacity={0.8}
              >
                <Text style={styles.cvvConfirmText}>Confirm</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Add Card Modal */}
      <Modal
        visible={showAddCardModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Add New Card</Text>
            <TouchableOpacity
              onPress={() => setShowAddCardModal(false)}
              style={styles.closeButton}
              activeOpacity={0.7}
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent} keyboardShouldPersistTaps="handled">
            <View style={styles.cardForm}>
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Card Number</Text>
                <TextInput
                  style={styles.formInput}
                  placeholder="1234 5678 9012 3456"
                  value={formatCardNumber(newCard.number)}
                  onChangeText={(text) => setNewCard({ ...newCard, number: text.replace(/\s/g, '') })}
                  keyboardType="numeric"
                  maxLength={19}
                />
              </View>
              
              <View style={styles.formRow}>
                <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
                  <Text style={styles.formLabel}>Expiry Date</Text>
                  <TextInput
                    style={styles.formInput}
                    placeholder="MM/YY"
                    value={formatExpiry(newCard.expiry)}
                    onChangeText={(text) => setNewCard({ ...newCard, expiry: text.replace(/\D/g, '') })}
                    keyboardType="numeric"
                    maxLength={5}
                  />
                </View>
                
                <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
                  <Text style={styles.formLabel}>CVV</Text>
                  <TextInput
                    style={styles.formInput}
                    placeholder="123"
                    value={newCard.cvv}
                    onChangeText={(text) => setNewCard({ ...newCard, cvv: text })}
                    keyboardType="numeric"
                    maxLength={4}
                    secureTextEntry
                  />
                </View>
              </View>
              
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Cardholder Name</Text>
                <TextInput
                  style={styles.formInput}
                  placeholder="John Doe"
                  value={newCard.name}
                  onChangeText={(text) => setNewCard({ ...newCard, name: text })}
                  autoCapitalize="words"
                />
              </View>
              
              <TouchableOpacity
                style={styles.saveCardOption}
                onPress={() => setNewCard({ ...newCard, saveCard: !newCard.saveCard })}
                activeOpacity={0.7}
              >
                <View style={[
                  styles.checkbox,
                  newCard.saveCard && styles.checkboxSelected,
                ]}>
                  {newCard.saveCard && <Text style={styles.checkmark}>✓</Text>}
                </View>
                <Text style={styles.saveCardText}>Save this card for future purchases</Text>
              </TouchableOpacity>
            </View>
            
            <TouchableOpacity
              style={styles.addCardButton}
              onPress={handleAddCard}
              activeOpacity={0.8}
            >
              <Text style={styles.addCardButtonText}>Add Card</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  paymentMethods: {
    marginBottom: 20,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
  },
  paymentOptionSelected: {
    borderColor: '#FF4444',
    backgroundColor: '#FFF5F5',
  },
  paymentContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  paymentIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  paymentDetails: {
    flex: 1,
  },
  paymentTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  paymentTitleSelected: {
    color: '#FF4444',
  },
  paymentSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    borderColor: '#FF4444',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#FF4444',
  },
  addCardOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#FF4444',
    borderStyle: 'dashed',
    borderRadius: 8,
    padding: 16,
    justifyContent: 'center',
  },
  addCardIcon: {
    fontSize: 20,
    color: '#FF4444',
    marginRight: 8,
  },
  addCardText: {
    fontSize: 16,
    color: '#FF4444',
    fontWeight: '500',
  },
  cashSection: {
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
  },
  cashTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 12,
  },
  cashInput: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 8,
  },
  cashNote: {
    fontSize: 12,
    color: '#666666',
  },
  securitySection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 16,
  },
  securityIndicators: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  securityIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  securityText: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cvvModal: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 24,
    margin: 20,
    minWidth: 280,
  },
  cvvTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    textAlign: 'center',
    marginBottom: 8,
  },
  cvvSubtitle: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  cvvInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  cvvButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cvvCancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    alignItems: 'center',
  },
  cvvCancelText: {
    fontSize: 16,
    color: '#666666',
  },
  cvvConfirmButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#FF4444',
    alignItems: 'center',
  },
  cvvConfirmButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  cvvConfirmText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#666666',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  cardForm: {
    marginBottom: 24,
  },
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: 'row',
  },
  formLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#FAFAFA',
  },
  saveCardOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    borderRadius: 4,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    borderColor: '#FF4444',
    backgroundColor: '#FF4444',
  },
  checkmark: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  saveCardText: {
    fontSize: 14,
    color: '#666666',
    flex: 1,
  },
  addCardButton: {
    backgroundColor: '#FF4444',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
  },
  addCardButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});