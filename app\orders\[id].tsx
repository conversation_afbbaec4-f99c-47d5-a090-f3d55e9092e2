import { useThemeColor } from '@/hooks/useThemeColor';
import { useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ScrollView, StyleSheet, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function OrderDetailScreen() {
  const { id } = useLocalSearchParams();
  const backgroundColor = useThemeColor({}, 'background');
  const [order, setOrder] = useState(null);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setOrder({
        id,
        restaurant: 'Pizza Palace',
        items: [
          { name: 'Margherita Pizza', quantity: 1, price: '$12.99' },
          { name: 'Caesar Salad', quantity: 1, price: '$8.99' }
        ],
        total: '$24.99',
        status: 'delivered',
        date: '2024-01-15',
        deliveryAddress: '123 Main St, City, State'
      });
    }, 1000);
  }, [id]);

  if (!order) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <View style={styles.loadingContainer}>
          <Text>Loading order details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Order #{order.id}</Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Restaurant</Text>
          <Text style={styles.restaurantName}>{order.restaurant}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Items</Text>
          {order.items.map((item, index) => (
            <View key={index} style={styles.item}>
              <Text style={styles.itemName}>{item.quantity}x {item.name}</Text>
              <Text style={styles.itemPrice}>{item.price}</Text>
            </View>
          ))}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Status</Text>
          <Text style={styles.status}>{order.status}</Text>
        </View>

        <View style={styles.totalSection}>
          <Text style={styles.totalText}>Total: {order.total}</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { padding: 20 },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', marginBottom: 20 },
  section: { marginBottom: 20 },
  sectionTitle: { fontSize: 18, fontWeight: 'bold', marginBottom: 8 },
  restaurantName: { fontSize: 16 },
  item: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  itemName: { fontSize: 16 },
  itemPrice: { fontSize: 16, fontWeight: 'bold' },
  status: { fontSize: 16, color: '#007AFF' },
  totalSection: { marginTop: 20 },
  totalText: { fontSize: 20, fontWeight: 'bold', textAlign: 'center' },
});
