import { useThemeColor } from '@/hooks/useThemeColor';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function ExploreScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All', icon: '🍽️' },
    { id: 'trending', name: 'Trending', icon: '🔥' },
    { id: 'nearby', name: 'Nearby', icon: '📍' },
    { id: 'offers', name: 'Offers', icon: '💰' },
  ];

  const exploreItems = [
    { id: '1', title: 'Top Rated Restaurants', subtitle: 'Discover the best in your area' },
    { id: '2', title: 'New Restaurants', subtitle: 'Recently added to FoodWay' },
    { id: '3', title: 'Cuisines', subtitle: 'Explore different food types' },
    { id: '4', title: 'Special Offers', subtitle: 'Limited time deals' },
  ];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Explore</Text>
        
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesContainer}>
          {categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryButton,
                selectedCategory === category.id && styles.categoryButtonActive
              ]}
              onPress={() => setSelectedCategory(category.id)}
            >
              <Text style={styles.categoryIcon}>{category.icon}</Text>
              <Text style={styles.categoryText}>{category.name}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        <View style={styles.itemsContainer}>
          {exploreItems.map((item) => (
            <TouchableOpacity
              key={item.id}
              style={styles.exploreItem}
              onPress={() => router.push('/restaurants')}
            >
              <Text style={styles.itemTitle}>{item.title}</Text>
              <Text style={styles.itemSubtitle}>{item.subtitle}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { padding: 20 },
  title: { fontSize: 28, fontWeight: 'bold', marginBottom: 20 },
  categoriesContainer: { marginBottom: 24 },
  categoryButton: { 
    alignItems: 'center', 
    marginRight: 16, 
    padding: 12, 
    borderRadius: 12,
    backgroundColor: '#f5f5f5'
  },
  categoryButtonActive: { backgroundColor: '#007AFF' },
  categoryIcon: { fontSize: 24, marginBottom: 4 },
  categoryText: { fontSize: 12, fontWeight: '500' },
  itemsContainer: { gap: 12 },
  exploreItem: { 
    backgroundColor: 'white', 
    padding: 16, 
    borderRadius: 12, 
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  itemTitle: { fontSize: 18, fontWeight: 'bold', marginBottom: 4 },
  itemSubtitle: { fontSize: 14, color: '#666' },
});

