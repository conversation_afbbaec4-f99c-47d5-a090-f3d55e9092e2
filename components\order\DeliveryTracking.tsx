import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { OrderStatus } from '@/app/order-success';

interface DeliveryTrackingProps {
  orderStatuses: OrderStatus[];
  showMap: boolean;
  deliveryAddress: string;
  restaurantName: string;
}

export default function DeliveryTracking({
  orderStatuses,
  showMap,
  deliveryAddress,
  restaurantName,
}: DeliveryTrackingProps) {
  const pulseAnimation = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Pulse animation for active status
    const pulse = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );

    pulse.start();

    return () => pulse.stop();
  }, []);

  const handleViewMap = () => {
    // Navigate to full map view
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const getStatusColor = (status: OrderStatus['status']) => {
    switch (status) {
      case 'completed':
        return '#34A853';
      case 'active':
        return '#FF4444';
      case 'pending':
        return '#CCCCCC';
      default:
        return '#CCCCCC';
    }
  };

  const renderStatusIcon = (orderStatus: OrderStatus, index: number) => {
    const isActive = orderStatus.status === 'active';
    const isCompleted = orderStatus.status === 'completed';
    
    return (
      <Animated.View
        style={[
          styles.statusIcon,
          { backgroundColor: getStatusColor(orderStatus.status) },
          isActive && { transform: [{ scale: pulseAnimation }] },
        ]}
      >
        <Text style={styles.statusEmoji}>
          {isCompleted ? '✓' : orderStatus.icon}
        </Text>
      </Animated.View>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Order Status</Text>
      
      {/* Status Timeline */}
      <View style={styles.timeline}>
        {orderStatuses.map((status, index) => {
          const isLast = index === orderStatuses.length - 1;
          const isActive = status.status === 'active';
          const isCompleted = status.status === 'completed';
          
          return (
            <View key={status.id} style={styles.timelineItem}>
              <View style={styles.timelineLeft}>
                {renderStatusIcon(status, index)}
                {!isLast && (
                  <View
                    style={[
                      styles.timelineConnector,
                      isCompleted && styles.timelineConnectorCompleted,
                    ]}
                  />
                )}
              </View>
              
              <View style={styles.timelineContent}>
                <Text style={[
                  styles.statusTitle,
                  isActive && styles.statusTitleActive,
                  isCompleted && styles.statusTitleCompleted,
                ]}>
                  {status.title}
                </Text>
                
                <Text style={styles.statusDescription}>
                  {status.description}
                </Text>
                
                {status.timestamp && (
                  <Text style={styles.statusTime}>
                    {status.timestamp.toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </Text>
                )}
                
                {status.estimatedTime && !status.timestamp && (
                  <Text style={styles.estimatedTime}>
                    Est. {status.estimatedTime}
                  </Text>
                )}
              </View>
            </View>
          );
        })}
      </View>

      {/* Map Preview */}
      {showMap && (
        <View style={styles.mapSection}>
          <TouchableOpacity
            style={styles.mapPreview}
            onPress={handleViewMap}
            activeOpacity={0.8}
          >
            <View style={styles.mapPlaceholder}>
              <Text style={styles.mapIcon}>🗺️</Text>
              <Text style={styles.mapText}>Track on Map</Text>
              <Text style={styles.mapSubtext}>
                From {restaurantName} to {deliveryAddress.split(',')[0]}
              </Text>
            </View>
            
            <View style={styles.mapOverlay}>
              <Text style={styles.viewMapText}>View Full Map →</Text>
            </View>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 20,
  },
  timeline: {
    marginBottom: 20,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  timelineLeft: {
    alignItems: 'center',
    marginRight: 16,
  },
  statusIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusEmoji: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  timelineConnector: {
    width: 2,
    height: 40,
    backgroundColor: '#E0E0E0',
    marginTop: 8,
  },
  timelineConnectorCompleted: {
    backgroundColor: '#34A853',
  },
  timelineContent: {
    flex: 1,
    paddingTop: 4,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  statusTitleActive: {
    color: '#FF4444',
    fontWeight: '600',
  },
  statusTitleCompleted: {
    color: '#34A853',
  },
  statusDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  statusTime: {
    fontSize: 12,
    color: '#34A853',
    fontWeight: '500',
  },
  estimatedTime: {
    fontSize: 12,
    color: '#FF4444',
    fontWeight: '500',
  },
  mapSection: {
    marginTop: 10,
  },
  mapPreview: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  mapPlaceholder: {
    padding: 24,
    alignItems: 'center',
  },
  mapIcon: {
    fontSize: 40,
    marginBottom: 8,
  },
  mapText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  mapSubtext: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
  mapOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 68, 68, 0.9)',
    paddingVertical: 12,
    alignItems: 'center',
  },
  viewMapText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});