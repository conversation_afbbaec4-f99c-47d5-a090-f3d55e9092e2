import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Share,
  Clipboard,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';

interface ReferralStats {
  totalReferrals: number;
  successfulReferrals: number;
  pendingReferrals: number;
  totalEarnings: number;
  thisMonthEarnings: number;
  referralCode: string;
}

interface Referral {
  id: string;
  friendName: string;
  status: 'pending' | 'completed' | 'expired';
  inviteDate: string;
  completionDate?: string;
  reward: number;
}

export default function ReferralScreen() {
  const router = useRouter();
  const [stats] = useState<ReferralStats>({
    totalReferrals: 12,
    successfulReferrals: 8,
    pendingReferrals: 3,
    totalEarnings: 40,
    thisMonthEarnings: 15,
    referralCode: 'JOHN2024',
  });

  const [recentReferrals] = useState<Referral[]>([
    {
      id: '1',
      friendName: '<PERSON>',
      status: 'completed',
      inviteDate: '2024-01-15',
      completionDate: '2024-01-16',
      reward: 5,
    },
    {
      id: '2',
      friendName: 'Mike R.',
      status: 'pending',
      inviteDate: '2024-01-20',
      reward: 5,
    },
    {
      id: '3',
      friendName: 'Emma L.',
      status: 'completed',
      inviteDate: '2024-01-18',
      completionDate: '2024-01-19',
      reward: 5,
    },
  ]);

  const handleCopyCode = async () => {
    await Clipboard.setString(stats.referralCode);
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    Alert.alert('Copied!', `Referral code ${stats.referralCode} copied to clipboard`);
  };

  const handleShare = async (platform?: string) => {
    const message = `Hey! Join me on FoodieApp and get 50% off your first order! Use my referral code: ${stats.referralCode}\n\nDownload the app: https://foodieapp.com/download`;
    
    try {
      await Share.share({
        message,
        title: 'Join FoodieApp and save!',
      });
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#34A853';
      case 'pending': return '#FF9800';
      case 'expired': return '#F44336';
      default: return '#666666';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Completed ✅';
      case 'pending': return 'Pending ⏳';
      case 'expired': return 'Expired ❌';
      default: return status;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
            activeOpacity={0.7}
          >
            <Text style={styles.backIcon}>←</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Refer Friends</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Hero Section */}
        <View style={styles.heroSection}>
          <Text style={styles.heroIcon}>👥</Text>
          <Text style={styles.heroTitle}>Earn $5 for Every Friend!</Text>
          <Text style={styles.heroSubtitle}>
            Invite friends and both of you get amazing rewards
          </Text>
        </View>

        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{stats.totalReferrals}</Text>
              <Text style={styles.statLabel}>Total Invites</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{stats.successfulReferrals}</Text>
              <Text style={styles.statLabel}>Successful</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>${stats.totalEarnings}</Text>
              <Text style={styles.statLabel}>Total Earned</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>${stats.thisMonthEarnings}</Text>
              <Text style={styles.statLabel}>This Month</Text>
            </View>
          </View>
        </View>

        {/* Referral Code Section */}
        <View style={styles.codeSection}>
          <Text style={styles.sectionTitle}>Your Referral Code</Text>
          <View style={styles.codeContainer}>
            <View style={styles.codeDisplay}>
              <Text style={styles.codeText}>{stats.referralCode}</Text>
              <TouchableOpacity
                style={styles.copyButton}
                onPress={handleCopyCode}
                activeOpacity={0.7}
              >
                <Text style={styles.copyIcon}>📋</Text>
                <Text style={styles.copyText}>Copy</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Share Options */}
        <View style={styles.shareSection}>
          <Text style={styles.sectionTitle}>Share with Friends</Text>
          <View style={styles.shareOptions}>
            <TouchableOpacity
              style={styles.shareButton}
              onPress={() => handleShare('whatsapp')}
              activeOpacity={0.8}
            >
              <Text style={styles.shareIcon}>💬</Text>
              <Text style={styles.shareText}>WhatsApp</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.shareButton}
              onPress={() => handleShare('sms')}
              activeOpacity={0.8}
            >
              <Text style={styles.shareIcon}>📱</Text>
              <Text style={styles.shareText}>SMS</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.shareButton}
              onPress={() => handleShare('email')}
              activeOpacity={0.8}
            >
              <Text style={styles.shareIcon}>📧</Text>
              <Text style={styles.shareText}>Email</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.shareButton}
              onPress={() => handleShare()}
              activeOpacity={0.8}
            >
              <Text style={styles.shareIcon}>📤</Text>
              <Text style={styles.shareText}>More</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* How It Works */}
        <View style={styles.howItWorksSection}>
          <Text style={styles.sectionTitle}>How It Works</Text>
          <View style={styles.stepsContainer}>
            <View style={styles.step}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>1</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Share Your Code</Text>
                <Text style={styles.stepDescription}>
                  Send your referral code to friends via WhatsApp, SMS, or social media
                </Text>
              </View>
            </View>

            <View style={styles.step}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>2</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Friend Signs Up</Text>
                <Text style={styles.stepDescription}>
                  Your friend downloads the app and uses your code during registration
                </Text>
              </View>
            </View>

            <View style={styles.step}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>3</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Both Get Rewards</Text>
                <Text style={styles.stepDescription}>
                  You earn $5 and your friend gets 50% off their first order
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Recent Referrals */}
        <View style={styles.recentSection}>
          <View style={styles.recentHeader}>
            <Text style={styles.sectionTitle}>Recent Referrals</Text>
            <TouchableOpacity
              onPress={() => router.push('/offers/referral-history')}
              activeOpacity={0.7}
            >
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.referralsList}>
            {recentReferrals.map((referral) => (
              <View key={referral.id} style={styles.referralCard}>
                <View style={styles.referralInfo}>
                  <Text style={styles.referralName}>{referral.friendName}</Text>
                  <Text style={styles.referralDate}>
                    Invited on {new Date(referral.inviteDate).toLocaleDateString()}
                  </Text>
                  {referral.completionDate && (
                    <Text style={styles.referralCompletion}>
                      Completed on {new Date(referral.completionDate).toLocaleDateString()}
                    </Text>
                  )}
                </View>
                <View style={styles.referralStatus}>
                  <Text style={[
                    styles.statusText,
                    { color: getStatusColor(referral.status) }
                  ]}>
                    {getStatusText(referral.status)}
                  </Text>
                  {referral.status === 'completed' && (
                    <Text style={styles.rewardText}>+${referral.reward}</Text>
                  )}
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Terms */}
        <View style={styles.termsSection}>
          <Text style={styles.termsTitle}>Terms & Conditions</Text>
          <Text style={styles.termsText}>
            • Referral rewards are credited after friend's first successful order{'\n'}
            • Maximum 10 referrals per month{'\n'}
            • Referred friends must be new users{'\n'}
            • Rewards expire after 90 days if unused{'\n'}
            • FoodieApp reserves the right to modify terms
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  placeholder: {
    width: 40,
  },
  heroSection: {
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 32,
    backgroundColor: '#F8F8F8',
  },
  heroIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 8,
    textAlign: 'center',
  },
  heroSubtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  statsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 24,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    flex: 1,
    minWidth: '22%',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
  codeSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  codeContainer: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
  },
  codeDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  codeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C2C2C',
    letterSpacing: 2,
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF4444',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  copyIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  copyText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  shareSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  shareOptions: {
    flexDirection: 'row',
    gap: 12,
  },
  shareButton: {
    flex: 1,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  shareIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  shareText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666666',
  },
  howItWorksSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  stepsContainer: {
    gap: 20,
  },
  step: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  stepNumberText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  stepDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  recentSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  recentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    color: '#FF4444',
    fontWeight: '500',
  },
  referralsList: {
    gap: 12,
  },
  referralCard: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  referralInfo: {
    flex: 1,
  },
  referralName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  referralDate: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 2,
  },
  referralCompletion: {
    fontSize: 12,
    color: '#34A853',
  },
  referralStatus: {
    alignItems: 'flex-end',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  rewardText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#34A853',
  },
  termsSection: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  termsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 12,
  },
  termsText: {
    fontSize: 12,
    color: '#666666',
    lineHeight: 18,
  },
});