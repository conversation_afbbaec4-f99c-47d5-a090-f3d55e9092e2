import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  FlatList,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import { Animated } from 'react-native';

interface OrderStats {
  totalOrders: number;
  totalSpent: number;
  favoriteRestaurant: string;
  averageOrder: number;
  thisMonth: {
    orders: number;
    spent: number;
  };
}

interface Order {
  id: string;
  orderNumber: string;
  restaurant: {
    id: string;
    name: string;
    logo: string;
    cuisine: string;
    address: string;
    phone: string;
  };
  date: string;
  status: 'delivered' | 'cancelled' | 'refunded' | 'in_progress';
  items: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
    customizations?: string[];
    image?: string;
  }>;
  total: number;
  subtotal: number;
  deliveryFee: number;
  taxes: number;
  tip: number;
  discounts: number;
  paymentMethod: string;
  deliveryAddress: string;
  deliveryTime?: string;
  rating?: number;
  hasReview: boolean;
  canReorder: boolean;
}

type FilterType = 'all' | 'delivered' | 'cancelled' | 'refunded';
type SortType = 'date' | 'amount' | 'restaurant' | 'rating';
type DateRange = 'week' | 'month' | '3months' | 'all';

export default function OrderHistoryScreen() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState<FilterType>('all');
  const [sortBy, setSortBy] = useState<SortType>('date');
  const [dateRange, setDateRange] = useState<DateRange>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [priceRange, setPriceRange] = useState({ min: 0, max: 1000 });

  const [stats] = useState<OrderStats>({
    totalOrders: 47,
    totalSpent: 1247.50,
    favoriteRestaurant: "Mario's Pizza",
    averageOrder: 26.54,
    thisMonth: {
      orders: 8,
      spent: 234.80,
    },
  });

  const [orders] = useState<Order[]>([
    {
      id: '1',
      orderNumber: 'FW123456',
      restaurant: {
        id: '1',
        name: "Mario's Pizza",
        logo: '🍕',
        cuisine: 'Italian',
        address: '123 Main St, Downtown',
        phone: '+****************',
      },
      date: '2024-01-20T14:30:00Z',
      status: 'delivered',
      items: [
        {
          id: '1',
          name: 'Margherita Pizza',
          quantity: 1,
          price: 18.99,
          customizations: ['Extra cheese', 'Thin crust'],
        },
        {
          id: '2',
          name: 'Caesar Salad',
          quantity: 1,
          price: 12.99,
        },
      ],
      total: 36.47,
      subtotal: 31.98,
      deliveryFee: 2.99,
      taxes: 2.56,
      tip: 5.00,
      discounts: 6.06,
      paymentMethod: 'Card ending in 1234',
      deliveryAddress: '456 Oak Ave, Apt 2B',
      deliveryTime: '2024-01-20T15:15:00Z',
      rating: 5,
      hasReview: true,
      canReorder: true,
    },
    // Add more sample orders...
  ]);

  const filteredOrders = useMemo(() => {
    let filtered = orders;

    // Apply status filter
    if (activeFilter !== 'all') {
      filtered = filtered.filter(order => order.status === activeFilter);
    }

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(order =>
        order.restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply date range filter
    const now = new Date();
    const filterDate = new Date();
    switch (dateRange) {
      case 'week':
        filterDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        filterDate.setMonth(now.getMonth() - 1);
        break;
      case '3months':
        filterDate.setMonth(now.getMonth() - 3);
        break;
      default:
        filterDate.setFullYear(2000); // Show all
    }
    filtered = filtered.filter(order => new Date(order.date) >= filterDate);

    // Apply price range filter
    filtered = filtered.filter(order => 
      order.total >= priceRange.min && order.total <= priceRange.max
    );

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.date).getTime() - new Date(a.date).getTime();
        case 'amount':
          return b.total - a.total;
        case 'restaurant':
          return a.restaurant.name.localeCompare(b.restaurant.name);
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        default:
          return 0;
      }
    });

    return filtered;
  }, [orders, activeFilter, searchQuery, dateRange, priceRange, sortBy]);

  const handleOrderPress = (order: Order) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push(`/orders/${order.id}`);
  };

  const handleReorder = (order: Order) => {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    Alert.alert(
      'Reorder Items',
      `Add all items from ${order.restaurant.name} to your cart?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Add to Cart', onPress: () => {
          // Add reorder logic here
          Alert.alert('Success', 'Items added to cart!');
        }},
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered': return '#34A853';
      case 'cancelled': return '#F44336';
      case 'refunded': return '#2196F3';
      case 'in_progress': return '#FF9800';
      default: return '#666666';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'delivered': return '✅';
      case 'cancelled': return '❌';
      case 'refunded': return '↩️';
      case 'in_progress': return '🕐';
      default: return '📦';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderOrderCard = ({ item: order }: { item: Order }) => (
    <TouchableOpacity
      style={styles.orderCard}
      onPress={() => handleOrderPress(order)}
      activeOpacity={0.8}
    >
      <View style={styles.orderHeader}>
        <View style={styles.restaurantInfo}>
          <Text style={styles.restaurantLogo}>{order.restaurant.logo}</Text>
          <View style={styles.restaurantDetails}>
            <Text style={styles.restaurantName}>{order.restaurant.name}</Text>
            <Text style={styles.cuisineType}>{order.restaurant.cuisine}</Text>
          </View>
        </View>
        <View style={styles.orderMeta}>
          <Text style={styles.orderNumber}>#{order.orderNumber}</Text>
          <Text style={styles.orderDate}>{formatDate(order.date)}</Text>
        </View>
      </View>

      <View style={styles.orderContent}>
        <View style={styles.orderSummary}>
          <Text style={styles.itemsCount}>
            {order.items.length} item{order.items.length !== 1 ? 's' : ''}
          </Text>
          <Text style={styles.orderTotal}>${order.total.toFixed(2)}</Text>
        </View>

        <View style={styles.statusContainer}>
          <View style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(order.status) }
          ]}>
            <Text style={styles.statusIcon}>{getStatusIcon(order.status)}</Text>
            <Text style={styles.statusText}>
              {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.orderActions}>
        {order.canReorder && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleReorder(order)}
            activeOpacity={0.7}
          >
            <Text style={styles.actionButtonText}>🔄 Reorder</Text>
          </TouchableOpacity>
        )}
        
        {order.status === 'delivered' && !order.hasReview && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => router.push(`/orders/${order.id}/review`)}
            activeOpacity={0.7}
          >
            <Text style={styles.actionButtonText}>⭐ Rate</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => router.push(`/orders/${order.id}/help`)}
          activeOpacity={0.7}
        >
          <Text style={styles.actionButtonText}>❓ Help</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Order History</Text>
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilters(true)}
          activeOpacity={0.7}
        >
          <Text style={styles.filterIcon}>⚙️</Text>
        </TouchableOpacity>
      </View>

      {/* Quick Stats */}
      <View style={styles.statsContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>{stats.totalOrders}</Text>
            <Text style={styles.statLabel}>Total Orders</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>${stats.totalSpent.toFixed(0)}</Text>
            <Text style={styles.statLabel}>Total Spent</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>{stats.favoriteRestaurant}</Text>
            <Text style={styles.statLabel}>Favorite</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>${stats.averageOrder.toFixed(0)}</Text>
            <Text style={styles.statLabel}>Avg Order</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>{stats.thisMonth.orders}</Text>
            <Text style={styles.statLabel}>This Month</Text>
          </View>
        </ScrollView>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search orders or restaurants..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999999"
        />
        <Text style={styles.searchIcon}>🔍</Text>
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterTabs}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {[
            { key: 'all', label: 'All Orders' },
            { key: 'delivered', label: 'Delivered' },
            { key: 'cancelled', label: 'Cancelled' },
            { key: 'refunded', label: 'Refunded' },
          ].map((filter) => (
            <TouchableOpacity
              key={filter.key}
              style={[
                styles.filterTab,
                activeFilter === filter.key && styles.filterTabActive,
              ]}
              onPress={() => setActiveFilter(filter.key as FilterType)}
              activeOpacity={0.7}
            >
              <Text style={[
                styles.filterTabText,
                activeFilter === filter.key && styles.filterTabTextActive,
              ]}>
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Orders List */}
      <FlatList
        data={filteredOrders}
        renderItem={renderOrderCard}
        keyExtractor={(item) => item.id}
        style={styles.ordersList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.ordersListContent}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Text style={styles.emptyIcon}>📦</Text>
            <Text style={styles.emptyTitle}>No Orders Found</Text>
            <Text style={styles.emptySubtitle}>
              Try adjusting your filters or search terms
            </Text>
          </View>
        }
      />

      {/* Filters Modal */}
      <Modal
        visible={showFilters}
        transparent
        animationType="slide"
        onRequestClose={() => setShowFilters(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter & Sort</Text>
              <TouchableOpacity
                onPress={() => setShowFilters(false)}
                style={styles.modalClose}
              >
                <Text style={styles.modalCloseText}>✕</Text>
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {/* Date Range */}
              <View style={styles.filterSection}>
                <Text style={styles.filterSectionTitle}>Date Range</Text>
                <View style={styles.filterOptions}>
                  {[
                    { key: 'week', label: 'Last Week' },
                    { key: 'month', label: 'Last Month' },
                    { key: '3months', label: 'Last 3 Months' },
                    { key: 'all', label: 'All Time' },
                  ].map((option) => (
                    <TouchableOpacity
                      key={option.key}
                      style={[
                        styles.filterOption,
                        dateRange === option.key && styles.filterOptionActive,
                      ]}
                      onPress={() => setDateRange(option.key as DateRange)}
                    >
                      <Text style={[
                        styles.filterOptionText,
                        dateRange === option.key && styles.filterOptionTextActive,
                      ]}>
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Sort By */}
              <View style={styles.filterSection}>
                <Text style={styles.filterSectionTitle}>Sort By</Text>
                <View style={styles.filterOptions}>
                  {[
                    { key: 'date', label: 'Date' },
                    { key: 'amount', label: 'Amount' },
                    { key: 'restaurant', label: 'Restaurant' },
                    { key: 'rating', label: 'Rating' },
                  ].map((option) => (
                    <TouchableOpacity
                      key={option.key}
                      style={[
                        styles.filterOption,
                        sortBy === option.key && styles.filterOptionActive,
                      ]}
                      onPress={() => setSortBy(option.key as SortType)}
                    >
                      <Text style={[
                        styles.filterOptionText,
                        sortBy === option.key && styles.filterOptionTextActive,
                      ]}>
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </ScrollView>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.modalActionButton}
                onPress={() => {
                  setActiveFilter('all');
                  setDateRange('all');
                  setSortBy('date');
                  setPriceRange({ min: 0, max: 1000 });
                }}
              >
                <Text style={styles.modalActionButtonText}>Reset</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalActionButton, styles.modalActionButtonPrimary]}
                onPress={() => setShowFilters(false)}
              >
                <Text style={[styles.modalActionButtonText, styles.modalActionButtonTextPrimary]}>
                  Apply Filters
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  filterButton: {
    padding: 8,
  },
  filterIcon: {
    fontSize: 20,
  },
  statsContainer: {
    paddingVertical: 16,
  },
  statCard: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginBottom: 16,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    paddingHorizontal: 16,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: '#2C2C2C',
  },
  searchIcon: {
    fontSize: 16,
    marginLeft: 8,
  },
  filterTabs: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F8F8F8',
    marginRight: 8,
  },
  filterTabActive: {
    backgroundColor: '#FF4444',
  },
  filterTabText: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  filterTabTextActive: {
    color: '#FFFFFF',
  },
  ordersList: {
    flex: 1,
  },
  ordersListContent: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  orderCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  restaurantInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  restaurantLogo: {
    fontSize: 32,
    marginRight: 12,
  },
  restaurantDetails: {
    flex: 1,
  },
  restaurantName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  cuisineType: {
    fontSize: 12,
    color: '#666666',
  },
  orderMeta: {
    alignItems: 'flex-end',
  },
  orderNumber: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 2,
  },
  orderDate: {
    fontSize: 12,
    color: '#666666',
  },
  orderContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  orderSummary: {
    flex: 1,
  },
  itemsCount: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  orderTotal: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusIcon: {
    fontSize: 12,
    marginRight: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  orderActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    backgroundColor: '#F8F8F8',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  modalClose: {
    padding: 4,
  },
  modalCloseText: {
    fontSize: 18,
    color: '#666666',
  },
  modalBody: {
    flex: 1,
    padding: 20,
  },
  filterSection: {
    marginBottom: 24,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 12,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F8F8F8',
  },
  filterOptionActive: {
    backgroundColor: '#FF4444',
  },
  filterOptionText: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  filterOptionTextActive: {
    color: '#FFFFFF',
  },
  modalActions: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  modalActionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
  },
  modalActionButtonPrimary: {
    backgroundColor: '#FF4444',
  },
  modalActionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666666',
  },
  modalActionButtonTextPrimary: {
    color: '#FFFFFF',
  },
});