import React, { useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Platform,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface SocialLoginButtonsProps {
  onSocialLogin: (provider: 'google' | 'facebook' | 'apple') => void;
}

export default function SocialLoginButtons({ onSocialLogin }: SocialLoginButtonsProps) {
  return (
    <View style={styles.container}>
      {/* Google Login */}
      <SocialButton
        provider="google"
        icon="🔍"
        label="Continue with Google"
        backgroundColor="#FFFFFF"
        textColor="#2C2C2C"
        borderColor="#E0E0E0"
        onPress={() => onSocialLogin('google')}
      />

      {/* Facebook Login */}
      <SocialButton
        provider="facebook"
        icon="📘"
        label="Continue with Facebook"
        backgroundColor="#1877F2"
        textColor="#FFFFFF"
        onPress={() => onSocialLogin('facebook')}
      />

      {/* Apple Login (iOS only) */}
      {Platform.OS === 'ios' && (
        <SocialButton
          provider="apple"
          icon="🍎"
          label="Continue with Apple"
          backgroundColor="#000000"
          textColor="#FFFFFF"
          onPress={() => onSocialLogin('apple')}
        />
      )}
    </View>
  );
}

interface SocialButtonProps {
  provider: string;
  icon: string;
  label: string;
  backgroundColor: string;
  textColor: string;
  borderColor?: string;
  onPress: () => void;
}

function SocialButton({
  provider,
  icon,
  label,
  backgroundColor,
  textColor,
  borderColor,
  onPress,
}: SocialButtonProps) {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={0.8}
    >
      <Animated.View
        style={[
          styles.socialButton,
          {
            backgroundColor,
            borderColor: borderColor || backgroundColor,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <Text style={styles.socialIcon}>{icon}</Text>
        <Text style={[styles.socialLabel, { color: textColor }]}>{label}</Text>
      </Animated.View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 12,
  },
  socialButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 48,
    borderRadius: 8,
    borderWidth: 1,
    paddingHorizontal: 16,
  },
  socialIcon: {
    fontSize: 18,
    marginRight: 12,
  },
  socialLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
});