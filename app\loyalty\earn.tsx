import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';

interface EarningMethod {
  id: string;
  title: string;
  description: string;
  points: string;
  icon: string;
  category: 'orders' | 'social' | 'engagement' | 'special';
  difficulty: 'easy' | 'medium' | 'hard';
  frequency: 'per_action' | 'daily' | 'weekly' | 'monthly';
  tips: string[];
}

interface BonusEvent {
  id: string;
  title: string;
  description: string;
  multiplier: string;
  validUntil: string;
  icon: string;
  active: boolean;
}

export default function EarnPointsScreen() {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [expandedMethod, setExpandedMethod] = useState<string | null>(null);

  const categories = [
    { id: 'all', name: 'All Methods', icon: '⭐', color: '#FF4444' },
    { id: 'orders', name: 'Orders', icon: '🍽️', color: '#4ECDC4' },
    { id: 'social', name: 'Social', icon: '👥', color: '#45B7D1' },
    { id: 'engagement', name: 'Engagement', icon: '💬', color: '#96CEB4' },
    { id: 'special', name: 'Special', icon: '🎯', color: '#FFEAA7' },
  ];

  const earningMethods: EarningMethod[] = [
    {
      id: '1',
      title: 'Place Orders',
      description: 'Earn points for every dollar spent',
      points: '1-2x per $1',
      icon: '🛒',
      category: 'orders',
      difficulty: 'easy',
      frequency: 'per_action',
      tips: [
        'Higher tier members earn more points per dollar',
        'Weekend orders often have bonus multipliers',
        'Larger orders = more points',
      ],
    },
    {
      id: '2',
      title: 'Write Reviews',
      description: 'Share your dining experience',
      points: '25-50 pts',
      icon: '⭐',
      category: 'engagement',
      difficulty: 'easy',
      frequency: 'per_action',
      tips: [
        'Include photos for bonus points',
        'Write detailed, helpful reviews',
        'Review within 24 hours for extra points',
      ],
    },
    {
      id: '3',
      title: 'Refer Friends',
      description: 'Invite friends to join the app',
      points: '500 pts',
      icon: '👥',
      category: 'social',
      difficulty: 'medium',
      frequency: 'per_action',
      tips: [
        'Share your referral code on social media',
        'Tell friends about your favorite restaurants',
        'Both you and your friend get bonus points',
      ],
    },
    {
      id: '4',
      title: 'Daily Check-in',
      description: 'Open the app daily',
      points: '5-10 pts',
      icon: '📱',
      category: 'engagement',
      difficulty: 'easy',
      frequency: 'daily',
      tips: [
        'Check in every day for streak bonuses',
        'Weekend check-ins give more points',
        'Set daily reminders',
      ],
    },
    {
      id: '5',
      title: 'Complete Challenges',
      description: 'Finish weekly and monthly challenges',
      points: '100-1000 pts',
      icon: '🎯',
      category: 'special',
      difficulty: 'medium',
      frequency: 'weekly',
      tips: [
        'Check challenges section regularly',
        'Focus on easier challenges first',
        'Some challenges have time limits',
      ],
    },
    {
      id: '6',
      title: 'Social Media Sharing',
      description: 'Share your orders on social platforms',
      points: '15-25 pts',
      icon: '📱',
      category: 'social',
      difficulty: 'easy',
      frequency: 'per_action',
      tips: [
        'Use official hashtags for bonus points',
        'Share photos of your food',
        'Tag the restaurant for extra visibility',
      ],
    },
    {
      id: '7',
      title: 'Birthday Bonus',
      description: 'Special points on your birthday month',
      points: '200 pts',
      icon: '🎂',
      category: 'special',
      difficulty: 'easy',
      frequency: 'monthly',
      tips: [
        'Make sure your birthday is set in profile',
        'Order during your birthday month',
        'Higher tiers get bigger birthday bonuses',
      ],
    },
    {
      id: '8',
      title: 'First Order from New Restaurant',
      description: 'Try restaurants you haven\'t ordered from',
      points: '50 pts',
      icon: '🆕',
      category: 'orders',
      difficulty: 'easy',
      frequency: 'per_action',
      tips: [
        'Explore different cuisines',
        'Check out newly added restaurants',
        'Look for restaurants with special offers',
      ],
    },
  ];

  const bonusEvents: BonusEvent[] = [
    {
      id: '1',
      title: 'Weekend Double Points',
      description: 'Earn 2x points on all weekend orders',
      multiplier: '2x',
      validUntil: 'Every weekend',
      icon: '🎉',
      active: true,
    },
    {
      id: '2',
      title: 'Review Week',
      description: 'Extra points for writing reviews',
      multiplier: '3x',
      validUntil: 'Ends in 3 days',
      icon: '📝',
      active: true,
    },
    {
      id: '3',
      title: 'New Year Challenge',
      description: 'Special seasonal challenges available',
      multiplier: 'Bonus',
      validUntil: 'Ends Jan 31',
      icon: '🎊',
      active: true,
    },
  ];

  const filteredMethods = selectedCategory === 'all' 
    ? earningMethods 
    : earningMethods.filter(method => method.category === selectedCategory);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '#34A853';
      case 'medium': return '#FF9800';
      case 'hard': return '#F44336';
      default: return '#34A853';
    }
  };

  const getFrequencyText = (frequency: string) => {
    switch (frequency) {
      case 'per_action': return 'Per action';
      case 'daily': return 'Daily';
      case 'weekly': return 'Weekly';
      case 'monthly': return 'Monthly';
      default: return 'Per action';
    }
  };

  const renderCategoryTabs = () => (
    <ScrollView 
      horizontal 
      showsHorizontalScrollIndicator={false}
      style={styles.categoryTabs}
    >
      {categories.map((category) => (
        <TouchableOpacity
          key={category.id}
          style={[
            styles.categoryTab,
            selectedCategory === category.id && styles.categoryTabActive,
            { borderColor: category.color },
          ]}
          onPress={() => setSelectedCategory(category.id)}
          activeOpacity={0.7}
        >
          <Text style={styles.categoryIcon}>{category.icon}</Text>
          <Text style={[
            styles.categoryName,
            selectedCategory === category.id && styles.categoryNameActive,
          ]}>
            {category.name}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  const renderBonusEvents = () => (
    <View style={styles.bonusSection}>
      <Text style={styles.sectionTitle}>Active Bonus Events</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={styles.bonusEventsList}>
          {bonusEvents.map((event) => (
            <View key={event.id} style={styles.bonusEventCard}>
              <View style={styles.bonusEventHeader}>
                <Text style={styles.bonusEventIcon}>{event.icon}</Text>
                <View style={styles.bonusEventMultiplier}>
                  <Text style={styles.bonusEventMultiplierText}>
                    {event.multiplier}
                  </Text>
                </View>
              </View>
              
              <Text style={styles.bonusEventTitle}>{event.title}</Text>
              <Text style={styles.bonusEventDescription}>
                {event.description}
              </Text>
              
              <Text style={styles.bonusEventValidity}>
                {event.validUntil}
              </Text>
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );

  const renderEarningMethod = (method: EarningMethod) => {
    const isExpanded = expandedMethod === method.id;
    
    return (
      <TouchableOpacity
        key={method.id}
        style={styles.methodCard}
        onPress={() => setExpandedMethod(isExpanded ? null : method.id)}
        activeOpacity={0.8}
      >
        <View style={styles.methodHeader}>
          <View style={styles.methodInfo}>
            <Text style={styles.methodIcon}>{method.icon}</Text>
            <View style={styles.methodDetails}>
              <Text style={styles.methodTitle}>{method.title}</Text>
              <Text style={styles.methodDescription}>{method.description}</Text>
            </View>
          </View>
          
          <View style={styles.methodMeta}>
            <Text style={styles.methodPoints}>{method.points}</Text>
            <View style={styles.methodBadges}>
              <View style={[
                styles.difficultyBadge,
                { backgroundColor: getDifficultyColor(method.difficulty) },
              ]}>
                <Text style={styles.difficultyText}>{method.difficulty}</Text>
              </View>
              <Text style={styles.frequencyText}>
                {getFrequencyText(method.frequency)}
              </Text>
            </View>
          </View>
        </View>
        
        {isExpanded && (
          <View style={styles.methodExpanded}>
            <Text style={styles.tipsTitle}>💡 Tips to maximize points:</Text>
            {method.tips.map((tip, index) => (
              <Text key={index} style={styles.tipItem}>
                • {tip}
              </Text>
            ))}
          </View>
        )}
        
        <View style={styles.expandIndicator}>
          <Text style={styles.expandIcon}>
            {isExpanded ? '▲' : '▼'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderPointsCalculator = () => (
    <View style={styles.calculatorSection}>
      <Text style={styles.sectionTitle}>Points Calculator</Text>
      <View style={styles.calculatorCard}>
        <Text style={styles.calculatorTitle}>Estimate your monthly earnings</Text>
        
        <View style={styles.calculatorRow}>
          <Text style={styles.calculatorLabel}>Orders per month:</Text>
          <Text style={styles.calculatorValue}>12 orders</Text>
        </View>
        
        <View style={styles.calculatorRow}>
          <Text style={styles.calculatorLabel}>Average order value:</Text>
          <Text style={styles.calculatorValue}>$25</Text>
        </View>
        
        <View style={styles.calculatorRow}>
          <Text style={styles.calculatorLabel}>Your tier multiplier:</Text>
          <Text style={styles.calculatorValue}>1.5x (Silver)</Text>
        </View>
        
        <View style={styles.calculatorDivider} />
        
        <View style={styles.calculatorResult}>
          <Text style={styles.calculatorResultLabel}>Estimated monthly points:</Text>
          <Text style={styles.calculatorResultValue}>450 points</Text>
        </View>
        
        <Text style={styles.calculatorNote}>
          Plus bonus points from reviews, challenges, and special events!
        </Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>How to Earn Points</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Bonus Events */}
        {renderBonusEvents()}
        
        {/* Category Tabs */}
        {renderCategoryTabs()}
        
        {/* Earning Methods */}
        <View style={styles.methodsSection}>
          <Text style={styles.sectionTitle}>
            {selectedCategory === 'all' ? 'All Earning Methods' : 
             categories.find(c => c.id === selectedCategory)?.name + ' Methods'}
          </Text>
          
          <View style={styles.methodsList}>
            {filteredMethods.map(renderEarningMethod)}
          </View>
        </View>
        
        {/* Points Calculator */}
        {renderPointsCalculator()}
        
        {/* Quick Tips */}
        <View style={styles.tipsSection}>
          <Text style={styles.sectionTitle}>Quick Tips</Text>
          <View style={styles.quickTipsList}>
            <View style={styles.quickTip}>
              <Text style={styles.quickTipIcon}>🎯</Text>
              <Text style={styles.quickTipText}>
                Focus on daily activities like check-ins and reviews for consistent points
              </Text>
            </View>
            
            <View style={styles.quickTip}>
              <Text style={styles.quickTipIcon}>📈</Text>
              <Text style={styles.quickTipText}>
                Upgrade your tier to earn more points per dollar spent
              </Text>
            </View>
            
            <View style={styles.quickTip}>
              <Text style={styles.quickTipIcon}>⏰</Text>
              <Text style={styles.quickTipText}>
                Take advantage of weekend bonuses and special events
              </Text>
            </View>
            
            <View style={styles.quickTip}>
              <Text style={styles.quickTipIcon}>👥</Text>
              <Text style={styles.quickTipText}>
                Refer friends for the biggest point bonuses
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  bonusSection: {
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  bonusEventsList: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    gap: 12,
  },
  bonusEventCard: {
    width: 180,
    backgroundColor: '#FFF3E0',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#FFE0B2',
  },
  bonusEventHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  bonusEventIcon: {
    fontSize: 20,
  },
  bonusEventMultiplier: {
    backgroundColor: '#FF9800',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  bonusEventMultiplierText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  bonusEventTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#E65100',
    marginBottom: 4,
  },
  bonusEventDescription: {
    fontSize: 11,
    color: '#F57C00',
    marginBottom: 8,
    lineHeight: 14,
  },
  bonusEventValidity: {
    fontSize: 10,
    color: '#FF9800',
    fontWeight: '500',
  },
  categoryTabs: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  categoryTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    backgroundColor: '#FFFFFF',
  },
  categoryTabActive: {
    backgroundColor: '#F8F8F8',
  },
  categoryIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  categoryName: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
  },
  categoryNameActive: {
    color: '#2C2C2C',
    fontWeight: '600',
  },
  methodsSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  methodsList: {
    gap: 12,
  },
  methodCard: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    position: 'relative',
  },
  methodHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  methodInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  methodIcon: {
    fontSize: 20,
    marginRight: 12,
    marginTop: 2,
  },
  methodDetails: {
    flex: 1,
  },
  methodTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  methodDescription: {
    fontSize: 12,
    color: '#666666',
    lineHeight: 16,
  },
  methodMeta: {
    alignItems: 'flex-end',
    marginLeft: 12,
  },
  methodPoints: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FF4444',
    marginBottom: 6,
  },
  methodBadges: {
    alignItems: 'flex-end',
    gap: 4,
  },
  difficultyBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  difficultyText: {
    fontSize: 9,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  frequencyText: {
    fontSize: 9,
    color: '#666666',
    fontWeight: '500',
  },
  methodExpanded: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  tipsTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  tipItem: {
    fontSize: 11,
    color: '#666666',
    lineHeight: 16,
    marginBottom: 4,
  },
  expandIndicator: {
    position: 'absolute',
    bottom: 8,
    right: 16,
  },
  expandIcon: {
    fontSize: 10,
    color: '#666666',
  },
  calculatorSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  calculatorCard: {
    backgroundColor: '#E3F2FD',
    borderRadius: 12,
    padding: 16,
  },
  calculatorTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1976D2',
    marginBottom: 12,
  },
  calculatorRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  calculatorLabel: {
    fontSize: 12,
    color: '#1976D2',
  },
  calculatorValue: {
    fontSize: 12,
    fontWeight: '500',
    color: '#1976D2',
  },
  calculatorDivider: {
    height: 1,
    backgroundColor: '#BBDEFB',
    marginVertical: 12,
  },
  calculatorResult: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  calculatorResultLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1976D2',
  },
  calculatorResultValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1976D2',
  },
  calculatorNote: {
    fontSize: 10,
    color: '#1976D2',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  tipsSection: {
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  quickTipsList: {
    gap: 12,
  },
  quickTip: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 12,
  },
  quickTipIcon: {
    fontSize: 16,
    marginRight: 12,
    marginTop: 2,
  },
  quickTipText: {
    fontSize: 12,
    color: '#2C2C2C',
    flex: 1,
    lineHeight: 16,
  },
});