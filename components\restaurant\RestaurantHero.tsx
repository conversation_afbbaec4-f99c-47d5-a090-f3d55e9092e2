import React, { useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';

const { width } = Dimensions.get('window');
const HERO_HEIGHT = 200;

interface Restaurant {
  id: string;
  name: string;
  cuisine: string;
  rating: number;
  deliveryTime: string;
  deliveryFee: number;
  image: string;
  isOpen: boolean;
}

interface RestaurantHeroProps {
  restaurant: Restaurant;
  scrollY: Animated.Value;
  onBack: () => void;
  onShare: () => void;
  onFavorite: () => void;
}

export default function RestaurantHero({
  restaurant,
  scrollY,
  onBack,
  onShare,
  onFavorite,
}: RestaurantHeroProps) {
  const favoriteScale = useRef(new Animated.Value(1)).current;

  const handleFavoritePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    // Animate heart
    Animated.sequence([
      Animated.timing(favoriteScale, {
        toValue: 1.3,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(favoriteScale, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
    
    onFavorite();
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Text key={i} style={styles.star}>★</Text>);
    }
    
    if (hasHalfStar) {
      stars.push(<Text key="half" style={styles.star}>☆</Text>);
    }

    return stars;
  };

  // Parallax effect
  const imageTranslateY = scrollY.interpolate({
    inputRange: [0, HERO_HEIGHT],
    outputRange: [0, -HERO_HEIGHT * 0.3],
    extrapolate: 'clamp',
  });

  const overlayOpacity = scrollY.interpolate({
    inputRange: [0, HERO_HEIGHT * 0.5],
    outputRange: [0, 0.3],
    extrapolate: 'clamp',
  });

  return (
    <View style={styles.container}>
      {/* Background Image with Parallax */}
      <Animated.View
        style={[
          styles.imageContainer,
          {
            transform: [{ translateY: imageTranslateY }],
          },
        ]}
      >
        <Image
          source={{ uri: restaurant.image }}
          style={styles.backgroundImage}
          contentFit="cover"
        />
        
        {/* Gradient Overlay */}
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.7)']}
          style={styles.gradient}
        />
        
        {/* Scroll Overlay */}
        <Animated.View
          style={[
            styles.scrollOverlay,
            { opacity: overlayOpacity },
          ]}
        />
      </Animated.View>

      {/* Header Controls */}
      <View style={styles.headerControls}>
        <TouchableOpacity
          style={styles.controlButton}
          onPress={onBack}
          activeOpacity={0.7}
        >
          <Text style={styles.controlIcon}>←</Text>
        </TouchableOpacity>
        
        <View style={styles.rightControls}>
          <TouchableOpacity
            style={styles.controlButton}
            onPress={onShare}
            activeOpacity={0.7}
          >
            <Text style={styles.controlIcon}>↗</Text>
          </TouchableOpacity>
          
          <Animated.View style={{ transform: [{ scale: favoriteScale }] }}>
            <TouchableOpacity
              style={styles.controlButton}
              onPress={handleFavoritePress}
              activeOpacity={0.7}
            >
              <Text style={styles.favoriteIcon}>♡</Text>
            </TouchableOpacity>
          </Animated.View>
        </View>
      </View>

      {/* Restaurant Info Overlay */}
      <View style={styles.infoOverlay}>
        <Text style={styles.restaurantName}>{restaurant.name}</Text>
        <Text style={styles.cuisineType}>{restaurant.cuisine}</Text>
        
        <View style={styles.ratingContainer}>
          <View style={styles.stars}>
            {renderStars(restaurant.rating)}
          </View>
          <Text style={styles.ratingText}>{restaurant.rating}</Text>
        </View>
        
        <View style={styles.deliveryInfo}>
          <Text style={styles.deliveryText}>
            {restaurant.deliveryTime} • ${restaurant.deliveryFee} delivery
          </Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: HERO_HEIGHT,
    zIndex: 10,
  },
  imageContainer: {
    ...StyleSheet.absoluteFillObject,
    height: HERO_HEIGHT + 50, // Extra height for parallax
  },
  backgroundImage: {
    width: '100%',
    height: '100%',
  },
  gradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: HERO_HEIGHT * 0.6,
  },
  scrollOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#000000',
  },
  headerControls: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  rightControls: {
    flexDirection: 'row',
  },
  controlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  controlIcon: {
    fontSize: 18,
    color: '#2C2C2C',
    fontWeight: 'bold',
  },
  favoriteIcon: {
    fontSize: 18,
    color: '#FF4444',
  },
  infoOverlay: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    right: 16,
  },
  restaurantName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
    marginBottom: 4,
  },
  cuisineType: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  stars: {
    flexDirection: 'row',
    marginRight: 6,
  },
  star: {
    fontSize: 14,
    color: '#FFD700',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  ratingText: {
    fontSize: 14,
    color: '#FFD700',
    fontWeight: '600',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  deliveryInfo: {
    marginTop: 4,
  },
  deliveryText: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});