import React from 'react';
import { View, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import OnboardingCarousel from '@/components/OnboardingCarousel';

export default function OnboardingScreen() {
  const handleOnboardingComplete = () => {
    // Navigate to main app or authentication
    router.replace('/(tabs)');
  };

  return (
    <View style={styles.container}>
      <OnboardingCarousel onComplete={handleOnboardingComplete} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
});