import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Animated,
  ActivityIndicator,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';

interface Restaurant {
  id: string;
  name: string;
  cuisine: string;
  rating: number;
  deliveryTime: string;
  deliveryFee: number;
  image: string;
  isOpen: boolean;
  distance: number;
  categories: string[];
}

interface RestaurantListProps {
  searchQuery: string;
  selectedCategory: string | null;
  userLocation: any;
}

export default function RestaurantList({ searchQuery, selectedCategory, userLocation }: RestaurantListProps) {
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    loadRestaurants(true);
  }, [searchQuery, selectedCategory]);

  const loadRestaurants = async (reset = false) => {
    if (loading && !reset) return;
    
    setLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockRestaurants: Restaurant[] = generateMockRestaurants(reset ? 1 : page);
      
      if (reset) {
        setRestaurants(mockRestaurants);
        setPage(2);
      } else {
        setRestaurants(prev => [...prev, ...mockRestaurants]);
        setPage(prev => prev + 1);
      }
      
      setHasMore(mockRestaurants.length === 10);
    } catch (error) {
      console.error('Error loading restaurants:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateMockRestaurants = (pageNum: number): Restaurant[] => {
    const baseRestaurants = [
      {
        name: 'Mario\'s Pizza Palace',
        cuisine: 'Italian',
        rating: 4.5,
        deliveryTime: '25-35 min',
        deliveryFee: 2.99,
        image: 'https://images.unsplash.com/photo-1513104890138-7c749659a591?w=400',
        categories: ['Pizza', 'Italian'],
      },
      {
        name: 'Burger Junction',
        cuisine: 'American',
        rating: 4.2,
        deliveryTime: '20-30 min',
        deliveryFee: 1.99,
        image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400',
        categories: ['Burgers', 'American'],
      },
      {
        name: 'Dragon Palace',
        cuisine: 'Chinese',
        rating: 4.7,
        deliveryTime: '30-40 min',
        deliveryFee: 3.49,
        image: 'https://images.unsplash.com/photo-1559847844-d721426d6edc?w=400',
        categories: ['Asian', 'Chinese'],
      },
      {
        name: 'Sweet Dreams Bakery',
        cuisine: 'Desserts',
        rating: 4.8,
        deliveryTime: '15-25 min',
        deliveryFee: 0,
        image: 'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400',
        categories: ['Desserts', 'Bakery'],
      },
    ];

    return baseRestaurants.map((restaurant, index) => ({
      ...restaurant,
      id: `${pageNum}-${index}`,
      isOpen: Math.random() > 0.1,
      distance: Math.round((Math.random() * 5 + 0.5) * 10) / 10,
    })).filter(restaurant => {
      // Filter by search query
      if (searchQuery && !restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !restaurant.cuisine.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }
      
      // Filter by category
      if (selectedCategory && !restaurant.categories.includes(selectedCategory)) {
        return false;
      }
      
      return true;
    });
  };

  const handleRestaurantPress = (restaurant: Restaurant) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.push(`/restaurant/${restaurant.id}`);
  };

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      loadRestaurants();
    }
  };

  const renderRestaurant = ({ item, index }: { item: Restaurant; index: number }) => (
    <RestaurantCard
      restaurant={item}
      onPress={() => handleRestaurantPress(item)}
      index={index}
    />
  );

  const renderFooter = () => {
    if (!loading) return null;
    
    return (
      <View style={styles.loadingFooter}>
        <ActivityIndicator size="small" color="#FF4444" />
        <Text style={styles.loadingText}>Loading more restaurants...</Text>
      </View>
    );
  };

  const renderEmpty = () => {
    if (loading) return null;
    
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyIcon}>🍽️</Text>
        <Text style={styles.emptyTitle}>No restaurants found</Text>
        <Text style={styles.emptySubtitle}>
          {searchQuery || selectedCategory 
            ? 'Try adjusting your search or filters'
            : 'Check your internet connection and try again'
          }
        </Text>
      </View>
    );
  };

  return (
    <FlatList
      data={restaurants}
      renderItem={renderRestaurant}
      keyExtractor={(item) => item.id}
      showsVerticalScrollIndicator={false}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.5}
      ListFooterComponent={renderFooter}
      ListEmptyComponent={renderEmpty}
      contentContainerStyle={styles.listContainer}
      scrollEnabled={false} // Parent ScrollView handles scrolling
    />
  );
}

interface RestaurantCardProps {
  restaurant: Restaurant;
  onPress: () => void;
  index: number;
}

function RestaurantCard({ restaurant, onPress, index }: RestaurantCardProps) {
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Staggered entrance animation
    Animated.sequence([
      Animated.delay(index * 100),
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          friction: 8,
          tension: 100,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, []);

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={1}
      style={styles.cardContainer}
    >
      <Animated.View
        style={[
          styles.restaurantCard,
          !restaurant.isOpen && styles.closedCard,
          {
            transform: [{ scale: scaleAnim }],
            opacity: fadeAnim,
          },
        ]}
      >
        {/* Restaurant Image */}
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: restaurant.image }}
            style={styles.restaurantImage}
            resizeMode="cover"
          />
          
          {/* Delivery Time Badge */}
          <View style={styles.deliveryBadge}>
            <Text style={styles.deliveryTime}>{restaurant.deliveryTime}</Text>
          </View>
          
          {/* Closed Overlay */}
          {!restaurant.isOpen && (
            <View style={styles.closedOverlay}>
              <Text style={styles.closedText}>Closed</Text>
            </View>
          )}
        </View>

        {/* Restaurant Info */}
        <View style={styles.infoContainer}>
          <Text style={styles.restaurantName} numberOfLines={1}>
            {restaurant.name}
          </Text>
          
          <Text style={styles.cuisine}>{restaurant.cuisine}</Text>
          
          <View style={styles.detailsRow}>
            <View style={styles.ratingContainer}>
              <Text style={styles.starIcon}>⭐</Text>
              <Text style={styles.rating}>{restaurant.rating}</Text>
            </View>
            
            <Text style={styles.separator}>•</Text>
            
            <Text style={styles.distance}>{restaurant.distance} km</Text>
            
            <Text style={styles.separator}>•</Text>
            
            <Text style={styles.deliveryFee}>
              {restaurant.deliveryFee === 0 ? 'Free delivery' : `$${restaurant.deliveryFee} delivery`}
            </Text>
          </View>
        </View>
      </Animated.View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  listContainer: {
    paddingHorizontal: 16,
  },
  cardContainer: {
    marginBottom: 16,
  },
  restaurantCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    overflow: 'hidden',
  },
  closedCard: {
    opacity: 0.7,
  },
  imageContainer: {
    position: 'relative',
    height: 160,
  },
  restaurantImage: {
    width: '100%',
    height: '100%',
  },
  deliveryBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  deliveryTime: {
    fontSize: 12,
    fontWeight: '600',
    color: '#34A853',
  },
  closedOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closedText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  infoContainer: {
    padding: 16,
  },
  restaurantName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  cuisine: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  detailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  starIcon: {
    fontSize: 12,
    marginRight: 2,
  },
  rating: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FBBC04',
  },
  separator: {
    fontSize: 14,
    color: '#CCCCCC',
    marginHorizontal: 8,
  },
  distance: {
    fontSize: 14,
    color: '#666666',
  },
  deliveryFee: {
    fontSize: 14,
    color: '#666666',
  },
  loadingFooter: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666666',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    paddingHorizontal: 32,
  },
});