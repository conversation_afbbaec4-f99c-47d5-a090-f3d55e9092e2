import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

import ProfileHeader from '@/components/profile/ProfileHeader';
import QuickActions from '@/components/profile/QuickActions';
import SettingsMenu from '@/components/profile/SettingsMenu';
import { useThemeColor } from '@/hooks/useThemeColor';

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  phone: string;
  profilePhoto?: string;
  memberSince: Date;
  isPhoneVerified: boolean;
  stats: {
    ordersCompleted: number;
    favoriteRestaurants: number;
    reviewsWritten: number;
    pointsEarned: number;
  };
}

export interface QuickAction {
  id: string;
  title: string;
  subtitle: string;
  icon: string;
  color: string;
  badge?: number;
  route: string;
}

export default function ProfileScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  
  const [userProfile] = useState<UserProfile>({
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    profilePhoto: '👤',
    memberSince: new Date('2023-01-15'),
    isPhoneVerified: true,
    stats: {
      ordersCompleted: 47,
      favoriteRestaurants: 12,
      reviewsWritten: 23,
      pointsEarned: 1250,
    },
  });

  const quickActions: QuickAction[] = [
    {
      id: 'order-history',
      title: 'Order History',
      subtitle: 'View past orders',
      icon: '🕒',
      color: '#4285F4',
      badge: 3,
      route: '/profile/order-history',
    },
    {
      id: 'favorites',
      title: 'Favorites',
      subtitle: 'Saved restaurants',
      icon: '❤️',
      color: '#FF4444',
      badge: userProfile.stats.favoriteRestaurants,
      route: '/profile/favorites',
    },
    {
      id: 'addresses',
      title: 'Addresses',
      subtitle: 'Manage addresses',
      icon: '📍',
      color: '#34A853',
      badge: 2,
      route: '/profile/addresses',
    },
    {
      id: 'payment',
      title: 'Payment Methods',
      subtitle: 'Cards & wallets',
      icon: '💳',
      color: '#FBBC04',
      badge: 3,
      route: '/profile/payment-methods',
    },
  ];

  const handleEditProfile = () => {
    router.push('/profile/edit');
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleQuickAction = (action: QuickAction) => {
    router.push(action.route as any);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleSettingsItem = (route: string) => {
    router.push(route as any);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: () => {
            // Handle logout logic
            router.replace('/auth/login');
          },
        },
      ]
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Profile Header */}
        <ProfileHeader
          userProfile={userProfile}
          onEditProfile={handleEditProfile}
        />

        {/* Quick Actions */}
        <QuickActions
          actions={quickActions}
          onActionPress={handleQuickAction}
        />

        {/* Settings Menu */}
        <SettingsMenu
          onSettingsPress={handleSettingsItem}
          onLogout={handleLogout}
        />

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  bottomSpacing: {
    height: 40,
  },
});