import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';

const { width } = Dimensions.get('window');

interface UserTier {
  level: 'Bronze' | 'Silver' | 'Gold';
  points: number;
  nextTierPoints: number;
  benefits: string[];
  multiplier: number;
}

interface QuickAction {
  title: string;
  subtitle: string;
  icon: string;
  route: string;
  color: string;
}

interface Challenge {
  id: string;
  title: string;
  description: string;
  progress: number;
  target: number;
  reward: number;
  timeLeft: string;
  type: 'order' | 'social' | 'seasonal';
}

export default function LoyaltyDashboard() {
  const router = useRouter();
  const [pointsAnimation] = useState(new Animated.Value(0));
  const [progressAnimation] = useState(new Animated.Value(0));

  const userTier: UserTier = {
    level: 'Silver',
    points: 1250,
    nextTierPoints: 1500,
    benefits: [
      '1.5x points per dollar',
      'Free delivery on $25+',
      'Priority support',
      'Exclusive Silver offers',
    ],
    multiplier: 1.5,
  };

  const quickActions: QuickAction[] = [
    {
      title: 'Redeem Points',
      subtitle: 'Browse rewards catalog',
      icon: '🎁',
      route: '/loyalty/rewards',
      color: '#FF4444',
    },
    {
      title: 'Earn More',
      subtitle: 'Ways to get points',
      icon: '⭐',
      route: '/loyalty/earn',
      color: '#4ECDC4',
    },
    {
      title: 'Tier Benefits',
      subtitle: 'View your perks',
      icon: '👑',
      route: '/loyalty/tiers',
      color: '#45B7D1',
    },
    {
      title: 'Points History',
      subtitle: 'Transaction log',
      icon: '📊',
      route: '/loyalty/history',
      color: '#96CEB4',
    },
  ];

  const activeChallenges: Challenge[] = [
    {
      id: '1',
      title: 'Weekend Warrior',
      description: 'Order 3 times this weekend',
      progress: 1,
      target: 3,
      reward: 500,
      timeLeft: '2 days',
      type: 'order',
    },
    {
      id: '2',
      title: 'Review Master',
      description: 'Write 5 reviews this month',
      progress: 3,
      target: 5,
      reward: 250,
      timeLeft: '12 days',
      type: 'social',
    },
    {
      id: '3',
      title: 'Explorer',
      description: 'Try 2 new restaurants',
      progress: 0,
      target: 2,
      reward: 300,
      timeLeft: '7 days',
      type: 'order',
    },
  ];

  const recentActivity = [
    { type: 'earned', amount: 45, description: 'Order from Mario\'s Pizza', time: '2 hours ago' },
    { type: 'earned', amount: 25, description: 'Review bonus', time: '1 day ago' },
    { type: 'redeemed', amount: -500, description: '$5 off voucher', time: '3 days ago' },
    { type: 'earned', amount: 67, description: 'Order from Dragon Palace', time: '5 days ago' },
  ];

  useEffect(() => {
    // Animate points counter
    Animated.timing(pointsAnimation, {
      toValue: userTier.points,
      duration: 2000,
      useNativeDriver: false,
    }).start();

    // Animate progress bar
    const progressPercentage = (userTier.points / userTier.nextTierPoints) * 100;
    Animated.timing(progressAnimation, {
      toValue: progressPercentage,
      duration: 1500,
      useNativeDriver: false,
    }).start();
  }, []);

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'Bronze': return '#CD7F32';
      case 'Silver': return '#C0C0C0';
      case 'Gold': return '#FFD700';
      default: return '#CD7F32';
    }
  };

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'Bronze': return '🥉';
      case 'Silver': return '🥈';
      case 'Gold': return '🥇';
      default: return '🥉';
    }
  };

  const renderPointsHeader = () => (
    <View style={styles.pointsHeader}>
      <View style={styles.pointsBalance}>
        <Animated.Text style={styles.pointsValue}>
          {pointsAnimation.interpolate({
            inputRange: [0, userTier.points],
            outputRange: ['0', userTier.points.toString()],
            extrapolate: 'clamp',
          })}
        </Animated.Text>
        <Text style={styles.pointsLabel}>Points</Text>
        <Text style={styles.pointsValue}>= ${(userTier.points * 0.01).toFixed(2)} in rewards</Text>
      </View>
      
      <View style={styles.tierStatus}>
        <View style={styles.tierBadge}>
          <Text style={styles.tierIcon}>{getTierIcon(userTier.level)}</Text>
          <Text style={[styles.tierLevel, { color: getTierColor(userTier.level) }]}>
            {userTier.level}
          </Text>
        </View>
        
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <Animated.View
              style={[
                styles.progressFill,
                {
                  width: progressAnimation.interpolate({
                    inputRange: [0, 100],
                    outputRange: ['0%', '100%'],
                    extrapolate: 'clamp',
                  }),
                  backgroundColor: getTierColor(userTier.level),
                },
              ]}
            />
          </View>
          <Text style={styles.progressText}>
            {userTier.nextTierPoints - userTier.points} points to Gold
          </Text>
        </View>
        
        <Text style={styles.tierBenefit}>
          Unlock free delivery on all orders at Gold
        </Text>
      </View>
    </View>
  );

  const renderQuickActions = () => (
    <View style={styles.quickActionsSection}>
      <Text style={styles.sectionTitle}>Quick Actions</Text>
      <View style={styles.quickActionsGrid}>
        {quickActions.map((action, index) => (
          <TouchableOpacity
            key={index}
            style={[styles.quickActionCard, { borderLeftColor: action.color }]}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              router.push(action.route as any);
            }}
            activeOpacity={0.8}
          >
            <Text style={styles.quickActionIcon}>{action.icon}</Text>
            <View style={styles.quickActionInfo}>
              <Text style={styles.quickActionTitle}>{action.title}</Text>
              <Text style={styles.quickActionSubtitle}>{action.subtitle}</Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderActiveChallenges = () => (
    <View style={styles.challengesSection}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Active Challenges</Text>
        <TouchableOpacity
          onPress={() => router.push('/loyalty/challenges')}
          activeOpacity={0.7}
        >
          <Text style={styles.seeAllText}>See All</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={styles.challengesList}>
          {activeChallenges.map((challenge) => (
            <View key={challenge.id} style={styles.challengeCard}>
              <View style={styles.challengeHeader}>
                <Text style={styles.challengeTitle}>{challenge.title}</Text>
                <Text style={styles.challengeReward}>+{challenge.reward} pts</Text>
              </View>
              
              <Text style={styles.challengeDescription}>
                {challenge.description}
              </Text>
              
              <View style={styles.challengeProgress}>
                <View style={styles.challengeProgressBar}>
                  <View
                    style={[
                      styles.challengeProgressFill,
                      { width: `${(challenge.progress / challenge.target) * 100}%` },
                    ]}
                  />
                </View>
                <Text style={styles.challengeProgressText}>
                  {challenge.progress}/{challenge.target}
                </Text>
              </View>
              
              <Text style={styles.challengeTimeLeft}>
                ⏰ {challenge.timeLeft} left
              </Text>
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );

  const renderRecentActivity = () => (
    <View style={styles.activitySection}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Recent Activity</Text>
        <TouchableOpacity
          onPress={() => router.push('/loyalty/history')}
          activeOpacity={0.7}
        >
          <Text style={styles.seeAllText}>View All</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.activityList}>
        {recentActivity.slice(0, 4).map((activity, index) => (
          <View key={index} style={styles.activityItem}>
            <View style={[
              styles.activityIcon,
              { backgroundColor: activity.type === 'earned' ? '#E8F5E8' : '#FFE5E5' },
            ]}>
              <Text style={styles.activityIconText}>
                {activity.type === 'earned' ? '+' : '-'}
              </Text>
            </View>
            
            <View style={styles.activityInfo}>
              <Text style={styles.activityDescription}>
                {activity.description}
              </Text>
              <Text style={styles.activityTime}>{activity.time}</Text>
            </View>
            
            <Text style={[
              styles.activityAmount,
              {
                color: activity.type === 'earned' ? '#34A853' : '#F44336',
              },
            ]}>
              {activity.type === 'earned' ? '+' : ''}{activity.amount} pts
            </Text>
          </View>
        ))}
      </View>
    </View>
  );

  const renderTierBenefits = () => (
    <View style={styles.benefitsSection}>
      <Text style={styles.sectionTitle}>Your {userTier.level} Benefits</Text>
      <View style={styles.benefitsList}>
        {userTier.benefits.map((benefit, index) => (
          <View key={index} style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>✓</Text>
            <Text style={styles.benefitText}>{benefit}</Text>
          </View>
        ))}
      </View>
      
      <TouchableOpacity
        style={styles.upgradeButton}
        onPress={() => router.push('/loyalty/tiers')}
        activeOpacity={0.8}
      >
        <Text style={styles.upgradeButtonText}>
          Upgrade to Gold for More Benefits
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Loyalty & Rewards</Text>
        <TouchableOpacity
          style={styles.settingsButton}
          onPress={() => router.push('/loyalty/settings')}
          activeOpacity={0.7}
        >
          <Text style={styles.settingsIcon}>⚙️</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderPointsHeader()}
        {renderQuickActions()}
        {renderActiveChallenges()}
        {renderTierBenefits()}
        {renderRecentActivity()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  settingsButton: {
    padding: 8,
  },
  settingsIcon: {
    fontSize: 20,
  },
  scrollView: {
    flex: 1,
  },
  pointsHeader: {
    backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    paddingHorizontal: 20,
    paddingVertical: 24,
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 16,
    backgroundColor: '#667eea',
  },
  pointsBalance: {
    alignItems: 'center',
    marginBottom: 20,
  },
  pointsValue: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  pointsLabel: {
    fontSize: 16,
    color: '#E0E0E0',
    marginBottom: 8,
  },
  tierStatus: {
    alignItems: 'center',
  },
  tierBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  tierIcon: {
    fontSize: 24,
    marginRight: 8,
  },
  tierLevel: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: '#E0E0E0',
  },
  tierBenefit: {
    fontSize: 12,
    color: '#E0E0E0',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  quickActionsSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  quickActionsGrid: {
    gap: 12,
  },
  quickActionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
  },
  quickActionIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  quickActionInfo: {
    flex: 1,
  },
  quickActionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  quickActionSubtitle: {
    fontSize: 12,
    color: '#666666',
  },
  challengesSection: {
    paddingBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  seeAllText: {
    fontSize: 14,
    color: '#FF4444',
    fontWeight: '500',
  },
  challengesList: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    gap: 12,
  },
  challengeCard: {
    width: 200,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
  },
  challengeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  challengeTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    flex: 1,
  },
  challengeReward: {
    fontSize: 12,
    fontWeight: '500',
    color: '#FF4444',
    backgroundColor: '#FFE5E5',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  challengeDescription: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 12,
    lineHeight: 16,
  },
  challengeProgress: {
    marginBottom: 8,
  },
  challengeProgressBar: {
    width: '100%',
    height: 6,
    backgroundColor: '#E0E0E0',
    borderRadius: 3,
    marginBottom: 4,
  },
  challengeProgressFill: {
    height: '100%',
    backgroundColor: '#FF4444',
    borderRadius: 3,
  },
  challengeProgressText: {
    fontSize: 10,
    color: '#666666',
    textAlign: 'right',
  },
  challengeTimeLeft: {
    fontSize: 10,
    color: '#666666',
  },
  benefitsSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  benefitsList: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  benefitIcon: {
    fontSize: 16,
    color: '#34A853',
    marginRight: 12,
  },
  benefitText: {
    fontSize: 14,
    color: '#2C2C2C',
    flex: 1,
  },
  upgradeButton: {
    backgroundColor: '#FFD700',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  upgradeButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  activitySection: {
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  activityList: {
    gap: 12,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  activityIconText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  activityInfo: {
    flex: 1,
  },
  activityDescription: {
    fontSize: 14,
    color: '#2C2C2C',
    marginBottom: 2,
  },
  activityTime: {
    fontSize: 12,
    color: '#666666',
  },
  activityAmount: {
    fontSize: 14,
    fontWeight: '600',
  },
});