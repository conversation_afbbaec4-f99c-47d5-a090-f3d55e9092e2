import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

interface NotificationSetting {
  id: string;
  title: string;
  description: string;
  enabled: boolean;
  category: 'orders' | 'promotions' | 'app' | 'delivery';
}

export default function NotificationSettingsScreen() {
  const [settings, setSettings] = useState<NotificationSetting[]>([
    {
      id: 'order_updates',
      title: 'Order Updates',
      description: 'Status changes, delivery notifications',
      enabled: true,
      category: 'orders',
    },
    {
      id: 'order_reminders',
      title: 'Order Reminders',
      description: 'Reminders about pending orders',
      enabled: true,
      category: 'orders',
    },
    {
      id: 'promotions',
      title: 'Promotions & Offers',
      description: 'Discounts, deals, and special offers',
      enabled: true,
      category: 'promotions',
    },
    {
      id: 'restaurant_news',
      title: 'Restaurant News',
      description: 'New menus, restaurant updates',
      enabled: false,
      category: 'promotions',
    },
    {
      id: 'cashback',
      title: 'Cashback & Rewards',
      description: 'Loyalty points and cashback notifications',
      enabled: true,
      category: 'promotions',
    },
    {
      id: 'app_updates',
      title: 'App Updates',
      description: 'New features and improvements',
      enabled: true,
      category: 'app',
    },
    {
      id: 'maintenance',
      title: 'Maintenance Alerts',
      description: 'Service interruptions and maintenance',
      enabled: true,
      category: 'app',
    },
    {
      id: 'marketing',
      title: 'Marketing Communications',
      description: 'Newsletters, surveys, and tips',
      enabled: false,
      category: 'app',
    },
  ]);

  const [deliveryMethods, setDeliveryMethods] = useState({
    push: true,
    sms: true,
    email: false,
  });

  const [frequency, setFrequency] = useState<'immediate' | 'daily' | 'weekly'>('immediate');

  const toggleSetting = (id: string) => {
    setSettings(prev => 
      prev.map(setting => 
        setting.id === id 
          ? { ...setting, enabled: !setting.enabled }
          : setting
      )
    );
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const toggleDeliveryMethod = (method: keyof typeof deliveryMethods) => {
    setDeliveryMethods(prev => ({
      ...prev,
      [method]: !prev[method],
    }));
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const groupedSettings = settings.reduce((acc, setting) => {
    if (!acc[setting.category]) {
      acc[setting.category] = [];
    }
    acc[setting.category].push(setting);
    return acc;
  }, {} as Record<string, NotificationSetting[]>);

  const categoryTitles = {
    orders: 'Order Notifications',
    promotions: 'Promotions & Marketing',
    app: 'App Updates',
    delivery: 'Delivery Preferences',
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notification Settings</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Notification Categories */}
        {Object.entries(groupedSettings).map(([category, categorySettings]) => (
          <View key={category} style={styles.section}>
            <Text style={styles.sectionTitle}>
              {categoryTitles[category as keyof typeof categoryTitles]}
            </Text>
            
            <View style={styles.settingsContainer}>
              {categorySettings.map((setting) => (
                <View key={setting.id} style={styles.settingItem}>
                  <View style={styles.settingContent}>
                    <Text style={styles.settingTitle}>{setting.title}</Text>
                    <Text style={styles.settingDescription}>
                      {setting.description}
                    </Text>
                  </View>
                  
                  <Switch
                    value={setting.enabled}
                    onValueChange={() => toggleSetting(setting.id)}
                    trackColor={{ false: '#E0E0E0', true: '#FF4444' }}
                    thumbColor={setting.enabled ? '#FFFFFF' : '#FFFFFF'}
                  />
                </View>
              ))}
            </View>
          </View>
        ))}

        {/* Delivery Methods */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Delivery Methods</Text>
          
          <View style={styles.settingsContainer}>
            <View style={styles.settingItem}>
              <View style={styles.settingContent}>
                <Text style={styles.settingTitle}>Push Notifications</Text>
                <Text style={styles.settingDescription}>
                  In-app notifications and alerts
                </Text>
              </View>
              
              <Switch
                value={deliveryMethods.push}
                onValueChange={() => toggleDeliveryMethod('push')}
                trackColor={{ false: '#E0E0E0', true: '#FF4444' }}
                thumbColor={deliveryMethods.push ? '#FFFFFF' : '#FFFFFF'}
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingContent}>
                <Text style={styles.settingTitle}>SMS Messages</Text>
                <Text style={styles.settingDescription}>
                  Text message updates
                </Text>
              </View>
              
              <Switch
                value={deliveryMethods.sms}
                onValueChange={() => toggleDeliveryMethod('sms')}
                trackColor={{ false: '#E0E0E0', true: '#FF4444' }}
                thumbColor={deliveryMethods.sms ? '#FFFFFF' : '#FFFFFF'}
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingContent}>
                <Text style={styles.settingTitle}>Email</Text>
                <Text style={styles.settingDescription}>
                  Detailed email notifications
                </Text>
              </View>
              
              <Switch
                value={deliveryMethods.email}
                onValueChange={() => toggleDeliveryMethod('email')}
                trackColor={{ false: '#E0E0E0', true: '#FF4444' }}
                thumbColor={deliveryMethods.email ? '#FFFFFF' : '#FFFFFF'}
              />
            </View>
          </View>
        </View>

        {/* Frequency Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notification Frequency</Text>
          
          <View style={styles.settingsContainer}>
            {(['immediate', 'daily', 'weekly'] as const).map((freq) => (
              <TouchableOpacity
                key={freq}
                style={styles.frequencyItem}
                onPress={() => {
                  setFrequency(freq);
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
                activeOpacity={0.7}
              >
                <View style={styles.frequencyContent}>
                  <Text style={styles.frequencyTitle}>
                    {freq.charAt(0).toUpperCase() + freq.slice(1)}
                  </Text>
                  <Text style={styles.frequencyDescription}>
                    {freq === 'immediate' && 'Get notifications right away'}
                    {freq === 'daily' && 'Daily digest of notifications'}
                    {freq === 'weekly' && 'Weekly summary of activity'}
                  </Text>
                </View>
                
                <View style={[
                  styles.radioButton,
                  frequency === freq && styles.radioButtonSelected,
                ]}>
                  {frequency === freq && (
                    <View style={styles.radioButtonInner} />
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  headerRight: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666666',
    marginBottom: 12,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  settingsContainer: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingContent: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  frequencyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  frequencyContent: {
    flex: 1,
    marginRight: 16,
  },
  frequencyTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  frequencyDescription: {
    fontSize: 14,
    color: '#666666',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#CCCCCC',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    borderColor: '#FF4444',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#FF4444',
  },
});