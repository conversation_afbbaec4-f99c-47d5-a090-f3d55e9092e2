import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface AppInitializationState {
  isLoading: boolean;
  isAuthenticated: boolean;
  userPreferences: any;
  error: string | null;
}

export function useAppInitialization() {
  const [state, setState] = useState<AppInitializationState>({
    isLoading: true,
    isAuthenticated: false,
    userPreferences: null,
    error: null,
  });

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Check authentication status
      const authToken = await AsyncStorage.getItem('authToken');
      const isAuthenticated = !!authToken;

      // Load user preferences
      const preferencesString = await AsyncStorage.getItem('userPreferences');
      const userPreferences = preferencesString 
        ? JSON.parse(preferencesString) 
        : getDefaultPreferences();

      // Cache essential data
      await cacheEssentialData();

      setState({
        isLoading: false,
        isAuthenticated,
        userPreferences,
        error: null,
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Initialization failed',
      }));
    }
  };

  const cacheEssentialData = async () => {
    // Cache frequently used data during app initialization
    // This could include:
    // - Popular restaurants
    // - Categories
    // - User's recent orders
    // - Delivery locations
    
    return new Promise(resolve => {
      setTimeout(resolve, 500); // Simulate caching
    });
  };

  const getDefaultPreferences = () => ({
    theme: 'light',
    notifications: true,
    location: null,
    language: 'en',
  });

  return state;
}