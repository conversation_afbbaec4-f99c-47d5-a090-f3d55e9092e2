import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  PanGestureHandler,
  PinchGestureHandler,
  State,
} from 'react-native';
import { Image } from 'expo-image';
import * as Haptics from 'expo-haptics';

const { width } = Dimensions.get('window');
const HERO_HEIGHT = 250;

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  images: string[];
  isVeg: boolean;
  spiceLevel: number;
  isBestseller: boolean;
  category: string;
  restaurant: string;
}

interface ItemHeroProps {
  item: MenuItem;
  onBack: () => void;
  onShare: () => void;
}

export default function ItemHero({ item, onBack, onShare }: ItemHeroProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const scrollX = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(1)).current;

  const handleImageScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { x: scrollX } } }],
    {
      useNativeDriver: false,
      listener: (event: any) => {
        const index = Math.round(event.nativeEvent.contentOffset.x / width);
        setCurrentImageIndex(index);
      },
    }
  );

  const onPinchGestureEvent = Animated.event(
    [{ nativeEvent: { scale: scale } }],
    { useNativeDriver: true }
  );

  const onPinchHandlerStateChange = (event: any) => {
    if (event.nativeEvent.oldState === State.ACTIVE) {
      Animated.spring(scale, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    }
  };

  const renderImageIndicators = () => {
    if (item.images.length <= 1) return null;

    return (
      <View style={styles.indicatorsContainer}>
        {item.images.map((_, index) => (
          <View
            key={index}
            style={[
              styles.indicator,
              index === currentImageIndex && styles.indicatorActive,
            ]}
          />
        ))}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Image Carousel */}
      <PinchGestureHandler
        onGestureEvent={onPinchGestureEvent}
        onHandlerStateChange={onPinchHandlerStateChange}
      >
        <Animated.View style={[styles.imageContainer, { transform: [{ scale }] }]}>
          <Animated.ScrollView
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onScroll={handleImageScroll}
            scrollEventThrottle={16}
          >
            {item.images.map((imageUri, index) => (
              <Image
                key={index}
                source={{ uri: imageUri }}
                style={styles.itemImage}
                contentFit="cover"
              />
            ))}
          </Animated.ScrollView>
        </Animated.View>
      </PinchGestureHandler>

      {/* Header Controls */}
      <View style={styles.headerControls}>
        <TouchableOpacity
          style={styles.controlButton}
          onPress={onBack}
          activeOpacity={0.7}
        >
          <Text style={styles.controlIcon}>←</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.controlButton}
          onPress={onShare}
          activeOpacity={0.7}
        >
          <Text style={styles.controlIcon}>↗</Text>
        </TouchableOpacity>
      </View>

      {/* Badges */}
      <View style={styles.badgesContainer}>
        {/* Veg/Non-veg Badge */}
        <View style={[
          styles.vegBadge,
          { borderColor: item.isVeg ? '#34A853' : '#FF4444' }
        ]}>
          <View style={[
            styles.vegDot,
            { backgroundColor: item.isVeg ? '#34A853' : '#FF4444' }
          ]} />
        </View>

        {/* Bestseller Ribbon */}
        {item.isBestseller && (
          <View style={styles.bestsellerRibbon}>
            <Text style={styles.bestsellerText}>BESTSELLER</Text>
          </View>
        )}
      </View>

      {/* Image Indicators */}
      {renderImageIndicators()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: HERO_HEIGHT,
    zIndex: 10,
  },
  imageContainer: {
    flex: 1,
  },
  itemImage: {
    width: width,
    height: HERO_HEIGHT,
  },
  headerControls: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  controlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlIcon: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  badgesContainer: {
    position: 'absolute',
    top: 100,
    left: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  vegBadge: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderRadius: 3,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    marginRight: 8,
  },
  vegDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  bestsellerRibbon: {
    backgroundColor: '#FF4444',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    transform: [{ rotate: '-5deg' }],
  },
  bestsellerText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  indicatorsContainer: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  indicatorActive: {
    backgroundColor: '#FFFFFF',
  },
});