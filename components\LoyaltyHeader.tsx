import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface LoyaltyData {
  points: number;
  tier: 'bronze' | 'silver' | 'gold';
  tierProgress: number;
  nextTierPoints: number;
  expiringPoints: number;
  expiringDate: string;
}

interface LoyaltyHeaderProps {
  loyaltyData: LoyaltyData;
  onPointsPress: () => void;
  pointsAnimation: Animated.Value;
}

export function LoyaltyHeader({
  loyaltyData,
  onPointsPress,
  pointsAnimation,
}: LoyaltyHeaderProps) {
  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'bronze': return '#CD7F32';
      case 'silver': return '#C0C0C0';
      case 'gold': return '#FFD700';
      default: return '#666666';
    }
  };

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'bronze': return '🥉';
      case 'silver': return '🥈';
      case 'gold': return '🥇';
      default: return '⭐';
    }
  };

  const handlePointsPress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    onPointsPress();
  };

  return (
    <View style={styles.container}>
      {/* Points Balance */}
      <TouchableOpacity
        style={styles.pointsContainer}
        onPress={handlePointsPress}
        activeOpacity={0.8}
      >
        <Animated.View style={[
          styles.pointsContent,
          {
            transform: [
              {
                scale: pointsAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [1, 1.1],
                }),
              },
            ],
          },
        ]}>
          <Text style={styles.coinIcon}>🪙</Text>
          <View style={styles.pointsInfo}>
            <Text style={styles.pointsValue}>
              {loyaltyData.points.toLocaleString()}
            </Text>
            <Text style={styles.pointsLabel}>Points</Text>
          </View>
        </Animated.View>
      </TouchableOpacity>

      {/* Tier Status */}
      <View style={styles.tierContainer}>
        <View style={styles.tierHeader}>
          <View style={styles.tierInfo}>
            <Text style={styles.tierIcon}>
              {getTierIcon(loyaltyData.tier)}
            </Text>
            <View>
              <Text style={[
                styles.tierName,
                { color: getTierColor(loyaltyData.tier) }
              ]}>
                {loyaltyData.tier.toUpperCase()} TIER
              </Text>
              <Text style={styles.tierSubtext}>
                {loyaltyData.nextTierPoints} points to next tier
              </Text>
            </View>
          </View>
        </View>

        {/* Progress Bar */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill,
                { 
                  width: `${loyaltyData.tierProgress}%`,
                  backgroundColor: getTierColor(loyaltyData.tier),
                }
              ]} 
            />
          </View>
          <Text style={styles.progressText}>
            {loyaltyData.tierProgress}%
          </Text>
        </View>
      </View>

      {/* Expiring Points Warning */}
      {loyaltyData.expiringPoints > 0 && (
        <View style={styles.expiringWarning}>
          <Text style={styles.warningIcon}>⚠️</Text>
          <Text style={styles.warningText}>
            {loyaltyData.expiringPoints} points expiring on {loyaltyData.expiringDate}
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F8F8F8',
    borderRadius: 16,
    padding: 16,
  },
  pointsContainer: {
    marginBottom: 16,
  },
  pointsContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  coinIcon: {
    fontSize: 32,
    marginRight: 12,
  },
  pointsInfo: {
    alignItems: 'center',
  },
  pointsValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  pointsLabel: {
    fontSize: 14,
    color: '#666666',
    marginTop: 2,
  },
  tierContainer: {
    marginBottom: 12,
  },
  tierHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  tierInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tierIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  tierName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  tierSubtext: {
    fontSize: 12,
    color: '#666666',
    marginTop: 2,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  progressBar: {
    flex: 1,
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666666',
    minWidth: 35,
    textAlign: 'right',
  },
  expiringWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
    padding: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#FF9800',
  },
  warningIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  warningText: {
    flex: 1,
    fontSize: 12,
    color: '#E65100',
    fontWeight: '500',
  },
});