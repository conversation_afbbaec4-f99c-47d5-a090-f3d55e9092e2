import { useState, useEffect } from 'react';

interface Restaurant {
  id: string;
  name: string;
  cuisine: string;
  rating: number;
  deliveryTime: string;
  deliveryFee: number;
  image: string;
  isOpen: boolean;
}

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  isVeg: boolean;
  spiceLevel: number;
  isBestseller: boolean;
  category: string;
}

export function useRestaurantDetail(restaurantId: string | undefined) {
  const [restaurant, setRestaurant] = useState<Restaurant | null>(null);
  const [menu, setMenu] = useState<{ [category: string]: MenuItem[] }>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!restaurantId) return;

    // Simulate API call
    setTimeout(() => {
      // Mock restaurant data
      setRestaurant({
        id: restaurantId,
        name: "Mario's Pizza Palace",
        cuisine: 'Italian',
        rating: 4.5,
        deliveryTime: '25-35 min',
        deliveryFee: 2.99,
        image: 'https://images.unsplash.com/photo-1513104890138-7c749659a591?w=400',
        isOpen: true,
      });

      // Mock menu data
      setMenu({
        Popular: [
          {
            id: '1',
            name: 'Margherita Pizza',
            description: 'Fresh mozzarella, tomato sauce, basil leaves',
            price: 14.99,
            image: 'https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?w=200',
            isVeg: true,
            spiceLevel: 0,
            isBestseller: true,
            category: 'Popular',
          },
          {
            id: '2',
            name: 'Pepperoni Pizza',
            description: 'Classic pepperoni with mozzarella cheese',
            price: 16.99,
            image: 'https://images.unsplash.com/photo-1628840042765-356cda07504e?w=200',
            isVeg: false,
            spiceLevel: 1,
            isBestseller: true,
            category: 'Popular',
          },
        ],
        Appetizers: [
          {
            id: '3',
            name: 'Garlic Bread',
            description: 'Toasted bread with garlic butter and herbs',
            price: 6.99,
            image: 'https://images.unsplash.com/photo-1541745537411-b8046dc6d66c?w=200',
            isVeg: true,
            spiceLevel: 0,
            isBestseller: false,
            category: 'Appetizers',
          },
        ],
        Mains: [
          {
            id: '4',
            name: 'Chicken Alfredo',
            description: 'Creamy alfredo sauce with grilled chicken',
            price: 18.99,
            image: 'https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=200',
            isVeg: false,
            spiceLevel: 0,
            isBestseller: false,
            category: 'Mains',
          },
        ],
        Desserts: [
          {
            id: '5',
            name: 'Tiramisu',
            description: 'Classic Italian dessert with coffee and mascarpone',
            price: 7.99,
            image: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?w=200',
            isVeg: true,
            spiceLevel: 0,
            isBestseller: false,
            category: 'Desserts',
          },
        ],
        Beverages: [
          {
            id: '6',
            name: 'Italian Soda',
            description: 'Refreshing sparkling water with fruit flavors',
            price: 3.99,
            image: 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=200',
            isVeg: true,
            spiceLevel: 0,
            isBestseller: false,
            category: 'Beverages',
          },
        ],
        Combos: [
          {
            id: '7',
            name: 'Pizza + Drink Combo',
            description: 'Any personal pizza with a drink of your choice',
            price: 12.99,
            image: 'https://images.unsplash.com/photo-1513104890138-7c749659a591?w=200',
            isVeg: true,
            spiceLevel: 0,
            isBestseller: false,
            category: 'Combos',
          },
        ],
      });

      setLoading(false);
    }, 1000);
  }, [restaurantId]);

  return { restaurant, menu, loading };
}