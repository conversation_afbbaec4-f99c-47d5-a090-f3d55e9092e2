import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';

interface LeaderboardUser {
  id: string;
  name: string;
  avatar?: string;
  referrals: number;
  earnings: number;
  rank: number;
  isCurrentUser?: boolean;
  badge?: string;
  streak?: number;
}

interface Prize {
  rank: number;
  title: string;
  reward: string;
  icon: string;
}

export default function LeaderboardScreen() {
  const router = useRouter();
  const [selectedPeriod, setSelectedPeriod] = useState<'monthly' | 'allTime'>('monthly');
  const [animatedValues] = useState(
    Array.from({ length: 10 }, () => new Animated.Value(0))
  );

  const currentUser: LeaderboardUser = {
    id: 'current',
    name: 'You',
    referrals: 8,
    earnings: 40.00,
    rank: 47,
    isCurrentUser: true,
  };

  const topUsers: LeaderboardUser[] = [
    {
      id: '1',
      name: '<PERSON>',
      referrals: 156,
      earnings: 780.00,
      rank: 1,
      badge: '👑',
      streak: 12,
    },
    {
      id: '2',
      name: '<PERSON>.',
      referrals: 142,
      earnings: 710.00,
      rank: 2,
      badge: '🥈',
      streak: 8,
    },
    {
      id: '3',
      name: 'Emma W.',
      referrals: 128,
      earnings: 640.00,
      rank: 3,
      badge: '🥉',
      streak: 15,
    },
    {
      id: '4',
      name: 'David B.',
      referrals: 98,
      earnings: 490.00,
      rank: 4,
      badge: '🔥',
      streak: 6,
    },
    {
      id: '5',
      name: 'Lisa G.',
      referrals: 87,
      earnings: 435.00,
      rank: 5,
      badge: '⭐',
      streak: 4,
    },
    {
      id: '6',
      name: 'Tom W.',
      referrals: 76,
      earnings: 380.00,
      rank: 6,
      streak: 3,
    },
    {
      id: '7',
      name: 'Anna K.',
      referrals: 65,
      earnings: 325.00,
      rank: 7,
      streak: 2,
    },
    {
      id: '8',
      name: 'John D.',
      referrals: 54,
      earnings: 270.00,
      rank: 8,
      streak: 1,
    },
    {
      id: '9',
      name: 'Maria S.',
      referrals: 43,
      earnings: 215.00,
      rank: 9,
    },
    {
      id: '10',
      name: 'Chris R.',
      referrals: 32,
      earnings: 160.00,
      rank: 10,
    },
  ];

  const prizes: Prize[] = [
    {
      rank: 1,
      title: 'Champion',
      reward: '$500 Cash + Premium Badge',
      icon: '👑',
    },
    {
      rank: 2,
      title: 'Runner-up',
      reward: '$300 Cash + Silver Badge',
      icon: '🥈',
    },
    {
      rank: 3,
      title: 'Third Place',
      reward: '$200 Cash + Bronze Badge',
      icon: '🥉',
    },
    {
      rank: 10,
      title: 'Top 10',
      reward: '$50 Bonus + Special Badge',
      icon: '🏆',
    },
  ];

  useEffect(() => {
    // Animate leaderboard entries
    const animations = animatedValues.map((value, index) =>
      Animated.timing(value, {
        toValue: 1,
        duration: 300,
        delay: index * 100,
        useNativeDriver: true,
      })
    );

    Animated.stagger(50, animations).start();
  }, [selectedPeriod]);

  const getPodiumHeight = (rank: number) => {
    switch (rank) {
      case 1: return 80;
      case 2: return 60;
      case 3: return 40;
      default: return 0;
    }
  };

  const getPodiumColor = (rank: number) => {
    switch (rank) {
      case 1: return '#FFD700';
      case 2: return '#C0C0C0';
      case 3: return '#CD7F32';
      default: return '#E0E0E0';
    }
  };

  const renderPodium = () => (
    <View style={styles.podiumContainer}>
      <Text style={styles.podiumTitle}>🏆 Top 3 Champions</Text>
      
      <View style={styles.podium}>
        {/* Second Place */}
        <View style={styles.podiumPosition}>
          <View style={styles.podiumUser}>
            <View style={styles.podiumAvatar}>
              <Text style={styles.podiumAvatarText}>
                {topUsers[1].name.charAt(0)}
              </Text>
            </View>
            <Text style={styles.podiumName}>{topUsers[1].name}</Text>
            <Text style={styles.podiumReferrals}>{topUsers[1].referrals} referrals</Text>
            <Text style={styles.podiumEarnings}>${topUsers[1].earnings.toFixed(0)}</Text>
          </View>
          <View style={[
            styles.podiumBase,
            { 
              height: getPodiumHeight(2),
              backgroundColor: getPodiumColor(2),
            }
          ]}>
            <Text style={styles.podiumRank}>2</Text>
          </View>
        </View>

        {/* First Place */}
        <View style={styles.podiumPosition}>
          <View style={styles.podiumUser}>
            <View style={[styles.podiumAvatar, styles.podiumAvatarFirst]}>
              <Text style={styles.podiumAvatarText}>
                {topUsers[0].name.charAt(0)}
              </Text>
            </View>
            <Text style={styles.podiumCrown}>👑</Text>
            <Text style={[styles.podiumName, styles.podiumNameFirst]}>
              {topUsers[0].name}
            </Text>
            <Text style={styles.podiumReferrals}>{topUsers[0].referrals} referrals</Text>
            <Text style={styles.podiumEarnings}>${topUsers[0].earnings.toFixed(0)}</Text>
          </View>
          <View style={[
            styles.podiumBase,
            { 
              height: getPodiumHeight(1),
              backgroundColor: getPodiumColor(1),
            }
          ]}>
            <Text style={styles.podiumRank}>1</Text>
          </View>
        </View>

        {/* Third Place */}
        <View style={styles.podiumPosition}>
          <View style={styles.podiumUser}>
            <View style={styles.podiumAvatar}>
              <Text style={styles.podiumAvatarText}>
                {topUsers[2].name.charAt(0)}
              </Text>
            </View>
            <Text style={styles.podiumName}>{topUsers[2].name}</Text>
            <Text style={styles.podiumReferrals}>{topUsers[2].referrals} referrals</Text>
            <Text style={styles.podiumEarnings}>${topUsers[2].earnings.toFixed(0)}</Text>
          </View>
          <View style={[
            styles.podiumBase,
            { 
              height: getPodiumHeight(3),
              backgroundColor: getPodiumColor(3),
            }
          ]}>
            <Text style={styles.podiumRank}>3</Text>
          </View>
        </View>
      </View>
    </View>
  );

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      <TouchableOpacity
        style={[
          styles.periodButton,
          selectedPeriod === 'monthly' && styles.periodButtonActive,
        ]}
        onPress={() => setSelectedPeriod('monthly')}
        activeOpacity={0.7}
      >
        <Text style={[
          styles.periodButtonText,
          selectedPeriod === 'monthly' && styles.periodButtonTextActive,
        ]}>
          This Month
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[
          styles.periodButton,
          selectedPeriod === 'allTime' && styles.periodButtonActive,
        ]}
        onPress={() => setSelectedPeriod('allTime')}
        activeOpacity={0.7}
      >
        <Text style={[
          styles.periodButtonText,
          selectedPeriod === 'allTime' && styles.periodButtonTextActive,
        ]}>
          All Time
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderCurrentUserRank = () => (
    <View style={styles.currentUserCard}>
      <View style={styles.currentUserHeader}>
        <Text style={styles.currentUserTitle}>Your Position</Text>
        <Text style={styles.currentUserRank}>#{currentUser.rank}</Text>
      </View>
      
      <View style={styles.currentUserStats}>
        <View style={styles.currentUserStat}>
          <Text style={styles.currentUserStatValue}>{currentUser.referrals}</Text>
          <Text style={styles.currentUserStatLabel}>Referrals</Text>
        </View>
        
        <View style={styles.currentUserStat}>
          <Text style={styles.currentUserStatValue}>
            ${currentUser.earnings.toFixed(0)}
          </Text>
          <Text style={styles.currentUserStatLabel}>Earned</Text>
        </View>
        
        <View style={styles.currentUserStat}>
          <Text style={styles.currentUserStatValue}>
            {topUsers[Math.min(currentUser.rank - 2, topUsers.length - 1)]?.referrals - currentUser.referrals || 0}
          </Text>
          <Text style={styles.currentUserStatLabel}>To Next Rank</Text>
        </View>
      </View>
      
      <TouchableOpacity
        style={styles.improveButton}
        onPress={() => router.push('/referral/invite')}
        activeOpacity={0.7}
      >
        <Text style={styles.improveButtonText}>📈 Improve Ranking</Text>
      </TouchableOpacity>
    </View>
  );

  const renderLeaderboardList = () => (
    <View style={styles.leaderboardList}>
      <Text style={styles.listTitle}>Full Leaderboard</Text>
      
      {topUsers.map((user, index) => (
        <Animated.View
          key={user.id}
          style={[
            styles.leaderboardItem,
            user.rank <= 3 && styles.leaderboardItemTop,
            {
              opacity: animatedValues[index],
              transform: [{
                translateY: animatedValues[index].interpolate({
                  inputRange: [0, 1],
                  outputRange: [20, 0],
                }),
              }],
            },
          ]}
        >
          <View style={styles.leaderboardRank}>
            <Text style={[
              styles.leaderboardRankText,
              user.rank <= 3 && styles.leaderboardRankTextTop,
            ]}>
              {user.rank}
            </Text>
          </View>
          
          <View style={styles.leaderboardAvatar}>
            <Text style={styles.leaderboardAvatarText}>
              {user.name.charAt(0)}
            </Text>
          </View>
          
          <View style={styles.leaderboardInfo}>
            <View style={styles.leaderboardNameContainer}>
              <Text style={styles.leaderboardName}>{user.name}</Text>
              {user.badge && (
                <Text style={styles.leaderboardBadge}>{user.badge}</Text>
              )}
            </View>
            
            <View style={styles.leaderboardStats}>
              <Text style={styles.leaderboardReferrals}>
                {user.referrals} referrals
              </Text>
              {user.streak && (
                <Text style={styles.leaderboardStreak}>
                  🔥 {user.streak} week streak
                </Text>
              )}
            </View>
          </View>
          
          <View style={styles.leaderboardEarnings}>
            <Text style={styles.leaderboardEarningsText}>
              ${user.earnings.toFixed(0)}
            </Text>
          </View>
        </Animated.View>
      ))}
    </View>
  );

  const renderPrizes = () => (
    <View style={styles.prizesCard}>
      <Text style={styles.prizesTitle}>🎁 Monthly Prizes</Text>
      
      <View style={styles.prizesList}>
        {prizes.map((prize) => (
          <View key={prize.rank} style={styles.prizeItem}>
            <Text style={styles.prizeIcon}>{prize.icon}</Text>
            <View style={styles.prizeInfo}>
              <Text style={styles.prizeTitle}>{prize.title}</Text>
              <Text style={styles.prizeReward}>{prize.reward}</Text>
            </View>
            <Text style={styles.prizeRank}>
              {prize.rank === 10 ? 'Top 10' : `#${prize.rank}`}
            </Text>
          </View>
        ))}
      </View>
      
      <View style={styles.prizeNote}>
        <Text style={styles.prizeNoteText}>
          💡 Prizes are awarded at the end of each month
        </Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Leaderboard</Text>
        <TouchableOpacity
          style={styles.rulesButton}
          onPress={() => {/* Show rules */}}
          activeOpacity={0.7}
        >
          <Text style={styles.rulesIcon}>ℹ️</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderPodium()}
        {renderPeriodSelector()}
        {renderCurrentUserRank()}
        {renderLeaderboardList()}
        {renderPrizes()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  rulesButton: {
    padding: 8,
  },
  rulesIcon: {
    fontSize: 20,
  },
  scrollView: {
    flex: 1,
  },
  podiumContainer: {
    backgroundColor: '#F8F8F8',
    margin: 16,
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  podiumTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 20,
  },
  podium: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'center',
    gap: 8,
  },
  podiumPosition: {
    alignItems: 'center',
  },
  podiumUser: {
    alignItems: 'center',
    marginBottom: 8,
  },
  podiumAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#FF4444',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  podiumAvatarFirst: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 3,
    borderColor: '#FFD700',
  },
  podiumAvatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  podiumCrown: {
    fontSize: 20,
    marginBottom: 4,
  },
  podiumName: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  podiumNameFirst: {
    fontSize: 14,
    color: '#FFD700',
  },
  podiumReferrals: {
    fontSize: 10,
    color: '#666666',
    marginBottom: 2,
  },
  podiumEarnings: {
    fontSize: 11,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  podiumBase: {
    width: 60,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  podiumRank: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: '#F8F8F8',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 8,
    padding: 4,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  periodButtonActive: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  periodButtonTextActive: {
    color: '#2C2C2C',
    fontWeight: '600',
  },
  currentUserCard: {
    backgroundColor: '#E8F5E8',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    padding: 20,
    borderWidth: 2,
    borderColor: '#4CAF50',
  },
  currentUserHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  currentUserTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  currentUserRank: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  currentUserStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  currentUserStat: {
    alignItems: 'center',
  },
  currentUserStatValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  currentUserStatLabel: {
    fontSize: 12,
    color: '#666666',
  },
  improveButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  improveButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  leaderboardList: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  leaderboardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
  },
  leaderboardItemTop: {
    backgroundColor: '#FFF3E0',
    borderWidth: 1,
    borderColor: '#FFD700',
  },
  leaderboardRank: {
    width: 30,
    alignItems: 'center',
    marginRight: 12,
  },
  leaderboardRankText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666666',
  },
  leaderboardRankTextTop: {
    color: '#FFD700',
  },
  leaderboardAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FF4444',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  leaderboardAvatarText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  leaderboardInfo: {
    flex: 1,
  },
  leaderboardNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  leaderboardName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginRight: 8,
  },
  leaderboardBadge: {
    fontSize: 16,
  },
  leaderboardStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  leaderboardReferrals: {
    fontSize: 12,
    color: '#666666',
  },
  leaderboardStreak: {
    fontSize: 11,
    color: '#FF9800',
    fontWeight: '500',
  },
  leaderboardEarnings: {
    alignItems: 'flex-end',
  },
  leaderboardEarningsText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  prizesCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginBottom: 40,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  prizesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
    textAlign: 'center',
  },
  prizesList: {
    marginBottom: 16,
  },
  prizeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  prizeIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  prizeInfo: {
    flex: 1,
  },
  prizeTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  prizeReward: {
    fontSize: 12,
    color: '#666666',
  },
  prizeRank: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#FF4444',
  },
  prizeNote: {
    backgroundColor: '#E3F2FD',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  prizeNoteText: {
    fontSize: 12,
    color: '#1976D2',
    textAlign: 'center',
  },
});