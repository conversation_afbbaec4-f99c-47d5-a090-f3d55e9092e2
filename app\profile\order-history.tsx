import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

interface OrderHistoryItem {
  id: string;
  orderNumber: string;
  restaurant: {
    name: string;
    image: string;
  };
  items: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
  total: number;
  status: 'delivered' | 'cancelled' | 'refunded';
  orderDate: Date;
  deliveryDate?: Date;
}

export default function OrderHistoryScreen() {
  const [orders] = useState<OrderHistoryItem[]>([
    {
      id: '1',
      orderNumber: 'FW123456',
      restaurant: {
        name: 'Burger Palace',
        image: '🍔',
      },
      items: [
        { name: 'Classic Burger', quantity: 2, price: 12.99 },
        { name: 'French Fries', quantity: 1, price: 4.99 },
      ],
      total: 30.97,
      status: 'delivered',
      orderDate: new Date('2024-01-15T18:30:00'),
      deliveryDate: new Date('2024-01-15T19:15:00'),
    },
    {
      id: '2',
      orderNumber: 'FW123455',
      restaurant: {
        name: 'Pizza Corner',
        image: '🍕',
      },
      items: [
        { name: 'Margherita Pizza', quantity: 1, price: 16.99 },
        { name: 'Garlic Bread', quantity: 1, price: 5.99 },
      ],
      total: 24.98,
      status: 'delivered',
      orderDate: new Date('2024-01-12T20:00:00'),
      deliveryDate: new Date('2024-01-12T20:45:00'),
    },
    {
      id: '3',
      orderNumber: 'FW123454',
      restaurant: {
        name: 'Sushi Express',
        image: '🍣',
      },
      items: [
        { name: 'California Roll', quantity: 2, price: 8.99 },
        { name: 'Miso Soup', quantity: 1, price: 3.99 },
      ],
      total: 21.97,
      status: 'cancelled',
      orderDate: new Date('2024-01-10T19:15:00'),
    },
  ]);

  const [filter, setFilter] = useState<'all' | 'delivered' | 'cancelled'>('all');

  const filteredOrders = orders.filter(order => 
    filter === 'all' || order.status === filter
  );

  const handleOrderPress = (order: OrderHistoryItem) => {
    router.push(`/profile/order-details/${order.id}`);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleReorder = (order: OrderHistoryItem) => {
    Alert.alert(
      'Reorder',
      `Add items from ${order.restaurant.name} to your cart?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Add to Cart',
          onPress: () => {
            // Add items to cart logic
            Alert.alert('Success', 'Items added to cart!');
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          },
        },
      ]
    );
  };

  const getStatusColor = (status: OrderHistoryItem['status']) => {
    switch (status) {
      case 'delivered':
        return '#34A853';
      case 'cancelled':
        return '#FF4444';
      case 'refunded':
        return '#FBBC04';
      default:
        return '#666666';
    }
  };

  const getStatusText = (status: OrderHistoryItem['status']) => {
    switch (status) {
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      case 'refunded':
        return 'Refunded';
      default:
        return status;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Order History</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        {(['all', 'delivered', 'cancelled'] as const).map((filterOption) => (
          <TouchableOpacity
            key={filterOption}
            style={[
              styles.filterTab,
              filter === filterOption && styles.filterTabActive,
            ]}
            onPress={() => setFilter(filterOption)}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.filterTabText,
              filter === filterOption && styles.filterTabTextActive,
            ]}>
              {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Orders List */}
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {filteredOrders.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyIcon}>📦</Text>
            <Text style={styles.emptyTitle}>No orders found</Text>
            <Text style={styles.emptySubtitle}>
              {filter === 'all' 
                ? "You haven't placed any orders yet"
                : `No ${filter} orders found`
              }
            </Text>
          </View>
        ) : (
          filteredOrders.map((order) => (
            <TouchableOpacity
              key={order.id}
              style={styles.orderCard}
              onPress={() => handleOrderPress(order)}
              activeOpacity={0.8}
            >
              <View style={styles.orderHeader}>
                <View style={styles.restaurantInfo}>
                  <Text style={styles.restaurantIcon}>{order.restaurant.image}</Text>
                  <View style={styles.restaurantDetails}>
                    <Text style={styles.restaurantName}>{order.restaurant.name}</Text>
                    <Text style={styles.orderNumber}>#{order.orderNumber}</Text>
                  </View>
                </View>
                
                <View style={styles.orderStatus}>
                  <Text style={[
                    styles.statusText,
                    { color: getStatusColor(order.status) },
                  ]}>
                    {getStatusText(order.status)}
                  </Text>
                  <Text style={styles.orderTotal}>${order.total.toFixed(2)}</Text>
                </View>
              </View>

              <View style={styles.orderDetails}>
                <Text style={styles.itemsCount}>
                  {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                </Text>
                <Text style={styles.orderDate}>
                  {formatDate(order.orderDate)} at {formatTime(order.orderDate)}
                </Text>
                {order.deliveryDate && (
                  <Text style={styles.deliveryDate}>
                    Delivered at {formatTime(order.deliveryDate)}
                  </Text>
                )}
              </View>

              {order.status === 'delivered' && (
                <View style={styles.orderActions}>
                  <TouchableOpacity
                    style={styles.reorderButton}
                    onPress={() => handleReorder(order)}
                    activeOpacity={0.7}
                  >
                    <Text style={styles.reorderButtonText}>Reorder</Text>
                  </TouchableOpacity>
                </View>
              )}
            </TouchableOpacity>
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  headerRight: {
    width: 40,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#F8F8F8',
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
  },
  filterTabActive: {
    backgroundColor: '#FF4444',
  },
  filterTabText: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  filterTabTextActive: {
    color: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
  orderCard: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  restaurantInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  restaurantIcon: {
    fontSize: 32,
    marginRight: 12,
  },
  restaurantDetails: {
    flex: 1,
  },
  restaurantName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  orderNumber: {
    fontSize: 14,
    color: '#666666',
  },
  orderStatus: {
    alignItems: 'flex-end',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  orderTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  orderDetails: {
    marginBottom: 12,
  },
  itemsCount: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  orderDate: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  deliveryDate: {
    fontSize: 14,
    color: '#34A853',
  },
  orderActions: {
    alignItems: 'flex-end',
  },
  reorderButton: {
    backgroundColor: '#FF4444',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  reorderButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});