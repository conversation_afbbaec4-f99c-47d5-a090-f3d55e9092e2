import * as Haptics from 'expo-haptics';
import { useRef, useState } from 'react';
import {
  Animated,
  Dimensions
} from 'react-native';

interface SpinWheelModalProps {
  visible: boolean;
  onClose: () => void;
  userPoints: number;
  onReward: (reward: { type: string; value: number; display: string }) => void;
}

const { width: screenWidth } = Dimensions.get('window');
const WHEEL_SIZE = Math.min(screenWidth - 80, 300);
const RADIUS = WHEEL_SIZE / 2;

interface WheelSegment {
  id: number;
  reward: {
    type: string;
    value: number;
    display: string;
    icon: string;
  };
  color: string;
  probability: number;
}

export function SpinWheelModal({
  visible,
  onClose,
  userPoints,
  onReward,
}: SpinWheelModalProps) {
  const [isSpinning, setIsSpinning] = useState(false);
  const [hasSpun, setHasSpun] = useState(false);
  const [result, setResult] = useState<WheelSegment | null>(null);
  const spinValue = useRef(new Animated.Value(0)).current;
  const scaleValue = useRef(new Animated.Value(1)).current;

  const segments: WheelSegment[] = [
    {
      id: 0,
      reward: { type: 'points', value: 25, display: '25 Points', icon: '⭐' },
      color: '#FF6B6B',
      probability: 0.3,
    },
    {
      id: 1,
      reward: { type: 'discount', value: 5, display: '5% Off', icon: '🎫' },
      color: '#4ECDC4',
      probability: 0.25,
    },
    {
      id: 2,
      reward: { type: 'points', value: 50, display: '50 Points', icon: '⭐' },
      color: '#45B7D1',
      probability: 0.2,
    },
    {
      id: 3,
      reward: { type: 'free_delivery', value: 0, display: 'Free Delivery', icon: '🚚' },
      color: '#96CEB4',
      probability: 0.15,
    },
    {
      id: 4,
      reward: { type: 'points', value: 100, display: '100 Points', icon: '⭐' },
      color: '#FFEAA7',
      probability: 0.08,
    },
    {
      id: 5,
      reward: { type: 'cashback', value: 10, display: '$10 Cashback', icon: '💰' },
      color: '#DDA0DD',
      probability: 0.02,
    },
  ];

  const SPIN_COST = 50;
  const canSpin = userPoints >= SPIN_COST && !isSpinning && !hasSpun;

  const getSegmentPath = (index: number, total: number) => {
    const angle = (2 * Math.PI) / total;
    const startAngle = index * angle - Math.PI / 2;
    const endAngle = (index + 1) * angle - Math.PI / 2;
    
    const x1 = RADIUS + (RADIUS - 20) * Math.cos(startAngle);
    const y1 = RADIUS + (RADIUS - 20) * Math.sin(startAngle);
    const x2 = RADIUS + (RADIUS - 20) * Math.cos(endAngle);
    const y2 = RADIUS + (RADIUS - 20) * Math.sin(endAngle);
    
    const largeArcFlag = angle > Math.PI ? 1 : 0;
    
    return `M ${RADIUS} ${RADIUS} L ${x1} ${y1} A ${RADIUS - 20} ${RADIUS - 20} 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;
  };

  const getTextPosition = (index: number, total: number) => {
    const angle = (2 * Math.PI) / total;
    const textAngle = index * angle + angle / 2 - Math.PI / 2;
    const textRadius = RADIUS - 60;
    
    return {
      x: RADIUS + textRadius * Math.cos(textAngle),
      y: RADIUS + textRadius * Math.sin(textAngle),
    };
  };

  const selectWinningSegment = () => {
    const random = Math.random();
    let cumulativeProbability = 0;
    
    for (const segment of segments) {
      cumulativeProbability += segment.probability;
      if (random <= cumulativeProbability) {
        return segment;
      }
    }
    
    return segments[0]; // Fallback
  };

  const handleSpin = () => {
    if (!canSpin) return;
    
    setIsSpinning(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    
    const winningSegment = selectWinningSegment();
    setResult(winningSegment);
    
    // Calculate rotation to land on winning segment
    const segmentAngle = 360 / segments.length;
    const targetAngle = winningSegment.id * segmentAngle;
    const spins = 5; // Number of full rotations
    const finalRotation = spins * 360 + (360 - targetAngle) + Math.random() * 20 - 10;
    
    // Animate wheel spin
    Animated.sequence([
      Animated.timing(scaleValue, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleValue, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(spinValue, {
        toValue: finalRotation,
        duration: 3000,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsSpinning(false);
      setHasSpun(true);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    });
  };

  const handleClaimReward = () => {
    if (result) {
      onReward(result.reward);
      onClose();
      // Reset state for next time
      setHasSpun(false);
      setResult(null);
      spinValue.setValue(0);
    }
  };
}
