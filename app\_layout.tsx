import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import 'react-native-reanimated';

import { AuthProvider } from '@/components/AuthProvider';
import FoodWaySplashScreen from '@/components/SplashScreen';
import { useColorScheme } from '@/hooks/useColorScheme';
import { LocationProvider } from '@/hooks/useLocation';
import { useOnboarding } from '@/hooks/useOnboarding';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [isAppReady, setIsAppReady] = useState(false);
  const [fontsLoaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  const { hasSeenOnboarding, isLoading: onboardingLoading } = useOnboarding();

  useEffect(() => {
    if (fontsLoaded && !onboardingLoading) {
      // Small delay to ensure smooth transition
      setTimeout(() => {
        setIsAppReady(true);
      }, 100);
    }
  }, [fontsLoaded, onboardingLoading]);

  const handleSplashFinish = () => {
    setIsAppReady(true);
  };

  // Show splash screen while loading
  if (!isAppReady) {
    return <FoodWaySplashScreen onFinish={handleSplashFinish} />;
  }

  return (
    <AuthProvider>
      <LocationProvider>
        <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
          <Stack screenOptions={{ headerShown: false }}>
            <Stack.Screen name="onboarding" />
            <Stack.Screen name="(tabs)" />
            <Stack.Screen name="auth" />
            <Stack.Screen name="profile" />
            <Stack.Screen name="restaurant" />
            <Stack.Screen name="menu" />
            <Stack.Screen name="item" />
            <Stack.Screen name="cart" />
            <Stack.Screen name="checkout" />
            <Stack.Screen name="orders" />
            <Stack.Screen name="support" />
            <Stack.Screen name="help" />
            <Stack.Screen name="loyalty" />
            <Stack.Screen name="referral" />
            <Stack.Screen name="location" />
            <Stack.Screen name="+not-found" />
          </Stack>
          <StatusBar style="auto" />
        </ThemeProvider>
      </LocationProvider>
    </AuthProvider>
  );
}









