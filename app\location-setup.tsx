import React from 'react';
import { View, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import LocationPermissionScreen from '@/components/location/LocationPermissionScreen';

export default function LocationSetupScreen() {
  const handleLocationSet = (location: any) => {
    // Location has been set, navigate to main app
    console.log('Location set:', location);
    router.replace('/(tabs)');
  };

  return (
    <View style={styles.container}>
      <LocationPermissionScreen onLocationSet={handleLocationSet} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
});