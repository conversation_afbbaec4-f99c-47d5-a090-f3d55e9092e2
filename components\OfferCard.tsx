import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  Clipboard,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface Offer {
  id: string;
  title: string;
  description: string;
  restaurant: {
    name: string;
    cuisine: string;
    logo: string;
  };
  discount: {
    type: 'percentage' | 'fixed' | 'bogo' | 'free_delivery';
    value: number;
    display: string;
  };
  code?: string;
  minOrder?: number;
  validUntil: string;
  category: 'restaurant' | 'delivery' | 'cashback' | 'first_time' | 'loyalty';
  status: 'available' | 'used' | 'expired' | 'coming_soon';
  badges: ('new' | 'expiring' | 'popular' | 'exclusive')[];
  image: string;
  terms: string[];
}

interface OfferCardProps {
  offer: Offer;
  onPress: () => void;
  onUse: () => void;
}

export function OfferCard({ offer, onPress, onUse }: OfferCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const getBadgeStyle = (badge: string) => {
    switch (badge) {
      case 'new':
        return { backgroundColor: '#FF4444', text: 'NEW' };
      case 'expiring':
        return { backgroundColor: '#FF9800', text: 'EXPIRES TODAY' };
      case 'popular':
        return { backgroundColor: '#4CAF50', text: '🔥 POPULAR' };
      case 'exclusive':
        return { backgroundColor: '#9C27B0', text: '👑 EXCLUSIVE' };
      default:
        return { backgroundColor: '#666666', text: badge.toUpperCase() };
    }
  };

  const getTimeRemaining = () => {
    const now = new Date();
    const expiry = new Date(offer.validUntil);
    const diff = expiry.getTime() - now.getTime();
    
    if (diff <= 0) return 'Expired';
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (days > 0) return `${days}d ${hours}h left`;
    if (hours > 0) return `${hours}h left`;
    
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    return `${minutes}m left`;
  };

  const handleCopyCode = async () => {
    if (offer.code) {
      await Clipboard.setString(offer.code);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      Alert.alert('Copied!', `Code ${offer.code} copied to clipboard`);
    }
  };

  const handleUseOffer = () => {
    if (offer.status === 'available') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      onUse();
    }
  };

  const getCardStyle = () => {
    switch (offer.status) {
      case 'used':
        return [styles.card, styles.cardUsed];
      case 'expired':
        return [styles.card, styles.cardExpired];
      case 'coming_soon':
        return [styles.card, styles.cardComingSoon];
      default:
        return styles.card;
    }
  };

  return (
    <TouchableOpacity
      style={getCardStyle()}
      onPress={onPress}
      activeOpacity={0.8}
      disabled={offer.status === 'expired'}
    >
      {/* Status Overlay */}
      {offer.status === 'used' && (
        <View style={styles.statusOverlay}>
          <Text style={styles.statusText}>USED</Text>
        </View>
      )}
      {offer.status === 'expired' && (
        <View style={[styles.statusOverlay, styles.expiredOverlay]}>
          <Text style={[styles.statusText, styles.expiredText]}>EXPIRED</Text>
        </View>
      )}

      {/* Badges */}
      <View style={styles.badgesContainer}>
        {offer.badges.map((badge, index) => {
          const badgeStyle = getBadgeStyle(badge);
          return (
            <View
              key={index}
              style={[styles.badge, { backgroundColor: badgeStyle.backgroundColor }]}
            >
              <Text style={styles.badgeText}>{badgeStyle.text}</Text>
            </View>
          );
        })}
      </View>

      {/* Main Content */}
      <View style={styles.content}>
        {/* Restaurant Info */}
        <View style={styles.restaurantInfo}>
          <View style={styles.restaurantLogo}>
            <Text style={styles.restaurantLogoText}>{offer.restaurant.logo}</Text>
          </View>
          <View style={styles.restaurantDetails}>
            <Text style={styles.restaurantName}>{offer.restaurant.name}</Text>
            <Text style={styles.restaurantCuisine}>{offer.restaurant.cuisine}</Text>
          </View>
        </View>

        {/* Discount Badge */}
        <View style={styles.discountBadge}>
          <Text style={styles.discountText}>{offer.discount.display}</Text>
        </View>
      </View>

      {/* Offer Details */}
      <View style={styles.offerDetails}>
        <Text style={styles.offerTitle}>{offer.title}</Text>
        <Text style={styles.offerDescription}>{offer.description}</Text>
        
        {/* Validity and Terms */}
        <View style={styles.validityContainer}>
          <Text style={styles.validityText}>
            Valid until {new Date(offer.validUntil).toLocaleDateString()}
          </Text>
          <Text style={styles.timeRemaining}>{getTimeRemaining()}</Text>
        </View>

        {offer.minOrder && (
          <Text style={styles.minOrder}>
            Minimum order: ${offer.minOrder}
          </Text>
        )}
      </View>

      {/* Code Section */}
      {offer.code && (
        <View style={styles.codeSection}>
          <View style={styles.codeContainer}>
            <Text style={styles.codeLabel}>Code:</Text>
            <Text style={styles.codeText}>{offer.code}</Text>
            <TouchableOpacity
              style={styles.copyButton}
              onPress={handleCopyCode}
              activeOpacity={0.7}
            >
              <Text style={styles.copyIcon}>📋</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Terms Toggle */}
      <TouchableOpacity
        style={styles.termsToggle}
        onPress={() => setIsExpanded(!isExpanded)}
        activeOpacity={0.7}
      >
        <Text style={styles.termsToggleText}>
          {isExpanded ? 'Hide Terms' : 'View Terms & Conditions'}
        </Text>
        <Text style={styles.termsToggleIcon}>
          {isExpanded ? '▲' : '▼'}
        </Text>
      </TouchableOpacity>

      {/* Expanded Terms */}
      {isExpanded && (
        <View style={styles.termsContainer}>
          {offer.terms.map((term, index) => (
            <Text key={index} style={styles.termText}>
              • {term}
            </Text>
          ))}
        </View>
      )}

      {/* Action Button */}
      <TouchableOpacity
        style={[
          styles.actionButton,
          offer.status !== 'available' && styles.actionButtonDisabled,
        ]}
        onPress={handleUseOffer}
        disabled={offer.status !== 'available'}
        activeOpacity={0.8}
      >
        <Text style={[
          styles.actionButtonText,
          offer.status !== 'available' && styles.actionButtonTextDisabled,
        ]}>
          {offer.status === 'available' ? 'Use Offer' :
           offer.status === 'used' ? 'Already Used' :
           offer.status === 'expired' ? 'Expired' :
           'Coming Soon'}
        </Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: '#F0F0F0',
    position: 'relative',
  },
  cardUsed: {
    opacity: 0.7,
    backgroundColor: '#F8F8F8',
  },
  cardExpired: {
    opacity: 0.6,
    backgroundColor: '#FFEBEE',
  },
  cardComingSoon: {
    opacity: 0.8,
    backgroundColor: '#F3E5F5',
  },
  statusOverlay: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: '#666666',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    zIndex: 1,
  },
  expiredOverlay: {
    backgroundColor: '#F44336',
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  expiredText: {
    color: '#FFFFFF',
  },
  badgesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  restaurantInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  restaurantLogo: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  restaurantLogoText: {
    fontSize: 24,
  },
  restaurantDetails: {
    flex: 1,
  },
  restaurantName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  restaurantCuisine: {
    fontSize: 14,
    color: '#666666',
  },
  discountBadge: {
    backgroundColor: '#FF4444',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  discountText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  offerDetails: {
    marginBottom: 16,
  },
  offerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  offerDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 12,
  },
  validityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  validityText: {
    fontSize: 12,
    color: '#666666',
  },
  timeRemaining: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FF4444',
  },
  minOrder: {
    fontSize: 12,
    color: '#666666',
    fontStyle: 'italic',
  },
  codeSection: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
  },
  codeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  codeLabel: {
    fontSize: 14,
    color: '#666666',
  },
  codeText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C2C2C',
    letterSpacing: 2,
    flex: 1,
    textAlign: 'center',
  },
  copyButton: {
    padding: 4,
  },
  copyIcon: {
    fontSize: 16,
  },
  termsToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
    marginBottom: 8,
  },
  termsToggleText: {
    fontSize: 12,
    color: '#666666',
  },
  termsToggleIcon: {
    fontSize: 12,
    color: '#666666',
  },
  termsContainer: {
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  termText: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 4,
    lineHeight: 16,
  },
  actionButton: {
    backgroundColor: '#FF4444',
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: 'center',
  },
  actionButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  actionButtonTextDisabled: {
    color: '#999999',
  },
});