import { useEffect, useState } from 'react';

interface Restaurant {
  id: string;
  name: string;
  cuisine: string;
  rating: number;
  deliveryTime: string;
  deliveryFee: number;
  image: string;
  isOpen: boolean;
  distance: number;
  categories: string[];
}

export function useRestaurants() {
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshRestaurants = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data would be replaced with actual API call
      const mockData: Restaurant[] = [
        {
          id: '1',
          name: 'Mario\'s Pizza Palace',
          cuisine: 'Italian',
          rating: 4.5,
          deliveryTime: '25-35 min',
          deliveryFee: 2.99,
          image: 'https://images.unsplash.com/photo-1513104890138-7c749659a591?w=400',
          isOpen: true,
          distance: 1.2,
          categories: ['Pizza', 'Italian'],
        },
      ];
      setRestaurants(mockData);
    } catch (err) {
      setError('Failed to load restaurants');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    refreshRestaurants();
  }, []);

  return { restaurants, loading, error, refreshRestaurants };
}
