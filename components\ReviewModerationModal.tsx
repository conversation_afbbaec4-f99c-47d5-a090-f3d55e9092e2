import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface ReviewModerationModalProps {
  visible: boolean;
  onClose: () => void;
  reviewId: string;
  reviewText: string;
}

interface ReportReason {
  id: string;
  title: string;
  description: string;
  icon: string;
}

export function ReviewModerationModal({
  visible,
  onClose,
  reviewId,
  reviewText,
}: ReviewModerationModalProps) {
  const [selectedReason, setSelectedReason] = useState<string>('');
  const [additionalDetails, setAdditionalDetails] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const reportReasons: ReportReason[] = [
    {
      id: 'inappropriate_language',
      title: 'Inappropriate Language',
      description: 'Contains offensive or inappropriate language',
      icon: '🚫',
    },
    {
      id: 'fake_review',
      title: 'Fake Review',
      description: 'Appears to be fake or not based on actual experience',
      icon: '🎭',
    },
    {
      id: 'spam',
      title: 'Spam',
      description: 'Repetitive or promotional content',
      icon: '📧',
    },
    {
      id: 'personal_attack',
      title: 'Personal Attack',
      description: 'Contains personal attacks or harassment',
      icon: '⚔️',
    },
    {
      id: 'irrelevant',
      title: 'Irrelevant Content',
      description: 'Not related to the restaurant or food experience',
      icon: '❓',
    },
    {
      id: 'copyright',
      title: 'Copyright Violation',
      description: 'Contains copyrighted content without permission',
      icon: '©️',
    },
  ];

  const contentGuidelines = [
    'Be honest and fair in your reviews',
    'Focus on your actual experience with the food and service',
    'Avoid personal attacks or inappropriate language',
    'Don\'t post fake or misleading information',
    'Respect others\' opinions and experiences',
    'Keep reviews relevant to the restaurant',
  ];

  const handleReasonSelect = (reasonId: string) => {
    setSelectedReason(reasonId);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleSubmitReport = async () => {
    if (!selectedReason) {
      Alert.alert('Selection Required', 'Please select a reason for reporting this review.');
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      
      Alert.alert(
        'Report Submitted',
        'Thank you for reporting this review. Our moderation team will review it shortly.',
        [
          {
            text: 'OK',
            onPress: () => {
              setSelectedReason('');
              setAdditionalDetails('');
              onClose();
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit report. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setSelectedReason('');
    setAdditionalDetails('');
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleClose}
            activeOpacity={0.7}
          >
            <Text style={styles.closeIcon}>✕</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Report Review</Text>
          <View style={styles.headerRight} />
        </View>

        <ScrollView 
          style={styles.content}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Review Preview */}
          <View style={styles.reviewPreview}>
            <Text style={styles.previewTitle}>Review to Report:</Text>
            <View style={styles.previewContent}>
              <Text style={styles.previewText} numberOfLines={3}>
                {reviewText}
              </Text>
            </View>
          </View>

          {/* Report Reasons */}
          <View style={styles.reasonsSection}>
            <Text style={styles.sectionTitle}>Why are you reporting this review?</Text>
            <Text style={styles.sectionSubtitle}>
              Select the reason that best describes the issue
            </Text>
            
            <View style={styles.reasonsList}>
              {reportReasons.map((reason) => (
                <TouchableOpacity
                  key={reason.id}
                  style={[
                    styles.reasonItem,
                    selectedReason === reason.id && styles.reasonItemSelected,
                  ]}
                  onPress={() => handleReasonSelect(reason.id)}
                  activeOpacity={0.8}
                >
                  <View style={styles.reasonIcon}>
                    <Text style={styles.reasonIconText}>{reason.icon}</Text>
                  </View>
                  <View style={styles.reasonContent}>
                    <Text style={[
                      styles.reasonTitle,
                      selectedReason === reason.id && styles.reasonTitleSelected,
                    ]}>
                      {reason.title}
                    </Text>
                    <Text style={[
                      styles.reasonDescription,
                      selectedReason === reason.id && styles.reasonDescriptionSelected,
                    ]}>
                      {reason.description}
                    </Text>
                  </View>
                  <View style={styles.reasonSelector}>
                    <View style={[
                      styles.radioButton,
                      selectedReason === reason.id && styles.radioButtonSelected,
                    ]}>
                      {selectedReason === reason.id && (
                        <View style={styles.radioButtonInner} />
                      )}
                    </View>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Additional Details */}
          <View style={styles.detailsSection}>
            <Text style={styles.sectionTitle}>Additional Details (Optional)</Text>
            <Text style={styles.sectionSubtitle}>
              Provide more context to help our moderation team
            </Text>
            
            <TextInput
              style={styles.detailsInput}
              placeholder="Describe the specific issue with this review..."
              placeholderTextColor="#999999"
              value={additionalDetails}
              onChangeText={setAdditionalDetails}
              multiline
              maxLength={500}
              textAlignVertical="top"
            />
            
            <View style={styles.characterCount}>
              <Text style={styles.characterCountText}>
                {additionalDetails.length}/500
              </Text>
            </View>
          </View>

          {/* Content Guidelines */}
          <View style={styles.guidelinesSection}>
            <Text style={styles.sectionTitle}>Community Guidelines</Text>
            <Text style={styles.guidelinesIntro}>
              Help us maintain a positive community by following these guidelines:
            </Text>
            
            <View style={styles.guidelinesList}>
              {contentGuidelines.map((guideline, index) => (
                <View key={index} style={styles.guidelineItem}>
                  <Text style={styles.guidelineBullet}>•</Text>
                  <Text style={styles.guidelineText}>{guideline}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Disclaimer */}
          <View style={styles.disclaimer}>
            <Text style={styles.disclaimerText}>
              Reports are reviewed by our moderation team. False reports may result in account restrictions.
            </Text>
          </View>
        </ScrollView>

        {/* Submit Button */}
        <View style={styles.submitContainer}>
          <TouchableOpacity
            style={[
              styles.submitButton,
              !selectedReason && styles.submitButtonDisabled,
            ]}
            onPress={handleSubmitReport}
            disabled={isSubmitting || !selectedReason}
            activeOpacity={0.8}
          >
            <Text style={[
              styles.submitButtonText,
              !selectedReason && styles.submitButtonTextDisabled,
            ]}>
              {isSubmitting ? 'Submitting Report...' : 'Submit Report'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  closeButton: {
    padding: 8,
  },
  closeIcon: {
    fontSize: 18,
    color: '#666666',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  headerRight: {
    width: 34,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  reviewPreview: {
    margin: 16,
    marginBottom: 24,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 12,
  },
  previewContent: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 3,
    borderLeftColor: '#FF4444',
  },
  previewText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  reasonsSection: {
    paddingHorizontal: 16,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
  },
  reasonsList: {
    gap: 12,
  },
  reasonItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  reasonItemSelected: {
    backgroundColor: '#FFF5F5',
    borderColor: '#FF4444',
  },
  reasonIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  reasonIconText: {
    fontSize: 20,
  },
  reasonContent: {
    flex: 1,
  },
  reasonTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  reasonTitleSelected: {
    color: '#FF4444',
  },
  reasonDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 18,
  },
  reasonDescriptionSelected: {
    color: '#FF4444',
  },
  reasonSelector: {
    marginLeft: 12,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#CCCCCC',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    borderColor: '#FF4444',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#FF4444',
  },
  detailsSection: {
    paddingHorizontal: 16,
    marginBottom: 32,
  },
  detailsInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    padding: 16,
    fontSize: 14,
    minHeight: 100,
    color: '#2C2C2C',
    textAlignVertical: 'top',
  },
  characterCount: {
    alignItems: 'flex-end',
    marginTop: 8,
  },
  characterCountText: {
    fontSize: 12,
    color: '#999999',
  },
  guidelinesSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  guidelinesIntro: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 16,
  },
  guidelinesList: {
    gap: 8,
  },
  guidelineItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  guidelineBullet: {
    fontSize: 16,
    color: '#FF4444',
    marginRight: 12,
    marginTop: 2,
  },
  guidelineText: {
    flex: 1,
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  disclaimer: {
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  disclaimerText: {
    fontSize: 12,
    color: '#999999',
    textAlign: 'center',
    lineHeight: 16,
  },
  submitContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  submitButton: {
    backgroundColor: '#FF4444',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  submitButtonTextDisabled: {
    color: '#999999',
  },
});