import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface OrderSummaryProps {
  subtotal: number;
  deliveryFee: number;
  serviceFee: number;
  tax: number;
  discount: number;
  total: number;
  appliedPromo?: string | null;
}

export default function OrderSummary({
  subtotal,
  deliveryFee,
  serviceFee,
  tax,
  discount,
  total,
  appliedPromo,
}: OrderSummaryProps) {
  const SummaryRow = ({ 
    label, 
    amount, 
    isDiscount = false, 
    isTotal = false 
  }: { 
    label: string; 
    amount: number; 
    isDiscount?: boolean; 
    isTotal?: boolean; 
  }) => (
    <View style={[styles.summaryRow, isTotal && styles.totalRow]}>
      <Text style={[
        styles.summaryLabel,
        isDiscount && styles.discountLabel,
        isTotal && styles.totalLabel,
      ]}>
        {label}
      </Text>
      <View style={styles.dottedLine} />
      <Text style={[
        styles.summaryAmount,
        isDiscount && styles.discountAmount,
        isTotal && styles.totalAmount,
      ]}>
        {isDiscount ? '-' : ''}${Math.abs(amount).toFixed(2)}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Order Summary</Text>
      
      <View style={styles.summaryContent}>
        <SummaryRow label="Subtotal" amount={subtotal} />
        <SummaryRow label="Delivery fee" amount={deliveryFee} />
        <SummaryRow label="Service fee" amount={serviceFee} />
        <SummaryRow label="Tax" amount={tax} />
        
        {discount > 0 && (
          <SummaryRow 
            label={`Discount${appliedPromo ? ` (${appliedPromo})` : ''}`} 
            amount={discount} 
            isDiscount 
          />
        )}
        
        <View style={styles.divider} />
        <SummaryRow label="Total" amount={total} isTotal />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#F0F0F0',
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  summaryContent: {
    gap: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 20,
  },
  totalRow: {
    backgroundColor: '#F5F5F5',
    marginHorizontal: -16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666666',
    minWidth: 80,
  },
  discountLabel: {
    color: '#22C55E',
    fontWeight: '500',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  dottedLine: {
    flex: 1,
    height: 1,
    marginHorizontal: 8,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    borderStyle: 'dotted',
  },
  summaryAmount: {
    fontSize: 14,
    color: '#2C2C2C',
    fontWeight: '500',
    minWidth: 60,
    textAlign: 'right',
  },
  discountAmount: {
    color: '#22C55E',
    fontWeight: '600',
  },
  totalAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  divider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginVertical: 8,
  },
});