import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';

import { useLocation } from '@/hooks/useLocation';
import { useAuth } from '@/hooks/useAuth';
import AddressModal from './AddressModal';

export default function HomeHeader() {
  const [showAddressModal, setShowAddressModal] = useState(false);
  const { userLocation } = useLocation();
  const { user } = useAuth();

  const handleAddressPress = () => {
    setShowAddressModal(true);
    Haptics.selectionAsync();
  };

  const handleProfilePress = () => {
    router.push('/profile');
    Haptics.selectionAsync();
  };

  const getDeliveryAddress = () => {
    if (!userLocation) return 'Set delivery location';
    
    if (userLocation.title) {
      return `Deliver to ${userLocation.title}`;
    }
    
    if (userLocation.address) {
      const address = userLocation.address;
      return `Deliver to ${address.street || address.name || 'Current Location'}`;
    }
    
    return 'Deliver to Current Location';
  };

  const getUserInitials = () => {
    if (!user?.fullName) return 'U';
    return user.fullName
      .split(' ')
      .map(name => name[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <View style={styles.container}>
      {/* Delivery Address */}
      <TouchableOpacity 
        style={styles.addressContainer}
        onPress={handleAddressPress}
        activeOpacity={0.7}
      >
        <View style={styles.locationIcon}>
          <Text style={styles.locationIconText}>📍</Text>
        </View>
        <View style={styles.addressTextContainer}>
          <Text style={styles.addressText} numberOfLines={1}>
            {getDeliveryAddress()}
          </Text>
          <Text style={styles.dropdownIcon}>▼</Text>
        </View>
      </TouchableOpacity>

      {/* Profile Avatar */}
      <TouchableOpacity 
        style={styles.profileContainer}
        onPress={handleProfilePress}
        activeOpacity={0.7}
      >
        {user?.avatar ? (
          <Image source={{ uri: user.avatar }} style={styles.avatar} />
        ) : (
          <View style={styles.avatarPlaceholder}>
            <Text style={styles.avatarText}>{getUserInitials()}</Text>
          </View>
        )}
        
        {/* Notification Badge */}
        {user?.hasUnreadNotifications && (
          <View style={styles.notificationBadge}>
            <View style={styles.badgeDot} />
          </View>
        )}
      </TouchableOpacity>

      {/* Address Selection Modal */}
      <AddressModal
        visible={showAddressModal}
        onClose={() => setShowAddressModal(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  addressContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  locationIcon: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  locationIconText: {
    fontSize: 16,
    color: '#FF4444',
  },
  addressTextContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  addressText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
    flex: 1,
  },
  dropdownIcon: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 4,
  },
  profileContainer: {
    position: 'relative',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  avatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  notificationBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF4444',
  },
});