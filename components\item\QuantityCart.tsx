import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  ActivityIndicator,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface QuantityCartProps {
  quantity: number;
  totalPrice: number;
  isLoading: boolean;
  onQuantityChange: (quantity: number) => void;
  onAddToCart: () => void;
}

export default function QuantityCart({
  quantity,
  totalPrice,
  isLoading,
  onQuantityChange,
  onAddToCart,
}: QuantityCartProps) {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const successAnim = useRef(new Animated.Value(0)).current;
  const priceAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Animate price when it changes
    Animated.sequence([
      Animated.timing(priceAnim, {
        toValue: 1.1,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(priceAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  }, [totalPrice]);

  const handleDecrease = () => {
    if (quantity > 1) {
      Haptics.selectionAsync();
      onQuantityChange(quantity - 1);
    }
  };

  const handleIncrease = () => {
    if (quantity < 10) {
      Haptics.selectionAsync();
      onQuantityChange(quantity + 1);
    }
  };

  const handleAddToCart = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    
    // Button press animation
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
    
    onAddToCart();
  };

  const showSuccessAnimation = () => {
    Animated.sequence([
      Animated.timing(successAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.delay(1000),
      Animated.timing(successAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  useEffect(() => {
    if (!isLoading) {
      showSuccessAnimation();
    }
  }, [isLoading]);

  return (
    <View style={styles.container}>
      {/* Quantity Selector */}
      <View style={styles.quantitySection}>
        <Text style={styles.quantityLabel}>Quantity</Text>
        <View style={styles.quantityControls}>
          <TouchableOpacity
            style={[
              styles.quantityButton,
              quantity <= 1 && styles.quantityButtonDisabled,
            ]}
            onPress={handleDecrease}
            disabled={quantity <= 1}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.quantityButtonText,
              quantity <= 1 && styles.quantityButtonTextDisabled,
            ]}>
              −
            </Text>
          </TouchableOpacity>
          
          <View style={styles.quantityDisplay}>
            <Text style={styles.quantityText}>{quantity}</Text>
          </View>
          
          <TouchableOpacity
            style={[
              styles.quantityButton,
              quantity >= 10 && styles.quantityButtonDisabled,
            ]}
            onPress={handleIncrease}
            disabled={quantity >= 10}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.quantityButtonText,
              quantity >= 10 && styles.quantityButtonTextDisabled,
            ]}>
              +
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Add to Cart Button */}
      <Animated.View
        style={[
          styles.addToCartContainer,
          { transform: [{ scale: scaleAnim }] },
        ]}
      >
        <TouchableOpacity
          style={[
            styles.addToCartButton,
            isLoading && styles.addToCartButtonLoading,
          ]}
          onPress={handleAddToCart}
          disabled={isLoading}
          activeOpacity={0.9}
        >
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator color="#FFFFFF" size="small" />
              <Text style={styles.loadingText}>Adding...</Text>
            </View>
          ) : (
            <View style={styles.buttonContent}>
              <Text style={styles.addToCartText}>
                Add {quantity > 1 ? `${quantity} ` : ''}to Cart
              </Text>
              <Animated.Text
                style={[
                  styles.totalPrice,
                  { transform: [{ scale: priceAnim }] },
                ]}
              >
                ${totalPrice.toFixed(2)}
              </Animated.Text>
            </View>
          )}
        </TouchableOpacity>
      </Animated.View>

      {/* Success Overlay */}
      <Animated.View
        style={[
          styles.successOverlay,
          {
            opacity: successAnim,
            transform: [
              {
                scale: successAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.8, 1],
                }),
              },
            ],
          },
        ]}
        pointerEvents="none"
      >
        <View style={styles.successContent}>
          <Text style={styles.successIcon}>✓</Text>
          <Text style={styles.successText}>Added to Cart!</Text>
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 34, // Account for safe area
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  quantitySection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  quantityLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  quantityButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  quantityButtonTextDisabled: {
    color: '#999999',
  },
  quantityDisplay: {
    minWidth: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 16,
  },
  quantityText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  addToCartContainer: {
    width: '100%',
  },
  addToCartButton: {
    backgroundColor: '#FF4444',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    shadowColor: '#FF4444',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  addToCartButtonLoading: {
    backgroundColor: '#FF6666',
  },
  buttonContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  addToCartText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  totalPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  loadingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 8,
  },
  successOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  successContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  successIcon: {
    fontSize: 32,
    color: '#34A853',
    marginBottom: 8,
  },
  successText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
  },
});