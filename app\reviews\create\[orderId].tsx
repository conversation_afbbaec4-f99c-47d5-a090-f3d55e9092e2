import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import * as Haptics from 'expo-haptics';
import * as ImagePicker from 'expo-image-picker';

interface RatingCategory {
  id: string;
  label: string;
  rating: number;
}

interface ReviewPhoto {
  id: string;
  uri: string;
  width: number;
  height: number;
}

export default function CreateReviewScreen() {
  const { orderId } = useLocalSearchParams<{ orderId: string }>();
  const [overallRating, setOverallRating] = useState(0);
  const [categoryRatings, setCategoryRatings] = useState<RatingCategory[]>([
    { id: 'food_quality', label: 'Food Quality', rating: 0 },
    { id: 'delivery_speed', label: 'Delivery Speed', rating: 0 },
    { id: 'packaging', label: 'Packaging', rating: 0 },
    { id: 'value_money', label: 'Value for Money', rating: 0 },
  ]);
  
  const [reviewText, setReviewText] = useState('');
  const [photos, setPhotos] = useState<ReviewPhoto[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);
  
  const scaleAnimation = useRef(new Animated.Value(1)).current;
  const maxCharacters = 500;

  const ratingLabels = ['', 'Poor', 'Fair', 'Good', 'Great', 'Excellent'];
  
  const reviewTemplates = [
    { rating: 5, text: 'Amazing food and fast delivery! Highly recommend.' },
    { rating: 4, text: 'Good experience overall, would order again.' },
    { rating: 3, text: 'Average experience, room for improvement.' },
    { rating: 2, text: 'Below expectations, several issues encountered.' },
    { rating: 1, text: 'Poor experience, would not recommend.' },
  ];

  const handleStarPress = (rating: number, isOverall: boolean = false, categoryId?: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    // Animate star selection
    Animated.sequence([
      Animated.timing(scaleAnimation, {
        toValue: 1.2,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnimation, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    if (isOverall) {
      setOverallRating(rating);
      // Auto-suggest template based on rating
      const template = reviewTemplates.find(t => t.rating === rating);
      if (template && reviewText === '') {
        setReviewText(template.text);
      }
    } else if (categoryId) {
      setCategoryRatings(prev => 
        prev.map(cat => 
          cat.id === categoryId ? { ...cat, rating } : cat
        )
      );
    }
  };

  const renderStars = (currentRating: number, onPress: (rating: number) => void, size: number = 32) => {
    return (
      <View style={styles.starsContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity
            key={star}
            onPress={() => onPress(star)}
            activeOpacity={0.8}
            style={styles.starButton}
          >
            <Animated.Text
              style={[
                styles.star,
                { 
                  fontSize: size,
                  color: star <= currentRating ? '#FFD700' : '#E0E0E0',
                  transform: [{ scale: star === currentRating ? scaleAnimation : 1 }],
                },
              ]}
            >
              ★
            </Animated.Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const handlePhotoUpload = async () => {
    if (photos.length >= 5) {
      Alert.alert('Photo Limit', 'You can upload up to 5 photos per review.');
      return;
    }

    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant camera/gallery permissions');
      return;
    }

    Alert.alert(
      'Add Photo',
      'Choose photo source',
      [
        { text: 'Camera', onPress: () => openCamera() },
        { text: 'Gallery', onPress: () => openGallery() },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const openCamera = async () => {
    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      addPhoto(result.assets[0]);
    }
  };

  const openGallery = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      addPhoto(result.assets[0]);
    }
  };

  const addPhoto = (asset: any) => {
    const newPhoto: ReviewPhoto = {
      id: Date.now().toString(),
      uri: asset.uri,
      width: asset.width,
      height: asset.height,
    };
    setPhotos(prev => [...prev, newPhoto]);
  };

  const removePhoto = (photoId: string) => {
    setPhotos(prev => prev.filter(photo => photo.id !== photoId));
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleTemplateSelect = (template: typeof reviewTemplates[0]) => {
    setOverallRating(template.rating);
    setReviewText(template.text);
    setShowTemplates(false);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleSubmitReview = async () => {
    if (overallRating === 0) {
      Alert.alert('Rating Required', 'Please provide an overall rating.');
      return;
    }

    if (reviewText.trim().length < 10) {
      Alert.alert('Review Too Short', 'Please write at least 10 characters.');
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Success animation
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      
      Alert.alert(
        'Review Submitted!',
        'Thank you for your feedback. Your review will help other customers.',
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit review. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const averageCategoryRating = categoryRatings.reduce((sum, cat) => sum + cat.rating, 0) / categoryRatings.length;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Write Review</Text>
        <TouchableOpacity
          style={styles.templatesButton}
          onPress={() => setShowTemplates(!showTemplates)}
          activeOpacity={0.7}
        >
          <Text style={styles.templatesIcon}>📝</Text>
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Restaurant Info */}
        <View style={styles.restaurantInfo}>
          <View style={styles.restaurantLogo}>
            <Text style={styles.restaurantLogoText}>🍕</Text>
          </View>
          <View style={styles.restaurantDetails}>
            <Text style={styles.restaurantName}>Mario's Pizza Palace</Text>
            <Text style={styles.orderInfo}>Order #{orderId} • Today, 2:30 PM</Text>
          </View>
        </View>

        {/* Overall Rating */}
        <View style={styles.ratingSection}>
          <Text style={styles.sectionTitle}>Overall Rating</Text>
          <View style={styles.overallRatingContainer}>
            {renderStars(overallRating, (rating) => handleStarPress(rating, true), 40)}
            <Text style={styles.ratingLabel}>
              {overallRating > 0 ? ratingLabels[overallRating] : 'Tap to rate'}
            </Text>
          </View>
        </View>

        {/* Category Ratings */}
        <View style={styles.categorySection}>
          <Text style={styles.sectionTitle}>Rate Categories</Text>
          {categoryRatings.map((category) => (
            <View key={category.id} style={styles.categoryRating}>
              <Text style={styles.categoryLabel}>{category.label}</Text>
              {renderStars(
                category.rating,
                (rating) => handleStarPress(rating, false, category.id),
                24
              )}
            </View>
          ))}
        </View>

        {/* Quick Templates */}
        {showTemplates && (
          <View style={styles.templatesSection}>
            <Text style={styles.sectionTitle}>Quick Templates</Text>
            {reviewTemplates.map((template, index) => (
              <TouchableOpacity
                key={index}
                style={styles.templateButton}
                onPress={() => handleTemplateSelect(template)}
                activeOpacity={0.8}
              >
                <View style={styles.templateRating}>
                  {renderStars(template.rating, () => {}, 16)}
                </View>
                <Text style={styles.templateText}>{template.text}</Text>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Review Text */}
        <View style={styles.textSection}>
          <Text style={styles.sectionTitle}>Your Review</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Tell others about your experience..."
            placeholderTextColor="#999999"
            value={reviewText}
            onChangeText={setReviewText}
            multiline
            maxLength={maxCharacters}
            textAlignVertical="top"
          />
          <View style={styles.characterCount}>
            <Text style={[
              styles.characterCountText,
              reviewText.length > maxCharacters * 0.9 && styles.characterCountWarning,
            ]}>
              {reviewText.length}/{maxCharacters}
            </Text>
          </View>
        </View>

        {/* Photo Upload */}
        <View style={styles.photoSection}>
          <Text style={styles.sectionTitle}>Add Photos (Optional)</Text>
          <Text style={styles.photoSubtitle}>
            Help others by sharing photos of your food
          </Text>
          
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.photosContainer}
          >
            {photos.map((photo) => (
              <View key={photo.id} style={styles.photoItem}>
                <View style={styles.photoPreview}>
                  <Text style={styles.photoPlaceholder}>📷</Text>
                </View>
                <TouchableOpacity
                  style={styles.removePhotoButton}
                  onPress={() => removePhoto(photo.id)}
                  activeOpacity={0.8}
                >
                  <Text style={styles.removePhotoIcon}>✕</Text>
                </TouchableOpacity>
              </View>
            ))}
            
            {photos.length < 5 && (
              <TouchableOpacity
                style={styles.addPhotoButton}
                onPress={handlePhotoUpload}
                activeOpacity={0.8}
              >
                <Text style={styles.addPhotoIcon}>+</Text>
                <Text style={styles.addPhotoText}>Add Photo</Text>
              </TouchableOpacity>
            )}
          </ScrollView>
        </View>

        {/* Photo Guidelines */}
        <View style={styles.guidelinesSection}>
          <Text style={styles.guidelinesTitle}>Photo Tips</Text>
          <Text style={styles.guidelinesText}>
            • Take photos in good lighting{'\n'}
            • Show the actual food you received{'\n'}
            • Avoid blurry or dark images{'\n'}
            • Include packaging if relevant
          </Text>
        </View>
      </ScrollView>

      {/* Submit Button */}
      <View style={styles.submitContainer}>
        <TouchableOpacity
          style={[
            styles.submitButton,
            (overallRating === 0 || reviewText.trim().length < 10) && styles.submitButtonDisabled,
          ]}
          onPress={handleSubmitReview}
          disabled={isSubmitting || overallRating === 0 || reviewText.trim().length < 10}
          activeOpacity={0.8}
        >
          <Text style={[
            styles.submitButtonText,
            (overallRating === 0 || reviewText.trim().length < 10) && styles.submitButtonTextDisabled,
          ]}>
            {isSubmitting ? 'Submitting...' : 'Submit Review'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  templatesButton: {
    padding: 8,
  },
  templatesIcon: {
    fontSize: 20,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  restaurantInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#F8F8F8',
    marginBottom: 20,
  },
  restaurantLogo: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  restaurantLogoText: {
    fontSize: 32,
  },
  restaurantDetails: {
    flex: 1,
  },
  restaurantName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  orderInfo: {
    fontSize: 14,
    color: '#666666',
  },
  ratingSection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  overallRatingContainer: {
    alignItems: 'center',
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  starButton: {
    padding: 4,
  },
  star: {
    fontSize: 32,
  },
  ratingLabel: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '500',
  },
  categorySection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  categoryRating: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  categoryLabel: {
    fontSize: 16,
    color: '#2C2C2C',
    flex: 1,
  },
  templatesSection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  templateButton: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  templateRating: {
    marginBottom: 8,
  },
  templateText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  textSection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 120,
    color: '#2C2C2C',
  },
  characterCount: {
    alignItems: 'flex-end',
    marginTop: 8,
  },
  characterCountText: {
    fontSize: 12,
    color: '#999999',
  },
  characterCountWarning: {
    color: '#FF4444',
  },
  photoSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  photoSubtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 16,
  },
  photosContainer: {
    flexDirection: 'row',
  },
  photoItem: {
    position: 'relative',
    marginRight: 12,
  },
  photoPreview: {
    width: 80,
    height: 80,
    backgroundColor: '#F0F0F0',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoPlaceholder: {
    fontSize: 32,
  },
  removePhotoButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
  },
  removePhotoIcon: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  addPhotoButton: {
    width: 80,
    height: 80,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addPhotoIcon: {
    fontSize: 24,
    color: '#666666',
    marginBottom: 4,
  },
  addPhotoText: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
  guidelinesSection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  guidelinesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  guidelinesText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  submitContainer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    backgroundColor: '#FFFFFF',
  },
  submitButton: {
    backgroundColor: '#FF4444',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  submitButtonTextDisabled: {
    color: '#999999',
  },
});