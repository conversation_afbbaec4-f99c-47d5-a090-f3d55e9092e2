import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

export default function EmptyCart() {
  const handleStartOrdering = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push('/');
  };

  const popularItems = [
    { id: '1', name: 'Margherita Pizza', price: 14.99 },
    { id: '2', name: 'Caesar Salad', price: 8.99 },
    { id: '3', name: 'Chicken Burger', price: 12.99 },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {/* Empty Cart Illustration */}
        <View style={styles.illustration}>
          <Text style={styles.cartIcon}>🛒</Text>
          <View style={styles.foodIcons}>
            <Text style={styles.foodIcon}>🍕</Text>
            <Text style={styles.foodIcon}>🍔</Text>
            <Text style={styles.foodIcon}>🥗</Text>
          </View>
        </View>

        {/* Empty State Message */}
        <Text style={styles.title}>Your cart is empty</Text>
        <Text style={styles.subtitle}>
          Looks like you haven't added anything to your cart yet.
          Start exploring our delicious menu!
        </Text>

        {/* Start Ordering Button */}
        <TouchableOpacity
          style={styles.startOrderingButton}
          onPress={handleStartOrdering}
          activeOpacity={0.8}
        >
          <Text style={styles.startOrderingText}>Start Ordering</Text>
        </TouchableOpacity>

        {/* Popular Items Suggestions */}
        <View style={styles.suggestions}>
          <Text style={styles.suggestionsTitle}>Popular items to try:</Text>
          {popularItems.map((item) => (
            <TouchableOpacity
              key={item.id}
              style={styles.suggestionItem}
              onPress={() => router.push(`/item/${item.id}`)}
              activeOpacity={0.7}
            >
              <Text style={styles.suggestionName}>{item.name}</Text>
              <Text style={styles.suggestionPrice}>${item.price}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  illustration: {
    alignItems: 'center',
    marginBottom: 32,
  },
  cartIcon: {
    fontSize: 80,
    marginBottom: 16,
  },
  foodIcons: {
    flexDirection: 'row',
    gap: 8,
  },
  foodIcon: {
    fontSize: 24,
    opacity: 0.6,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  startOrderingButton: {
    backgroundColor: '#FF4444',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 8,
    marginBottom: 40,
  },
  startOrderingText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  suggestions: {
    width: '100%',
    maxWidth: 300,
  },
  suggestionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
    textAlign: 'center',
  },
  suggestionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    marginBottom: 8,
  },
  suggestionName: {
    fontSize: 14,
    color: '#2C2C2C',
    fontWeight: '500',
  },
  suggestionPrice: {
    fontSize: 14,
    color: '#FF4444',
    fontWeight: '600',
  },
});