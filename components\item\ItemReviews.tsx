import { useState } from 'react';

interface Review {
  id: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  date: string;
  comment: string;
  photos?: string[];
  helpful: number;
  isHelpful?: boolean;
}

interface ItemReviewsProps {
  reviews: {
    averageRating: number;
    totalReviews: number;
    ratingBreakdown: { [key: number]: number };
    recentReviews: Review[];
  };
}

export default function ItemReviews({ reviews }: ItemReviewsProps) {
  const [sortBy, setSortBy] = useState<'recent' | 'helpful' | 'rating'>('recent');
  const [expandedReviews, setExpandedReviews] = useState<string[]>([]);
}


