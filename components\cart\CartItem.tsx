import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  PanGestureHandler,
  State,
  Alert,
} from 'react-native';
import { Image } from 'expo-image';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';

import { useCart } from '@/hooks/useCart';

interface CartItemProps {
  item: {
    id: string;
    name: string;
    price: number;
    image: string;
    customization: any;
    quantity: number;
    totalPrice: number;
  };
  index: number;
}

export default function CartItem({ item, index }: CartItemProps) {
  const { updateQuantity, removeFromCart } = useCart();
  const [isRemoving, setIsRemoving] = useState(false);
  
  const translateX = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(1)).current;
  const opacity = useRef(new Animated.Value(1)).current;

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity <= 0) {
      handleRemoveItem();
      return;
    }

    Haptics.selectionAsync();
    
    // Animate quantity change
    Animated.sequence([
      Animated.timing(scale, {
        toValue: 1.1,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    updateQuantity(item.id, item.customization, newQuantity);
  };

  const handleRemoveItem = () => {
    Alert.alert(
      'Remove Item',
      `Remove ${item.name} from your cart?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            setIsRemoving(true);
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            
            // Animate removal
            Animated.parallel([
              Animated.timing(opacity, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
              }),
              Animated.timing(translateX, {
                toValue: -400,
                duration: 300,
                useNativeDriver: true,
              }),
            ]).start(() => {
              removeFromCart(item.id, item.customization);
            });
          },
        },
      ]
    );
  };

  const handleEditItem = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push(`/item/${item.id}?edit=true`);
  };

  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    { useNativeDriver: true }
  );

  const onHandlerStateChange = (event: any) => {
    if (event.nativeEvent.state === State.END) {
      const { translationX } = event.nativeEvent;
      
      if (translationX < -100) {
        // Swipe to delete
        handleRemoveItem();
      } else {
        // Snap back
        Animated.spring(translateX, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      }
    }
  };

  const formatCustomizations = () => {
    const customizations = [];
    
    if (item.customization.size) {
      customizations.push(`Size: ${item.customization.size}`);
    }
    
    if (item.customization.toppings && item.customization.toppings.length > 0) {
      customizations.push(`Toppings: ${item.customization.toppings.join(', ')}`);
    }
    
    if (item.customization.spiceLevel) {
      const spiceLabels = ['Mild', 'Medium', 'Hot', 'Extra Hot'];
      customizations.push(`Spice: ${spiceLabels[item.customization.spiceLevel - 1]}`);
    }
    
    if (item.customization.instructions) {
      customizations.push(`Note: ${item.customization.instructions}`);
    }
    
    return customizations.join(' • ');
  };

  if (isRemoving) {
    return null;
  }

  return (
    <PanGestureHandler
      onGestureEvent={onGestureEvent}
      onHandlerStateChange={onHandlerStateChange}
    >
      <Animated.View
        style={[
          styles.container,
          {
            transform: [{ translateX }, { scale }],
            opacity,
          },
        ]}
      >
        <TouchableOpacity
          style={styles.itemContent}
          onPress={handleEditItem}
          activeOpacity={0.7}
        >
          <Image source={{ uri: item.image }} style={styles.itemImage} />
          
          <View style={styles.itemDetails}>
            <Text style={styles.itemName} numberOfLines={2}>
              {item.name}
            </Text>
            
            {formatCustomizations() && (
              <Text style={styles.customizations} numberOfLines={2}>
                {formatCustomizations()}
              </Text>
            )}
            
            <View style={styles.priceRow}>
              <Text style={styles.pricePerItem}>
                ${item.price.toFixed(2)} each
              </Text>
              <Text style={styles.totalPrice}>
                ${item.totalPrice.toFixed(2)}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
        
        <View style={styles.quantityControls}>
          <TouchableOpacity
            style={[
              styles.quantityButton,
              item.quantity <= 1 && styles.quantityButtonDisabled,
            ]}
            onPress={() => handleQuantityChange(item.quantity - 1)}
            disabled={item.quantity <= 1}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.quantityButtonText,
              item.quantity <= 1 && styles.quantityButtonTextDisabled,
            ]}>
              −
            </Text>
          </TouchableOpacity>
          
          <Animated.View style={[styles.quantityDisplay, { transform: [{ scale }] }]}>
            <Text style={styles.quantityText}>{item.quantity}</Text>
          </Animated.View>
          
          <TouchableOpacity
            style={styles.quantityButton}
            onPress={() => handleQuantityChange(item.quantity + 1)}
            activeOpacity={0.7}
          >
            <Text style={styles.quantityButtonText}>+</Text>
          </TouchableOpacity>
        </View>
        
        {/* Delete indicator for swipe */}
        <Animated.View
          style={[
            styles.deleteIndicator,
            {
              opacity: translateX.interpolate({
                inputRange: [-100, -50, 0],
                outputRange: [1, 0.5, 0],
                extrapolate: 'clamp',
              }),
            },
          ]}
        >
          <Text style={styles.deleteText}>Remove</Text>
        </Animated.View>
      </Animated.View>
    </PanGestureHandler>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#F0F0F0',
    overflow: 'hidden',
  },
  itemContent: {
    flexDirection: 'row',
    padding: 12,
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  itemDetails: {
    flex: 1,
    justifyContent: 'space-between',
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  customizations: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
    lineHeight: 18,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pricePerItem: {
    fontSize: 14,
    color: '#666666',
  },
  totalPrice: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  quantityButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F8F8F8',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 16,
  },
  quantityButtonDisabled: {
    backgroundColor: '#F0F0F0',
  },
  quantityButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FF4444',
  },
  quantityButtonTextDisabled: {
    color: '#CCCCCC',
  },
  quantityDisplay: {
    minWidth: 40,
    alignItems: 'center',
  },
  quantityText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  deleteIndicator: {
    position: 'absolute',
    right: 16,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FF4444',
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  deleteText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14,
  },
});