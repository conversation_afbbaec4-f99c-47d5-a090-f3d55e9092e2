import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { ItemCustomization } from '@/app/item/[id]';

interface CustomizationOptionsProps {
  customization: ItemCustomization;
  onUpdate: (updates: Partial<ItemCustomization>) => void;
}

interface SizeOption {
  id: 'small' | 'medium' | 'large';
  name: string;
  priceMultiplier: number;
  illustration: string;
}

interface ToppingOption {
  id: string;
  name: string;
  price: number;
  icon: string;
  category: string;
}

interface SpiceLevelOption {
  id: 'mild' | 'medium' | 'hot' | 'extra-hot';
  name: string;
  peppers: number;
  color: string;
}

export default function CustomizationOptions({
  customization,
  onUpdate,
}: CustomizationOptionsProps) {
  const [expandedSections, setExpandedSections] = useState({
    size: true,
    toppings: false,
    spice: false,
    instructions: false,
  });

  const sizeOptions: SizeOption[] = [
    { id: 'small', name: 'Small', priceMultiplier: 0.8, illustration: '🍕' },
    { id: 'medium', name: 'Medium', priceMultiplier: 1, illustration: '🍕' },
    { id: 'large', name: 'Large', priceMultiplier: 1.3, illustration: '🍕' },
  ];

  const toppingOptions: ToppingOption[] = [
    { id: 'extra-cheese', name: 'Extra Cheese', price: 1.5, icon: '🧀', category: 'Cheese' },
    { id: 'pepperoni', name: 'Pepperoni', price: 2.0, icon: '🍖', category: 'Meat' },
    { id: 'mushrooms', name: 'Mushrooms', price: 1.0, icon: '🍄', category: 'Vegetables' },
    { id: 'bell-peppers', name: 'Bell Peppers', price: 1.0, icon: '🫑', category: 'Vegetables' },
    { id: 'olives', name: 'Olives', price: 1.0, icon: '🫒', category: 'Vegetables' },
    { id: 'extra-sauce', name: 'Extra Sauce', price: 0.5, icon: '🍅', category: 'Sauces' },
  ];

  const spiceLevels: SpiceLevelOption[] = [
    { id: 'mild', name: 'Mild', peppers: 1, color: '#34A853' },
    { id: 'medium', name: 'Medium', peppers: 2, color: '#FBBC04' },
    { id: 'hot', name: 'Hot', peppers: 3, color: '#FF8C00' },
    { id: 'extra-hot', name: 'Extra Hot', peppers: 4, color: '#FF4444' },
  ];

  const toggleSection = (section: keyof typeof expandedSections) => {
    Haptics.selectionAsync();
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const handleSizeChange = (size: 'small' | 'medium' | 'large') => {
    Haptics.selectionAsync();
    onUpdate({ size });
  };

  const handleToppingToggle = (toppingId: string) => {
    Haptics.selectionAsync();
    const newToppings = customization.toppings.includes(toppingId)
      ? customization.toppings.filter(id => id !== toppingId)
      : [...customization.toppings, toppingId];
    onUpdate({ toppings: newToppings });
  };

  const handleSpiceLevelChange = (spiceLevel: 'mild' | 'medium' | 'hot' | 'extra-hot') => {
    Haptics.selectionAsync();
    onUpdate({ spiceLevel });
  };

  const handleInstructionsChange = (specialInstructions: string) => {
    onUpdate({ specialInstructions });
  };

  const renderSectionHeader = (
    title: string,
    section: keyof typeof expandedSections,
    subtitle?: string
  ) => (
    <TouchableOpacity
      style={styles.sectionHeader}
      onPress={() => toggleSection(section)}
      activeOpacity={0.7}
    >
      <View style={styles.sectionHeaderContent}>
        <Text style={styles.sectionTitle}>{title}</Text>
        {subtitle && <Text style={styles.sectionSubtitle}>{subtitle}</Text>}
      </View>
      <Text style={[
        styles.expandIcon,
        expandedSections[section] && styles.expandIconRotated
      ]}>
        ▼
      </Text>
    </TouchableOpacity>
  );

  const renderSpicePeppers = (level: SpiceLevelOption) => {
    const peppers = [];
    for (let i = 0; i < 4; i++) {
      peppers.push(
        <Text
          key={i}
          style={[
            styles.spicePepper,
            { 
              opacity: i < level.peppers ? 1 : 0.3,
              color: i < level.peppers ? level.color : '#CCCCCC'
            }
          ]}
        >
          🌶️
        </Text>
      );
    }
    return peppers;
  };

  const groupToppingsByCategory = () => {
    const grouped: { [category: string]: ToppingOption[] } = {};
    toppingOptions.forEach(topping => {
      if (!grouped[topping.category]) {
        grouped[topping.category] = [];
      }
      grouped[topping.category].push(topping);
    });
    return grouped;
  };

  return (
    <View style={styles.container}>
      {/* Size Selection */}
      <View style={styles.section}>
        {renderSectionHeader('Size', 'size', 'Choose your preferred size')}
        
        {expandedSections.size && (
          <View style={styles.sizeOptions}>
            {sizeOptions.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.sizeOption,
                  customization.size === option.id && styles.sizeOptionSelected,
                ]}
                onPress={() => handleSizeChange(option.id)}
                activeOpacity={0.7}
              >
                <View style={styles.sizeIllustration}>
                  <Text style={[
                    styles.sizeIcon,
                    { fontSize: option.id === 'small' ? 20 : option.id === 'medium' ? 24 : 28 }
                  ]}>
                    {option.illustration}
                  </Text>
                </View>
                <Text style={[
                  styles.sizeOptionText,
                  customization.size === option.id && styles.sizeOptionTextSelected,
                ]}>
                  {option.name}
                </Text>
                <Text style={[
                  styles.sizePriceText,
                  customization.size === option.id && styles.sizePriceTextSelected,
                ]}>
                  {option.priceMultiplier === 1 ? 'Standard' : 
                   option.priceMultiplier < 1 ? '-20%' : '+30%'}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>

      {/* Toppings */}
      <View style={styles.section}>
        {renderSectionHeader(
          'Toppings & Add-ons',
          'toppings',
          `${customization.toppings.length} selected`
        )}
        
        {expandedSections.toppings && (
          <View style={styles.toppingsContainer}>
            {Object.entries(groupToppingsByCategory()).map(([category, toppings]) => (
              <View key={category} style={styles.toppingCategory}>
                <Text style={styles.toppingCategoryTitle}>{category}</Text>
                {toppings.map((topping) => (
                  <TouchableOpacity
                    key={topping.id}
                    style={styles.toppingOption}
                    onPress={() => handleToppingToggle(topping.id)}
                    activeOpacity={0.7}
                  >
                    <View style={styles.toppingLeft}>
                      <View style={[
                        styles.checkbox,
                        customization.toppings.includes(topping.id) && styles.checkboxSelected,
                      ]}>
                        {customization.toppings.includes(topping.id) && (
                          <Text style={styles.checkmark}>✓</Text>
                        )}
                      </View>
                      <Text style={styles.toppingIcon}>{topping.icon}</Text>
                      <Text style={styles.toppingName}>{topping.name}</Text>
                    </View>
                    <Text style={styles.toppingPrice}>+${topping.price.toFixed(2)}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            ))}
          </View>
        )}
      </View>

      {/* Spice Level */}
      <View style={styles.section}>
        {renderSectionHeader('Spice Level', 'spice', customization.spiceLevel)}
        
        {expandedSections.spice && (
          <View style={styles.spiceLevels}>
            {spiceLevels.map((level) => (
              <TouchableOpacity
                key={level.id}
                style={[
                  styles.spiceLevel,
                  customization.spiceLevel === level.id && styles.spiceLevelSelected,
                ]}
                onPress={() => handleSpiceLevelChange(level.id)}
                activeOpacity={0.7}
              >
                <View style={styles.spiceLevelLeft}>
                  <View style={[
                    styles.radioButton,
                    customization.spiceLevel === level.id && styles.radioButtonSelected,
                  ]}>
                    {customization.spiceLevel === level.id && (
                      <View style={styles.radioButtonInner} />
                    )}
                  </View>
                  <Text style={styles.spiceLevelName}>{level.name}</Text>
                </View>
                <View style={styles.spicePeppers}>
                  {renderSpicePeppers(level)}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>

      {/* Special Instructions */}
      <View style={styles.section}>
        {renderSectionHeader('Special Instructions', 'instructions', 'Optional')}
        
        {expandedSections.instructions && (
          <View style={styles.instructionsContainer}>
            <TextInput
              style={styles.instructionsInput}
              placeholder="No onions, extra sauce, etc."
              placeholderTextColor="#999999"
              value={customization.specialInstructions}
              onChangeText={handleInstructionsChange}
              multiline
              maxLength={100}
            />
            <Text style={styles.characterCount}>
              {customization.specialInstructions.length}/100
            </Text>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
  },
  section: {
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    paddingBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  sectionHeaderContent: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  expandIcon: {
    fontSize: 12,
    color: '#666666',
    transform: [{ rotate: '0deg' }],
  },
  expandIconRotated: {
    transform: [{ rotate: '180deg' }],
  },
  sizeOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
  },
  sizeOption: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 4,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#F0F0F0',
    backgroundColor: '#FFFFFF',
  },
  sizeOptionSelected: {
    borderColor: '#FF4444',
    backgroundColor: '#FFF5F5',
  },
  sizeIllustration: {
    marginBottom: 8,
  },
  sizeIcon: {
    textAlign: 'center',
  },
  sizeOptionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  sizeOptionTextSelected: {
    color: '#FF4444',
  },
  sizePriceText: {
    fontSize: 12,
    color: '#666666',
  },
  sizePriceTextSelected: {
    color: '#FF4444',
  },
  toppingsContainer: {
    marginTop: 12,
  },
  toppingCategory: {
    marginBottom: 16,
  },
  toppingCategoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  toppingOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  toppingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#CCCCCC',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  checkboxSelected: {
    borderColor: '#FF4444',
    backgroundColor: '#FF4444',
  },
  checkmark: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  toppingIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  toppingName: {
    fontSize: 16,
    color: '#2C2C2C',
    flex: 1,
  },
  toppingPrice: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FF4444',
  },
  spiceLevels: {
    marginTop: 12,
  },
  spiceLevel: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#F0F0F0',
  },
  spiceLevelSelected: {
    borderColor: '#FF4444',
    backgroundColor: '#FFF5F5',
  },
  spiceLevelLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#CCCCCC',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  radioButtonSelected: {
    borderColor: '#FF4444',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#FF4444',
  },
  spiceLevelName: {
    fontSize: 16,
    color: '#2C2C2C',
  },
  spicePeppers: {
    flexDirection: 'row',
  },
  spicePepper: {
    fontSize: 16,
    marginLeft: 2,
  },
  instructionsContainer: {
    marginTop: 12,
  },
  instructionsInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#2C2C2C',
    minHeight: 80,
    textAlignVertical: 'top',
  },
  characterCount: {
    fontSize: 12,
    color: '#999999',
    textAlign: 'right',
    marginTop: 4,
  },
});