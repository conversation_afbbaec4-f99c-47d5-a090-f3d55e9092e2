import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  Animated,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';

interface Reward {
  id: string;
  title: string;
  description: string;
  pointsCost: number;
  category: 'delivery' | 'discount' | 'credit' | 'experience';
  image: string;
  availability: 'available' | 'limited' | 'out_of_stock';
  stock?: number;
  terms: string[];
  expiryDays: number;
}

interface RewardCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
}

export default function RewardsCatalog() {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedReward, setSelectedReward] = useState<Reward | null>(null);
  const [showRedemptionModal, setShowRedemptionModal] = useState(false);
  const [userPoints] = useState(1250);
  const [redeemAnimation] = useState(new Animated.Value(0));

  const categories: RewardCategory[] = [
    { id: 'all', name: 'All', icon: '🎁', color: '#FF4444' },
    { id: 'delivery', name: 'Delivery', icon: '🚚', color: '#4ECDC4' },
    { id: 'discount', name: 'Discounts', icon: '💰', color: '#45B7D1' },
    { id: 'credit', name: 'Credits', icon: '💳', color: '#96CEB4' },
    { id: 'experience', name: 'Experiences', icon: '⭐', color: '#FFEAA7' },
  ];

  const rewards: Reward[] = [
    {
      id: '1',
      title: 'Free Delivery',
      description: 'Free delivery on your next order',
      pointsCost: 100,
      category: 'delivery',
      image: '🚚',
      availability: 'available',
      terms: ['Valid for 30 days', 'Cannot be combined with other offers'],
      expiryDays: 30,
    },
    {
      id: '2',
      title: 'Express Delivery Upgrade',
      description: 'Upgrade to express delivery for free',
      pointsCost: 150,
      category: 'delivery',
      image: '⚡',
      availability: 'available',
      terms: ['Valid for 14 days', 'Subject to availability'],
      expiryDays: 14,
    },
    {
      id: '3',
      title: '$5 Off Next Order',
      description: 'Get $5 off your next order',
      pointsCost: 500,
      category: 'discount',
      image: '💵',
      availability: 'available',
      terms: ['Minimum order $20', 'Valid for 30 days'],
      expiryDays: 30,
    },
    {
      id: '4',
      title: '20% Off Discount',
      description: 'Get 20% off your entire order',
      pointsCost: 750,
      category: 'discount',
      image: '🏷️',
      availability: 'limited',
      stock: 5,
      terms: ['Maximum discount $15', 'Valid for 14 days'],
      expiryDays: 14,
    },
    {
      id: '5',
      title: '$15 Dining Credit',
      description: 'Credit to use at any restaurant',
      pointsCost: 1500,
      category: 'credit',
      image: '🍽️',
      availability: 'available',
      terms: ['Valid for 60 days', 'Cannot be split across orders'],
      expiryDays: 60,
    },
    {
      id: '6',
      title: 'Chef\'s Special Access',
      description: 'Access to exclusive chef specials',
      pointsCost: 2000,
      category: 'experience',
      image: '👨‍🍳',
      availability: 'limited',
      stock: 2,
      terms: ['Valid for 7 days', 'Subject to restaurant availability'],
      expiryDays: 7,
    },
  ];

  const filteredRewards = selectedCategory === 'all' 
    ? rewards 
    : rewards.filter(reward => reward.category === selectedCategory);

  const handleRewardPress = (reward: Reward) => {
    setSelectedReward(reward);
    setShowRedemptionModal(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  const handleRedemption = () => {
    if (!selectedReward) return;

    if (userPoints < selectedReward.pointsCost) {
      Alert.alert(
        'Insufficient Points',
        `You need ${selectedReward.pointsCost - userPoints} more points to redeem this reward.`,
        [{ text: 'OK' }]
      );
      return;
    }

    // Animate redemption
    Animated.sequence([
      Animated.timing(redeemAnimation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(redeemAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    setTimeout(() => {
      setShowRedemptionModal(false);
      Alert.alert(
        'Reward Redeemed!',
        `${selectedReward.title} has been added to your account. Check your rewards in the profile section.`,
        [{ text: 'Great!' }]
      );
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }, 600);
  };

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'available': return '#34A853';
      case 'limited': return '#FF9800';
      case 'out_of_stock': return '#F44336';
      default: return '#34A853';
    }
  };

  const getAvailabilityText = (reward: Reward) => {
    switch (reward.availability) {
      case 'available': return 'Available';
      case 'limited': return `Only ${reward.stock} left`;
      case 'out_of_stock': return 'Out of Stock';
      default: return 'Available';
    }
  };

  const renderCategoryTabs = () => (
    <ScrollView 
      horizontal 
      showsHorizontalScrollIndicator={false}
      style={styles.categoryTabs}
    >
      {categories.map((category) => (
        <TouchableOpacity
          key={category.id}
          style={[
            styles.categoryTab,
            selectedCategory === category.id && styles.categoryTabActive,
            { borderColor: category.color },
          ]}
          onPress={() => setSelectedCategory(category.id)}
          activeOpacity={0.7}
        >
          <Text style={styles.categoryIcon}>{category.icon}</Text>
          <Text style={[
            styles.categoryName,
            selectedCategory === category.id && styles.categoryNameActive,
          ]}>
            {category.name}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  const renderRewardCard = (reward: Reward) => (
    <TouchableOpacity
      key={reward.id}
      style={[
        styles.rewardCard,
        reward.availability === 'out_of_stock' && styles.rewardCardDisabled,
      ]}
      onPress={() => handleRewardPress(reward)}
      disabled={reward.availability === 'out_of_stock'}
      activeOpacity={0.8}
    >
      <View style={styles.rewardHeader}>
        <Text style={styles.rewardImage}>{reward.image}</Text>
        <View style={styles.rewardInfo}>
          <Text style={styles.rewardTitle}>{reward.title}</Text>
          <Text style={styles.rewardDescription}>{reward.description}</Text>
        </View>
      </View>
      
      <View style={styles.rewardFooter}>
        <View style={styles.pointsCost}>
          <Text style={styles.pointsValue}>{reward.pointsCost}</Text>
          <Text style={styles.pointsLabel}>points</Text>
        </View>
        
        <View style={styles.availabilityContainer}>
          <View style={[
            styles.availabilityDot,
            { backgroundColor: getAvailabilityColor(reward.availability) },
          ]} />
          <Text style={[
            styles.availabilityText,
            { color: getAvailabilityColor(reward.availability) },
          ]}>
            {getAvailabilityText(reward)}
          </Text>
        </View>
      </View>
      
      {userPoints < reward.pointsCost && (
        <View style={styles.insufficientPointsOverlay}>
          <Text style={styles.insufficientPointsText}>
            Need {reward.pointsCost - userPoints} more points
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const renderRedemptionModal = () => (
    <Modal
      visible={showRedemptionModal}
      transparent
      animationType="fade"
      onRequestClose={() => setShowRedemptionModal(false)}
    >
      <View style={styles.modalOverlay}>
        <Animated.View style={[
          styles.modalContent,
          {
            transform: [{
              scale: redeemAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [1, 0.95],
              }),
            }],
          },
        ]}>
          {selectedReward && (
            <>
              <View style={styles.modalHeader}>
                <Text style={styles.modalImage}>{selectedReward.image}</Text>
                <Text style={styles.modalTitle}>{selectedReward.title}</Text>
                <Text style={styles.modalDescription}>
                  {selectedReward.description}
                </Text>
              </View>
              
              <View style={styles.modalDetails}>
                <View style={styles.pointsCostLarge}>
                  <Text style={styles.pointsValueLarge}>
                    {selectedReward.pointsCost}
                  </Text>
                  <Text style={styles.pointsLabelLarge}>points</Text>
                </View>
                
                <View style={styles.termsSection}>
                  <Text style={styles.termsTitle}>Terms & Conditions:</Text>
                  {selectedReward.terms.map((term, index) => (
                    <Text key={index} style={styles.termItem}>
                      • {term}
                    </Text>
                  ))}
                  <Text style={styles.termItem}>
                    • Expires in {selectedReward.expiryDays} days after redemption
                  </Text>
                </View>
              </View>
              
              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowRedemptionModal(false)}
                  activeOpacity={0.7}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[
                    styles.redeemButton,
                    userPoints < selectedReward.pointsCost && styles.redeemButtonDisabled,
                  ]}
                  onPress={handleRedemption}
                  disabled={userPoints < selectedReward.pointsCost}
                  activeOpacity={0.8}
                >
                  <Text style={[
                    styles.redeemButtonText,
                    userPoints < selectedReward.pointsCost && styles.redeemButtonTextDisabled,
                  ]}>
                    Redeem Now
                  </Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </Animated.View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Rewards Catalog</Text>
        <View style={styles.pointsBalance}>
          <Text style={styles.pointsBalanceText}>{userPoints} pts</Text>
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Category Tabs */}
        {renderCategoryTabs()}
        
        {/* Rewards Grid */}
        <View style={styles.rewardsSection}>
          <Text style={styles.sectionTitle}>
            {selectedCategory === 'all' ? 'All Rewards' : 
             categories.find(c => c.id === selectedCategory)?.name + ' Rewards'}
          </Text>
          
          <View style={styles.rewardsList}>
            {filteredRewards.map(renderRewardCard)}
          </View>
        </View>
        
        {/* Points Earning Tip */}
        <View style={styles.tipSection}>
          <Text style={styles.tipIcon}>💡</Text>
          <View style={styles.tipContent}>
            <Text style={styles.tipTitle}>Need More Points?</Text>
            <Text style={styles.tipText}>
              Complete challenges, write reviews, and refer friends to earn bonus points!
            </Text>
            <TouchableOpacity
              style={styles.tipButton}
              onPress={() => router.push('/loyalty/earn')}
              activeOpacity={0.7}
            >
              <Text style={styles.tipButtonText}>Learn How to Earn</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
      
      {renderRedemptionModal()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  pointsBalance: {
    backgroundColor: '#FF4444',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  pointsBalanceText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  categoryTabs: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  categoryTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    backgroundColor: '#FFFFFF',
  },
  categoryTabActive: {
    backgroundColor: '#F8F8F8',
  },
  categoryIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  categoryName: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  categoryNameActive: {
    color: '#2C2C2C',
    fontWeight: '600',
  },
  rewardsSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  rewardsList: {
    gap: 12,
  },
  rewardCard: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    position: 'relative',
  },
  rewardCardDisabled: {
    opacity: 0.5,
  },
  rewardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  rewardImage: {
    fontSize: 32,
    marginRight: 16,
  },
  rewardInfo: {
    flex: 1,
  },
  rewardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  rewardDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 18,
  },
  rewardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  pointsCost: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  pointsValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF4444',
    marginRight: 4,
  },
  pointsLabel: {
    fontSize: 12,
    color: '#FF4444',
  },
  availabilityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  availabilityDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 6,
  },
  availabilityText: {
    fontSize: 12,
    fontWeight: '500',
  },
  insufficientPointsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  insufficientPointsText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  tipSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#E3F2FD',
    marginHorizontal: 16,
    marginBottom: 40,
    borderRadius: 12,
    padding: 16,
  },
  tipIcon: {
    fontSize: 20,
    marginRight: 12,
    marginTop: 2,
  },
  tipContent: {
    flex: 1,
  },
  tipTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1976D2',
    marginBottom: 4,
  },
  tipText: {
    fontSize: 12,
    color: '#1976D2',
    lineHeight: 16,
    marginBottom: 8,
  },
  tipButton: {
    alignSelf: 'flex-start',
    backgroundColor: '#1976D2',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  tipButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 400,
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  modalImage: {
    fontSize: 48,
    marginBottom: 12,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 8,
    textAlign: 'center',
  },
  modalDescription: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 18,
  },
  modalDetails: {
    marginBottom: 20,
  },
  pointsCostLarge: {
    flexDirection: 'row',
    alignItems: 'baseline',
    justifyContent: 'center',
    marginBottom: 20,
  },
  pointsValueLarge: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FF4444',
    marginRight: 6,
  },
  pointsLabelLarge: {
    fontSize: 16,
    color: '#FF4444',
  },
  termsSection: {
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 12,
  },
  termsTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  termItem: {
    fontSize: 11,
    color: '#666666',
    lineHeight: 16,
    marginBottom: 2,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#F8F8F8',
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  redeemButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#FF4444',
    alignItems: 'center',
  },
  redeemButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  redeemButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  redeemButtonTextDisabled: {
    color: '#999999',
  },
});