import { useState, useEffect } from 'react';

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  images: string[];
  isVeg: boolean;
  spiceLevel: number;
  isBestseller: boolean;
  category: string;
  restaurant: string;
  ingredients?: string[];
  allergens?: string[];
  nutrition?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  prepTime?: number;
  originalPrice?: number;
  discount?: number;
}

interface RelatedItem {
  id: string;
  name: string;
  price: number;
  image: string;
  isVeg: boolean;
}

interface Review {
  id: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  date: string;
  comment: string;
  photos?: string[];
  helpful: number;
  isHelpful?: boolean;
}

interface ReviewsData {
  averageRating: number;
  totalReviews: number;
  ratingBreakdown: { [key: number]: number };
  recentReviews: Review[];
}

export function useItemDetail(itemId: string | undefined) {
  const [item, setItem] = useState<MenuItem | null>(null);
  const [relatedItems, setRelatedItems] = useState<RelatedItem[]>([]);
  const [reviews, setReviews] = useState<ReviewsData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!itemId) return;

    const fetchItemDetail = async () => {
      setLoading(true);
      
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data
        const mockItem: MenuItem = {
          id: itemId,
          name: 'Margherita Pizza',
          description: 'Classic Italian pizza with fresh mozzarella, tomato sauce, and basil leaves. Made with authentic San Marzano tomatoes and buffalo mozzarella for the perfect taste.',
          price: 14.99,
          originalPrice: 16.99,
          discount: 12,
          images: [
            'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
            'https://images.unsplash.com/photo-1574071318508-1cdbab80d002?w=400',
            'https://images.unsplash.com/photo-1513104890138-7c749659a591?w=400',
          ],
          isVeg: true,
          spiceLevel: 1,
          isBestseller: true,
          category: 'Pizza',
          restaurant: 'Mario\'s Italian Kitchen',
          ingredients: [
            'Pizza dough',
            'San Marzano tomatoes',
            'Fresh mozzarella',
            'Fresh basil',
            'Extra virgin olive oil',
            'Sea salt',
            'Black pepper'
          ],
          allergens: ['Gluten', 'Dairy'],
          nutrition: {
            calories: 285,
            protein: 12,
            carbs: 36,
            fat: 10,
          },
          prepTime: 15,
        };

        const mockRelatedItems: RelatedItem[] = [
          {
            id: '2',
            name: 'Pepperoni Pizza',
            price: 16.99,
            image: 'https://images.unsplash.com/photo-1628840042765-356cda07504e?w=400',
            isVeg: false,
          },
          {
            id: '3',
            name: 'Caesar Salad',
            price: 8.99,
            image: 'https://images.unsplash.com/photo-1546793665-c74683f339c1?w=400',
            isVeg: true,
          },
          {
            id: '4',
            name: 'Garlic Bread',
            price: 5.99,
            image: 'https://images.unsplash.com/photo-1573140247632-f8fd74997d5c?w=400',
            isVeg: true,
          },
          {
            id: '5',
            name: 'Tiramisu',
            price: 7.99,
            image: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?w=400',
            isVeg: true,
          },
        ];

        const mockReviews: ReviewsData = {
          averageRating: 4.3,
          totalReviews: 127,
          ratingBreakdown: {
            5: 65,
            4: 32,
            3: 18,
            2: 8,
            1: 4,
          },
          recentReviews: [
            {
              id: '1',
              userName: 'Sarah Johnson',
              userAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b9e0e4d4?w=100',
              rating: 5,
              date: '2 days ago',
              comment: 'Absolutely delicious! The crust was perfect and the ingredients tasted so fresh. Will definitely order again.',
              photos: [
                'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=200',
                'https://images.unsplash.com/photo-1574071318508-1cdbab80d002?w=200',
              ],
              helpful: 12,
              isHelpful: false,
            },
            {
              id: '2',
              userName: 'Mike Chen',
              rating: 4,
              date: '1 week ago',
              comment: 'Great pizza, authentic taste. The delivery was quick too. Only wish the portion was a bit larger.',
              helpful: 8,
              isHelpful: true,
            },
            {
              id: '3',
              userName: 'Emma Wilson',
              userAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',
              rating: 5,
              date: '2 weeks ago',
              comment: 'Best Margherita pizza in town! The basil was so fresh and the cheese quality is excellent.',
              helpful: 15,
              isHelpful: false,
            },
          ],
        };

        setItem(mockItem);
        setRelatedItems(mockRelatedItems);
        setReviews(mockReviews);
      } catch (error) {
        console.error('Error fetching item details:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchItemDetail();
  }, [itemId]);

  return {
    item,
    relatedItems,
    reviews,
    loading,
  };
}