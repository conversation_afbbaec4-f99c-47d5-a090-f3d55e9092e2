import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router } from 'expo-router';
import { useThemeColor } from '@/hooks/useThemeColor';

export default function MenuScreen() {
  const { restaurantId } = useLocalSearchParams();
  const backgroundColor = useThemeColor({}, 'background');
  const [menuItems, setMenuItems] = useState([]);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setMenuItems([
        { id: '1', name: 'Margherita Pizza', price: '$12.99', description: 'Classic tomato and mozzarella' },
        { id: '2', name: 'Caesar Salad', price: '$8.99', description: 'Fresh romaine with parmesan' },
        { id: '3', name: 'Pasta Carbonara', price: '$14.99', description: 'Creamy pasta with bacon' },
      ]);
    }, 1000);
  }, [restaurantId]);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Menu</Text>
        
        {menuItems.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={styles.menuItem}
            onPress={() => router.push(`/item/${item.id}`)}
          >
            <View style={styles.itemInfo}>
              <Text style={styles.itemName}>{item.name}</Text>
              <Text style={styles.itemDescription}>{item.description}</Text>
            </View>
            <Text style={styles.itemPrice}>{item.price}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { padding: 20 },
  title: { fontSize: 28, fontWeight: 'bold', marginBottom: 20 },
  menuItem: { 
    flexDirection: 'row', 
    justifyContent: 'space-between', 
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  itemInfo: { flex: 1 },
  itemName: { fontSize: 18, fontWeight: 'bold', marginBottom: 4 },
  itemDescription: { fontSize: 14, color: '#666' },
  itemPrice: { fontSize: 16, fontWeight: 'bold', color: '#007AFF' },
});