import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import * as Haptics from 'expo-haptics';
import * as ImagePicker from 'expo-image-picker';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'agent' | 'system';
  timestamp: Date;
  type: 'text' | 'image' | 'order' | 'file';
  imageUri?: string;
  orderId?: string;
  fileUri?: string;
  fileName?: string;
}

interface Agent {
  id: string;
  name: string;
  avatar: string;
  isOnline: boolean;
  isTyping: boolean;
}

export default function ChatScreen() {
  const { category } = useLocalSearchParams<{ category: string }>();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Hello! I\'m here to help you with your order issues. How can I assist you today?',
      sender: 'agent',
      timestamp: new Date(),
      type: 'text',
    },
  ]);
  
  const [inputText, setInputText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [showQuickReplies, setShowQuickReplies] = useState(true);
  const flatListRef = useRef<FlatList>(null);
  const typingAnimation = useRef(new Animated.Value(0)).current;

  const agent: Agent = {
    id: 'agent_1',
    name: 'Sarah Johnson',
    avatar: '👩‍💼',
    isOnline: true,
    isTyping: false,
  };

  const quickReplies = [
    'My order is late',
    'Wrong items delivered',
    'Missing items',
    'Food quality issue',
    'Refund request',
    'Change delivery address',
  ];

  const quickActions = [
    { icon: '📷', label: 'Camera', action: () => handleImagePicker('camera') },
    { icon: '🖼️', label: 'Gallery', action: () => handleImagePicker('gallery') },
    { icon: '📄', label: 'File', action: () => handleFilePicker() },
    { icon: '📍', label: 'Location', action: () => handleLocationShare() },
  ];

  useEffect(() => {
    // Simulate typing indicator
    if (agent.isTyping) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(typingAnimation, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(typingAnimation, {
            toValue: 0,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      ).start();
    }
  }, [agent.isTyping]);

  const handleSendMessage = () => {
    if (inputText.trim().length === 0) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      text: inputText.trim(),
      sender: 'user',
      timestamp: new Date(),
      type: 'text',
    };

    setMessages(prev => [...prev, newMessage]);
    setInputText('');
    setShowQuickReplies(false);
    
    // Simulate agent response
    setTimeout(() => {
      simulateAgentResponse(inputText);
    }, 1000);

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const simulateAgentResponse = (userMessage: string) => {
    let response = "I understand your concern. Let me help you with that.";
    
    if (userMessage.toLowerCase().includes('late')) {
      response = "I can see your order is running late. Let me check with the restaurant and provide you with an update.";
    } else if (userMessage.toLowerCase().includes('wrong') || userMessage.toLowerCase().includes('missing')) {
      response = "I'm sorry about the incorrect order. I'll process a refund or replacement for you right away.";
    } else if (userMessage.toLowerCase().includes('refund')) {
      response = "I can help you with the refund process. The refund will be processed within 3-5 business days.";
    }

    const agentMessage: Message = {
      id: Date.now().toString(),
      text: response,
      sender: 'agent',
      timestamp: new Date(),
      type: 'text',
    };

    setMessages(prev => [...prev, agentMessage]);
  };

  const handleQuickReply = (reply: string) => {
    setInputText(reply);
    setShowQuickReplies(false);
  };

  const handleImagePicker = async (source: 'camera' | 'gallery') => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant camera/gallery permissions');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      const imageMessage: Message = {
        id: Date.now().toString(),
        text: 'Image',
        sender: 'user',
        timestamp: new Date(),
        type: 'image',
        imageUri: result.assets[0].uri,
      };

      setMessages(prev => [...prev, imageMessage]);
    }
  };

  const handleFilePicker = () => {
    // Implement file picker logic
    Alert.alert('File Picker', 'File picker functionality would be implemented here');
  };

  const handleLocationShare = () => {
    const locationMessage: Message = {
      id: Date.now().toString(),
      text: 'Location shared: 123 Main St, City, State',
      sender: 'user',
      timestamp: new Date(),
      type: 'text',
    };

    setMessages(prev => [...prev, locationMessage]);
  };

  const renderMessage = ({ item }: { item: Message }) => {
    const isUser = item.sender === 'user';
    const isSystem = item.sender === 'system';

    if (isSystem) {
      return (
        <View style={styles.systemMessageContainer}>
          <Text style={styles.systemMessage}>{item.text}</Text>
          <Text style={styles.systemTimestamp}>
            {item.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>
        </View>
      );
    }

    return (
      <View style={[
        styles.messageContainer,
        isUser ? styles.userMessageContainer : styles.agentMessageContainer,
      ]}>
        {!isUser && (
          <View style={styles.agentAvatar}>
            <Text style={styles.agentAvatarText}>{agent.avatar}</Text>
          </View>
        )}
        
        <View style={[
          styles.messageBubble,
          isUser ? styles.userBubble : styles.agentBubble,
        ]}>
          {item.type === 'image' && item.imageUri ? (
            <TouchableOpacity
              style={styles.imageContainer}
              onPress={() => {
                // Open image viewer
                Alert.alert('Image Viewer', 'Image viewer would open here');
              }}
            >
              <Text style={styles.imagePreview}>🖼️ Image</Text>
            </TouchableOpacity>
          ) : (
            <Text style={[
              styles.messageText,
              isUser ? styles.userMessageText : styles.agentMessageText,
            ]}>
              {item.text}
            </Text>
          )}
        </View>
      </View>
    );
  };

  const renderTypingIndicator = () => {
    if (!agent.isTyping) return null;

    return (
      <View style={styles.typingContainer}>
        <View style={styles.agentAvatar}>
          <Text style={styles.agentAvatarText}>{agent.avatar}</Text>
        </View>
        <View style={styles.typingBubble}>
          <Animated.View style={[
            styles.typingDots,
            { opacity: typingAnimation }
          ]}>
            <Text style={styles.typingText}>●●●</Text>
          </Animated.View>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        
        <View style={styles.agentInfo}>
          <View style={styles.agentHeaderAvatar}>
            <Text style={styles.agentHeaderAvatarText}>{agent.avatar}</Text>
            <View style={[
              styles.onlineIndicator,
              agent.isOnline && styles.onlineIndicatorActive,
            ]} />
          </View>
          <View style={styles.agentDetails}>
            <Text style={styles.agentName}>{agent.name}</Text>
            <Text style={styles.agentStatus}>
              {agent.isTyping ? 'Typing...' : agent.isOnline ? 'Online' : 'Offline'}
            </Text>
          </View>
        </View>

        <TouchableOpacity
          style={styles.callButton}
          onPress={() => Alert.alert('Call Support', 'Calling support...')}
          activeOpacity={0.7}
        >
          <Text style={styles.callIcon}>📞</Text>
        </TouchableOpacity>
      </View>

      {/* Messages */}
      <KeyboardAvoidingView 
        style={styles.chatContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          onContentSizeChange={() => flatListRef.current?.scrollToEnd()}
          showsVerticalScrollIndicator={false}
          ListFooterComponent={renderTypingIndicator}
        />

        {/* Quick Replies */}
        {showQuickReplies && (
          <View style={styles.quickRepliesContainer}>
            <Text style={styles.quickRepliesTitle}>Quick replies:</Text>
            <View style={styles.quickReplies}>
              {quickReplies.map((reply, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.quickReplyButton}
                  onPress={() => handleQuickReply(reply)}
                  activeOpacity={0.8}
                >
                  <Text style={styles.quickReplyText}>{reply}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        {/* Input Area */}
        <View style={styles.inputContainer}>
          <View style={styles.inputRow}>
            <TouchableOpacity
              style={styles.attachmentButton}
              onPress={() => {
                // Show attachment options
                Alert.alert(
                  'Attachments',
                  'Choose attachment type',
                  quickActions.map(action => ({
                    text: `${action.icon} ${action.label}`,
                    onPress: action.action,
                  }))
                );
              }}
              activeOpacity={0.7}
            >
              <Text style={styles.attachmentIcon}>📎</Text>
            </TouchableOpacity>

            <TextInput
              style={styles.textInput}
              value={inputText}
              onChangeText={setInputText}
              placeholder="Type a message..."
              placeholderTextColor="#999999"
              multiline
              maxLength={1000}
            />

            <TouchableOpacity
              style={[
                styles.sendButton,
                inputText.trim().length > 0 && styles.sendButtonActive,
              ]}
              onPress={handleSendMessage}
              disabled={inputText.trim().length === 0}
              activeOpacity={0.8}
            >
              <Text style={styles.sendIcon}>✈️</Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    backgroundColor: '#FFFFFF',
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  agentInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  agentHeaderAvatar: {
    position: 'relative',
    marginRight: 12,
  },
  agentHeaderAvatarText: {
    fontSize: 32,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#CCCCCC',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  onlineIndicatorActive: {
    backgroundColor: '#34A853',
  },
  agentDetails: {
    flex: 1,
  },
  agentName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  agentStatus: {
    fontSize: 12,
    color: '#666666',
  },
  callButton: {
    padding: 8,
  },
  callIcon: {
    fontSize: 24,
  },
  chatContainer: {
    flex: 1,
  },
  messagesList: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  messagesContent: {
    paddingVertical: 16,
  },
  messageContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  userMessageContainer: {
    justifyContent: 'flex-end',
  },
  agentMessageContainer: {
    justifyContent: 'flex-start',
  },
  agentAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  agentAvatarText: {
    fontSize: 16,
  },
  messageBubble: {
    maxWidth: '75%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
  },
  userBubble: {
    backgroundColor: '#FF4444',
    borderBottomRightRadius: 4,
  },
  agentBubble: {
    backgroundColor: '#F5F5F5',
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  userMessageText: {
    color: '#FFFFFF',
  },
  agentMessageText: {
    color: '#2C2C2C',
  },
  systemMessageContainer: {
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  systemMessage: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
  systemTimestamp: {
    fontSize: 12,
    color: '#999999',
    marginTop: 4,
  },
  imageContainer: {
    width: 200,
    height: 150,
    backgroundColor: '#F0F0F0',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagePreview: {
    fontSize: 48,
  },
  typingContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  typingBubble: {
    backgroundColor: '#F5F5F5',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    borderBottomLeftRadius: 4,
  },
  typingDots: {
    alignItems: 'center',
  },
  typingText: {
    fontSize: 16,
    color: '#666666',
    letterSpacing: 2,
  },
  quickRepliesContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  quickRepliesTitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  quickReplies: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickReplyButton: {
    backgroundColor: '#F8F8F8',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  quickReplyText: {
    fontSize: 14,
    color: '#2C2C2C',
  },
  inputContainer: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  attachmentButton: {
    padding: 8,
    marginRight: 8,
  },
  attachmentIcon: {
    fontSize: 20,
    color: '#666666',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    maxHeight: 100,
    marginRight: 8,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#CCCCCC',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonActive: {
    backgroundColor: '#FF4444',
  },
  sendIcon: {
    fontSize: 16,
  },
});