import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';

interface Challenge {
  id: string;
  title: string;
  description: string;
  type: 'order' | 'social' | 'seasonal';
  difficulty: 'easy' | 'medium' | 'hard';
  progress: number;
  target: number;
  reward: number;
  timeLeft: string;
  status: 'active' | 'completed' | 'expired';
  requirements: string[];
  tips: string[];
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlockedAt: string;
  points: number;
}

export default function ChallengesScreen() {
  const router = useRouter();
  const [selectedTab, setSelectedTab] = useState<'active' | 'completed' | 'achievements'>('active');
  const [selectedChallenge, setSelectedChallenge] = useState<Challenge | null>(null);
  const [showChallengeModal, setShowChallengeModal] = useState(false);
  const [celebrationAnimation] = useState(new Animated.Value(0));

  const challenges: Challenge[] = [
    {
      id: '1',
      title: 'Weekend Warrior',
      description: 'Order 3 times this weekend',
      type: 'order',
      difficulty: 'easy',
      progress: 1,
      target: 3,
      reward: 500,
      timeLeft: '2 days',
      status: 'active',
      requirements: [
        'Place orders on Saturday or Sunday',
        'Orders must be completed successfully',
        'Minimum order value: $15',
      ],
      tips: [
        'Try breakfast, lunch, and dinner orders',
        'Weekend orders often have bonus multipliers',
      ],
    },
    {
      id: '2',
      title: 'Review Master',
      description: 'Write 5 reviews this month',
      type: 'social',
      difficulty: 'medium',
      progress: 3,
      target: 5,
      reward: 250,
      timeLeft: '12 days',
      status: 'active',
      requirements: [
        'Write detailed reviews (minimum 20 characters)',
        'Include rating for food, delivery, and overall experience',
        'Reviews must be for completed orders',
      ],
      tips: [
        'Be honest and helpful in your reviews',
        'Include photos for bonus points',
      ],
    },
    {
      id: '3',
      title: 'Explorer',
      description: 'Try 2 new restaurants',
      type: 'order',
      difficulty: 'easy',
      progress: 0,
      target: 2,
      reward: 300,
      timeLeft: '7 days',
      status: 'active',
      requirements: [
        'Order from restaurants you haven\'t tried before',
        'Complete the full order process',
        'Minimum order value: $20',
      ],
      tips: [
        'Check out highly rated new restaurants',
        'Look for restaurants with special offers',
      ],
    },
    {
      id: '4',
      title: 'Social Butterfly',
      description: 'Refer 3 friends this month',
      type: 'social',
      difficulty: 'hard',
      progress: 1,
      target: 3,
      reward: 1000,
      timeLeft: '15 days',
      status: 'active',
      requirements: [
        'Friends must sign up using your referral code',
        'Friends must complete their first order',
        'Referrals must be new users',
      ],
      tips: [
        'Share your referral code on social media',
        'Tell friends about your favorite restaurants',
      ],
    },
    {
      id: '5',
      title: 'Healthy January',
      description: 'Order 10 healthy meals',
      type: 'seasonal',
      difficulty: 'medium',
      progress: 10,
      target: 10,
      reward: 750,
      timeLeft: 'Completed',
      status: 'completed',
      requirements: [
        'Order from restaurants tagged as "Healthy"',
        'Include salads, grilled items, or vegetarian options',
        'Complete orders during January',
      ],
      tips: [
        'Look for restaurants with healthy badges',
        'Try new cuisines with healthy options',
      ],
    },
  ];

  const achievements: Achievement[] = [
    {
      id: '1',
      title: 'First Order',
      description: 'Completed your first order',
      icon: '🎉',
      unlockedAt: '2 months ago',
      points: 50,
    },
    {
      id: '2',
      title: 'Review Master',
      description: 'Written 50 reviews',
      icon: '📝',
      unlockedAt: '1 month ago',
      points: 500,
    },
    {
      id: '3',
      title: 'Explorer',
      description: 'Tried 25 different restaurants',
      icon: '🗺️',
      unlockedAt: '3 weeks ago',
      points: 300,
    },
    {
      id: '4',
      title: 'Loyal Customer',
      description: 'Completed 100 orders',
      icon: '👑',
      unlockedAt: '1 week ago',
      points: 1000,
    },
  ];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '#34A853';
      case 'medium': return '#FF9800';
      case 'hard': return '#F44336';
      default: return '#34A853';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'order': return '🍽️';
      case 'social': return '👥';
      case 'seasonal': return '🎯';
      default: return '🎯';
    }
  };

  const handleChallengePress = (challenge: Challenge) => {
    setSelectedChallenge(challenge);
    setShowChallengeModal(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {[
        { key: 'active', label: 'Active', count: challenges.filter(c => c.status === 'active').length },
        { key: 'completed', label: 'Completed', count: challenges.filter(c => c.status === 'completed').length },
        { key: 'achievements', label: 'Achievements', count: achievements.length },
      ].map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tabButton,
            selectedTab === tab.key && styles.tabButtonActive,
          ]}
          onPress={() => setSelectedTab(tab.key as any)}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.tabButtonText,
            selectedTab === tab.key && styles.tabButtonTextActive,
          ]}>
            {tab.label}
          </Text>
          <View style={[
            styles.tabBadge,
            selectedTab === tab.key && styles.tabBadgeActive,
          ]}>
            <Text style={[
              styles.tabBadgeText,
              selectedTab === tab.key && styles.tabBadgeTextActive,
            ]}>
              {tab.count}
            </Text>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderChallengeCard = (challenge: Challenge) => (
    <TouchableOpacity
      key={challenge.id}
      style={[
        styles.challengeCard,
        challenge.status === 'completed' && styles.challengeCardCompleted,
      ]}
      onPress={() => handleChallengePress(challenge)}
      activeOpacity={0.8}
    >
      <View style={styles.challengeHeader}>
        <View style={styles.challengeTypeContainer}>
          <Text style={styles.challengeTypeIcon}>{getTypeIcon(challenge.type)}</Text>
          <View style={[
            styles.difficultyBadge,
            { backgroundColor: getDifficultyColor(challenge.difficulty) },
          ]}>
            <Text style={styles.difficultyText}>{challenge.difficulty}</Text>
          </View>
        </View>
        
        <View style={styles.challengeReward}>
          <Text style={styles.rewardValue}>+{challenge.reward}</Text>
          <Text style={styles.rewardLabel}>pts</Text>
        </View>
      </View>
      
      <Text style={styles.challengeTitle}>{challenge.title}</Text>
      <Text style={styles.challengeDescription}>{challenge.description}</Text>
      
      <View style={styles.challengeProgress}>
        <View style={styles.progressBarContainer}>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${Math.min((challenge.progress / challenge.target) * 100, 100)}%`,
                  backgroundColor: challenge.status === 'completed' ? '#34A853' : '#FF4444',
                },
              ]}
            />
          </View>
          <Text style={styles.progressText}>
            {challenge.progress}/{challenge.target}
          </Text>
        </View>
        
        <View style={styles.challengeFooter}>
          <Text style={styles.timeLeft}>
            {challenge.status === 'completed' ? '✅ Completed' : `⏰ ${challenge.timeLeft} left`}
          </Text>
          {challenge.status === 'completed' && (
            <Text style={styles.completedBadge}>🎉</Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderAchievementCard = (achievement: Achievement) => (
    <View key={achievement.id} style={styles.achievementCard}>
      <Text style={styles.achievementIcon}>{achievement.icon}</Text>
      <View style={styles.achievementInfo}>
        <Text style={styles.achievementTitle}>{achievement.title}</Text>
        <Text style={styles.achievementDescription}>{achievement.description}</Text>
        <Text style={styles.achievementDate}>Unlocked {achievement.unlockedAt}</Text>
      </View>
      <View style={styles.achievementPoints}>
        <Text style={styles.achievementPointsValue}>+{achievement.points}</Text>
        <Text style={styles.achievementPointsLabel}>pts</Text>
      </View>
    </View>
  );

  const renderChallengeModal = () => (
    <Modal
      visible={showChallengeModal}
      transparent
      animationType="slide"
      onRequestClose={() => setShowChallengeModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          {selectedChallenge && (
            <>
              <View style={styles.modalHeader}>
                <View style={styles.modalTypeContainer}>
                  <Text style={styles.modalTypeIcon}>
                    {getTypeIcon(selectedChallenge.type)}
                  </Text>
                  <Text style={styles.modalType}>
                    {selectedChallenge.type.charAt(0).toUpperCase() + selectedChallenge.type.slice(1)} Challenge
                  </Text>
                </View>
                
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => setShowChallengeModal(false)}
                  activeOpacity={0.7}
                >
                  <Text style={styles.closeIcon}>✕</Text>
                </TouchableOpacity>
              </View>
              
              <Text style={styles.modalTitle}>{selectedChallenge.title}</Text>
              <Text style={styles.modalDescription}>{selectedChallenge.description}</Text>
              
              <View style={styles.modalProgress}>
                <View style={styles.modalProgressBar}>
                  <View
                    style={[
                      styles.modalProgressFill,
                      {
                        width: `${Math.min((selectedChallenge.progress / selectedChallenge.target) * 100, 100)}%`,
                      },
                    ]}
                  />
                </View>
                <Text style={styles.modalProgressText}>
                  {selectedChallenge.progress} of {selectedChallenge.target} completed
                </Text>
              </View>
              
              <View style={styles.modalReward}>
                <Text style={styles.modalRewardLabel}>Reward</Text>
                <Text style={styles.modalRewardValue}>
                  +{selectedChallenge.reward} points
                </Text>
              </View>
              
              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Requirements</Text>
                {selectedChallenge.requirements.map((req, index) => (
                  <Text key={index} style={styles.modalRequirement}>
                    • {req}
                  </Text>
                ))}
              </View>
              
              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Tips</Text>
                {selectedChallenge.tips.map((tip, index) => (
                  <Text key={index} style={styles.modalTip}>
                    💡 {tip}
                  </Text>
                ))}
              </View>
              
              {selectedChallenge.status === 'active' && (
                <TouchableOpacity
                  style={styles.startChallengeButton}
                  onPress={() => {
                    setShowChallengeModal(false);
                    router.push('/restaurants');
                  }}
                  activeOpacity={0.8}
                >
                  <Text style={styles.startChallengeButtonText}>
                    Start Challenge
                  </Text>
                </TouchableOpacity>
              )}
            </>
          )}
        </View>
      </View>
    </Modal>
  );

  const renderContent = () => {
    switch (selectedTab) {
      case 'active':
        return (
          <View style={styles.challengesList}>
            {challenges.filter(c => c.status === 'active').map(renderChallengeCard)}
          </View>
        );
      case 'completed':
        return (
          <View style={styles.challengesList}>
            {challenges.filter(c => c.status === 'completed').map(renderChallengeCard)}
          </View>
        );
      case 'achievements':
        return (
          <View style={styles.achievementsList}>
            {achievements.map(renderAchievementCard)}
          </View>
        );
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Challenges & Achievements</Text>
        <View style={styles.placeholder} />
      </View>

      {renderTabBar()}

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {selectedTab === 'active' && (
          <View style={styles.summarySection}>
            <Text style={styles.summaryTitle}>Active Challenges</Text>
            <Text style={styles.summaryText}>
              Complete challenges to earn bonus points and unlock achievements!
            </Text>
          </View>
        )}
        
        {renderContent()}
      </ScrollView>
      
      {renderChallengeModal()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  placeholder: {
    width: 40,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#F8F8F8',
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 8,
    padding: 4,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 6,
    gap: 6,
  },
  tabButtonActive: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666666',
  },
  tabButtonTextActive: {
    color: '#2C2C2C',
    fontWeight: '600',
  },
  tabBadge: {
    backgroundColor: '#E0E0E0',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    minWidth: 20,
    alignItems: 'center',
  },
  tabBadgeActive: {
    backgroundColor: '#FF4444',
  },
  tabBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#666666',
  },
  tabBadgeTextActive: {
    color: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  summarySection: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  summaryText: {
    fontSize: 12,
    color: '#666666',
    lineHeight: 16,
  },
  challengesList: {
    paddingHorizontal: 16,
    paddingBottom: 40,
    gap: 12,
  },
  challengeCard: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
  },
  challengeCardCompleted: {
    backgroundColor: '#E8F5E8',
    borderWidth: 1,
    borderColor: '#34A853',
  },
  challengeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  challengeTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  challengeTypeIcon: {
    fontSize: 16,
  },
  difficultyBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  difficultyText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  challengeReward: {
    alignItems: 'flex-end',
  },
  rewardValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FF4444',
  },
  rewardLabel: {
    fontSize: 10,
    color: '#FF4444',
  },
  challengeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  challengeDescription: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 12,
    lineHeight: 16,
  },
  challengeProgress: {
    gap: 8,
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  progressBar: {
    flex: 1,
    height: 6,
    backgroundColor: '#E0E0E0',
    borderRadius: 3,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 10,
    color: '#666666',
    fontWeight: '500',
    minWidth: 30,
  },
  challengeFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  timeLeft: {
    fontSize: 10,
    color: '#666666',
  },
  completedBadge: {
    fontSize: 14,
  },
  achievementsList: {
    paddingHorizontal: 16,
    paddingBottom: 40,
    gap: 12,
  },
  achievementCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
  },
  achievementIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  achievementInfo: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  achievementDescription: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 2,
  },
  achievementDate: {
    fontSize: 10,
    color: '#999999',
  },
  achievementPoints: {
    alignItems: 'flex-end',
  },
  achievementPointsValue: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#34A853',
  },
  achievementPointsLabel: {
    fontSize: 10,
    color: '#34A853',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  modalTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  modalTypeIcon: {
    fontSize: 16,
  },
  modalType: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
  },
  closeButton: {
    padding: 4,
  },
  closeIcon: {
    fontSize: 18,
    color: '#666666',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  modalDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 16,
    lineHeight: 18,
  },
  modalProgress: {
    marginBottom: 16,
  },
  modalProgressBar: {
    width: '100%',
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    marginBottom: 8,
  },
  modalProgressFill: {
    height: '100%',
    backgroundColor: '#FF4444',
    borderRadius: 4,
  },
  modalProgressText: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
  modalReward: {
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  modalRewardLabel: {
    fontSize: 12,
    color: '#F57C00',
    marginBottom: 4,
  },
  modalRewardValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#F57C00',
  },
  modalSection: {
    marginBottom: 16,
  },
  modalSectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  modalRequirement: {
    fontSize: 12,
    color: '#666666',
    lineHeight: 16,
    marginBottom: 4,
  },
  modalTip: {
    fontSize: 12,
    color: '#666666',
    lineHeight: 16,
    marginBottom: 4,
  },
  startChallengeButton: {
    backgroundColor: '#FF4444',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  startChallengeButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});