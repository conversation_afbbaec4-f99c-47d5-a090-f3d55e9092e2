import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  FlatList,
  TextInput,
  SafeAreaView,
} from 'react-native';
import * as Haptics from 'expo-haptics';

import { useLocation } from '@/hooks/useLocation';

interface Address {
  id: string;
  title: string;
  subtitle: string;
  type: 'home' | 'work' | 'other' | 'current';
  icon: string;
}

interface AddressModalProps {
  visible: boolean;
  onClose: () => void;
}

const SAVED_ADDRESSES: Address[] = [
  {
    id: '1',
    title: 'Home',
    subtitle: '123 Main St, San Francisco, CA',
    type: 'home',
    icon: '🏠',
  },
  {
    id: '2',
    title: 'Work',
    subtitle: '456 Market St, San Francisco, CA',
    type: 'work',
    icon: '🏢',
  },
  {
    id: '3',
    title: 'Mom\'s House',
    subtitle: '789 Oak Ave, Oakland, CA',
    type: 'other',
    icon: '📍',
  },
];

export default function AddressModal({ visible, onClose }: AddressModalProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const { userLocation, getCurrentLocation } = useLocation();

  const handleAddressSelect = (address: Address) => {
    Haptics.selectionAsync();
    console.log('Selected address:', address.title);
    onClose();
  };

  const handleCurrentLocation = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    getCurrentLocation();
    onClose();
  };

  const renderAddress = ({ item }: { item: Address }) => (
    <TouchableOpacity
      style={styles.addressItem}
      onPress={() => handleAddressSelect(item)}
      activeOpacity={0.7}
    >
      <View style={styles.addressIcon}>
        <Text style={styles.iconText}>{item.icon}</Text>
      </View>
      <View style={styles.addressInfo}>
        <Text style={styles.addressTitle}>{item.title}</Text>
        <Text style={styles.addressSubtitle}>{item.subtitle}</Text>
      </View>
      <Text style={styles.arrow}>→</Text>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeText}>✕</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Delivery Address</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Text style={styles.searchIcon}>🔍</Text>
            <TextInput
              style={styles.searchInput}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Search for area, street name..."
              placeholderTextColor="#999999"
            />
          </View>
        </View>

        {/* Current Location */}
        <TouchableOpacity
          style={styles.currentLocationButton}
          onPress={handleCurrentLocation}
          activeOpacity={0.7}
        >
          <View style={styles.currentLocationIcon}>
            <Text style={styles.iconText}>📍</Text>
          </View>
          <View style={styles.currentLocationInfo}>
            <Text style={styles.currentLocationTitle}>Use current location</Text>
            <Text style={styles.currentLocationSubtitle}>
              {userLocation?.address?.street || 'Enable location services'}
            </Text>
          </View>
          <Text style={styles.arrow}>→</Text>
        </TouchableOpacity>

        {/* Saved Addresses */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Saved Addresses</Text>
          <FlatList
            data={SAVED_ADDRESSES}
            renderItem={renderAddress}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
          />
        </View>

        {/* Add New Address */}
        <TouchableOpacity style={styles.addAddressButton} activeOpacity={0.7}>
          <View style={styles.addIcon}>
            <Text style={styles.addIconText}>+</Text>
          </View>
          <Text style={styles.addAddressText}>Add new address</Text>
        </TouchableOpacity>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  closeButton: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeText: {
    fontSize: 18,
    color: '#666666',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  placeholder: {
    width: 32,
  },
  searchContainer: {
    padding: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 44,
  },
  searchIcon: {
    fontSize: 16,
    marginRight: 8,
    color: '#666666',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#2C2C2C',
  },
  currentLocationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  currentLocationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FF444420',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  iconText: {
    fontSize: 16,
  },
  currentLocationInfo: {
    flex: 1,
  },
  currentLocationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF4444',
    marginBottom: 2,
  },
  currentLocationSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  arrow: {
    fontSize: 16,
    color: '#CCCCCC',
  },
  section: {
    flex: 1,
    paddingTop: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  addressItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  addressIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  addressInfo: {
    flex: 1,
  },
  addressTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  addressSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  addAddressButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  addIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  addIconText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  addAddressText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FF4444',
  },
});