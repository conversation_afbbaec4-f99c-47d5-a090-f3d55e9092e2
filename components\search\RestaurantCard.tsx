import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Animated,
} from 'react-native';
import { Image } from 'expo-image';
import * as Haptics from 'expo-haptics';

const { width } = Dimensions.get('window');

interface Restaurant {
  id: string;
  name: string;
  cuisine: string;
  rating: number;
  deliveryTime: string;
  deliveryFee: number;
  image: string;
  isOpen: boolean;
  distance: number;
  categories: string[];
}

interface RestaurantCardProps {
  restaurant: Restaurant;
  viewMode: 'list' | 'grid' | 'map';
  index: number;
}

export default function RestaurantCard({ restaurant, viewMode, index }: RestaurantCardProps) {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Staggered entrance animation
    const delay = index * 100;
    
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 300,
        delay,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 300,
        delay,
        useNativeDriver: true,
      }),
    ]).start();
  }, [index]);

  const handlePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    console.log('Restaurant pressed:', restaurant.name);
  };

  const handleFavoritePress = () => {
    Haptics.selectionAsync();
    console.log('Favorite toggled for:', restaurant.name);
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Text key={i} style={styles.star}>★</Text>);
    }
    
    if (hasHalfStar) {
      stars.push(<Text key="half" style={styles.star}>☆</Text>);
    }

    return stars;
  };

  if (viewMode === 'grid') {
    return (
      <Animated.View 
        style={[
          styles.gridCard,
          {
            transform: [{ scale: scaleAnim }],
            opacity: opacityAnim,
          }
        ]}
      >
        <TouchableOpacity onPress={handlePress} activeOpacity={0.9}>
          <View style={styles.gridImageContainer}>
            <Image source={{ uri: restaurant.image }} style={styles.gridImage} />
            <TouchableOpacity 
              style={styles.favoriteButton}
              onPress={handleFavoritePress}
            >
              <Text style={styles.favoriteIcon}>♡</Text>
            </TouchableOpacity>
            {!restaurant.isOpen && (
              <View style={styles.closedOverlay}>
                <Text style={styles.closedText}>Closed</Text>
              </View>
            )}
          </View>
          
          <View style={styles.gridContent}>
            <Text style={styles.gridName} numberOfLines={1}>{restaurant.name}</Text>
            <View style={styles.gridRating}>
              {renderStars(restaurant.rating)}
              <Text style={styles.gridRatingText}>{restaurant.rating}</Text>
            </View>
            <Text style={styles.gridCuisine} numberOfLines={1}>{restaurant.cuisine}</Text>
            <Text style={styles.gridDelivery}>{restaurant.deliveryTime}</Text>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View 
      style={[
        styles.listCard,
        {
          transform: [{ scale: scaleAnim }],
          opacity: opacityAnim,
        }
      ]}
    >
      <TouchableOpacity onPress={handlePress} activeOpacity={0.9}>
        <View style={styles.listContent}>
          <View style={styles.listImageContainer}>
            <Image source={{ uri: restaurant.image }} style={styles.listImage} />
            {!restaurant.isOpen && (
              <View style={styles.closedOverlay}>
                <Text style={styles.closedText}>Closed</Text>
              </View>
            )}
          </View>
          
          <View style={styles.listInfo}>
            <View style={styles.listHeader}>
              <Text style={styles.listName} numberOfLines={1}>{restaurant.name}</Text>
              <TouchableOpacity 
                style={styles.favoriteButton}
                onPress={handleFavoritePress}
              >
                <Text style={styles.favoriteIcon}>♡</Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.listRating}>
              {renderStars(restaurant.rating)}
              <Text style={styles.listRatingText}>{restaurant.rating}</Text>
              <Text style={styles.listReviews}>(500+ reviews)</Text>
            </View>
            
            <Text style={styles.listCuisine}>{restaurant.cuisine} • $$</Text>
            
            <View style={styles.listDeliveryInfo}>
              <Text style={styles.listDeliveryTime}>{restaurant.deliveryTime}</Text>
              <Text style={styles.listDeliveryFee}>
                {restaurant.deliveryFee === 0 ? 'Free delivery' : `$${restaurant.deliveryFee} delivery`}
              </Text>
            </View>
            
            {restaurant.categories.includes('Offers') && (
              <View style={styles.offerBadge}>
                <Text style={styles.offerText}>20% OFF</Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  // List View Styles
  listCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginVertical: 6,
    borderRadius: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  listContent: {
    flexDirection: 'row',
    padding: 12,
  },
  listImageContainer: {
    position: 'relative',
  },
  listImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  listInfo: {
    flex: 1,
    marginLeft: 12,
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  listName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    flex: 1,
  },
  listRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  listRatingText: {
    fontSize: 14,
    color: '#2C2C2C',
    fontWeight: '500',
    marginLeft: 4,
  },
  listReviews: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 4,
  },
  listCuisine: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  listDeliveryInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  listDeliveryTime: {
    fontSize: 14,
    color: '#2C2C2C',
    fontWeight: '500',
  },
  listDeliveryFee: {
    fontSize: 14,
    color: '#666666',
  },

  // Grid View Styles
  gridCard: {
    backgroundColor: '#FFFFFF',
    margin: 8,
    borderRadius: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    width: (width - 48) / 2,
  },
  gridImageContainer: {
    position: 'relative',
  },
  gridImage: {
    width: '100%',
    height: 120,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  gridContent: {
    padding: 12,
  },
  gridName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  gridRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  gridRatingText: {
    fontSize: 12,
    color: '#2C2C2C',
    fontWeight: '500',
    marginLeft: 4,
  },
  gridCuisine: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 4,
  },
  gridDelivery: {
    fontSize: 12,
    color: '#2C2C2C',
    fontWeight: '500',
  },

  // Common Styles
  star: {
    fontSize: 12,
    color: '#FBBC04',
  },
  favoriteButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoriteIcon: {
    fontSize: 16,
    color: '#FF4444',
  },
  closedOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  closedText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  offerBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#34A853',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  offerText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});