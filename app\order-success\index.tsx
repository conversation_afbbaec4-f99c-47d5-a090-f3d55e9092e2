import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

import OrderSuccessHeader from '@/components/order/OrderSuccessHeader';
import OrderDetails from '@/components/order/OrderDetails';
import DeliveryTracking from '@/components/order/DeliveryTracking';
import DeliveryPersonInfo from '@/components/order/DeliveryPersonInfo';
import OrderActions from '@/components/order/OrderActions';

export interface OrderStatus {
  id: string;
  title: string;
  description: string;
  status: 'completed' | 'active' | 'pending';
  timestamp?: Date;
  estimatedTime?: string;
  icon: string;
}

export interface DeliveryPerson {
  id: string;
  name: string;
  photo: string;
  rating: number;
  vehicle: {
    type: 'bike' | 'car' | 'scooter';
    licensePlate: string;
  };
  location: {
    latitude: number;
    longitude: number;
  };
  phone: string;
}

export interface OrderInfo {
  id: string;
  orderNumber: string;
  restaurant: {
    id: string;
    name: string;
    image: string;
    phone: string;
  };
  items: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
    customizations?: string[];
  }>;
  total: number;
  paymentMethod: string;
  deliveryAddress: string;
  orderTime: Date;
  estimatedDelivery: string;
  currentStatus: string;
}

export default function OrderSuccessScreen() {
  const [orderInfo] = useState<OrderInfo>({
    id: '1',
    orderNumber: 'FW123456',
    restaurant: {
      id: '1',
      name: 'Burger Palace',
      image: '🍔',
      phone: '+****************',
    },
    items: [
      {
        id: '1',
        name: 'Classic Burger',
        quantity: 2,
        price: 12.99,
        customizations: ['No onions', 'Extra cheese'],
      },
      {
        id: '2',
        name: 'French Fries',
        quantity: 1,
        price: 4.99,
      },
      {
        id: '3',
        name: 'Coca Cola',
        quantity: 2,
        price: 2.99,
      },
    ],
    total: 33.96,
    paymentMethod: 'Visa ****4242',
    deliveryAddress: '123 Main St, City, State 12345',
    orderTime: new Date(),
    estimatedDelivery: '25-30 minutes',
    currentStatus: 'preparing',
  });

  const [orderStatuses, setOrderStatuses] = useState<OrderStatus[]>([
    {
      id: 'confirmed',
      title: 'Order Confirmed',
      description: 'Restaurant received your order',
      status: 'completed',
      timestamp: new Date(),
      icon: '✅',
    },
    {
      id: 'preparing',
      title: 'Preparing Your Order',
      description: 'Chef is cooking your food',
      status: 'active',
      estimatedTime: '10-15 min',
      icon: '🍳',
    },
    {
      id: 'out_for_delivery',
      title: 'Out for Delivery',
      description: 'Driver is on the way',
      status: 'pending',
      estimatedTime: '15-20 min',
      icon: '🚚',
    },
    {
      id: 'delivered',
      title: 'Delivered',
      description: 'Enjoy your meal!',
      status: 'pending',
      icon: '📦',
    },
  ]);

  const [deliveryPerson, setDeliveryPerson] = useState<DeliveryPerson | null>(null);
  const [showTracking, setShowTracking] = useState(false);

  // Simulate order status updates
  useEffect(() => {
    const statusUpdateInterval = setInterval(() => {
      setOrderStatuses(prevStatuses => {
        const currentActiveIndex = prevStatuses.findIndex(s => s.status === 'active');
        if (currentActiveIndex < prevStatuses.length - 1) {
          const newStatuses = [...prevStatuses];
          
          // Complete current status
          newStatuses[currentActiveIndex] = {
            ...newStatuses[currentActiveIndex],
            status: 'completed',
            timestamp: new Date(),
          };
          
          // Activate next status
          if (currentActiveIndex + 1 < newStatuses.length) {
            newStatuses[currentActiveIndex + 1] = {
              ...newStatuses[currentActiveIndex + 1],
              status: 'active',
            };
            
            // Assign delivery person when out for delivery
            if (newStatuses[currentActiveIndex + 1].id === 'out_for_delivery') {
              setDeliveryPerson({
                id: '1',
                name: 'John Smith',
                photo: '👨‍🚴',
                rating: 4.8,
                vehicle: {
                  type: 'bike',
                  licensePlate: 'ABC123',
                },
                location: {
                  latitude: 37.7749,
                  longitude: -122.4194,
                },
                phone: '+****************',
              });
              setShowTracking(true);
            }
          }
          
          return newStatuses;
        }
        return prevStatuses;
      });
    }, 15000); // Update every 15 seconds for demo

    return () => clearInterval(statusUpdateInterval);
  }, []);

  const handleTrackOrder = () => {
    router.push('/order-tracking');
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleContactSupport = () => {
    Alert.alert(
      'Contact Support',
      'How would you like to get help?',
      [
        { text: 'Live Chat', onPress: () => console.log('Open chat') },
        { text: 'Call Support', onPress: () => console.log('Call support') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleReorder = () => {
    Alert.alert(
      'Reorder',
      'Add the same items to your cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Add to Cart', 
          onPress: () => {
            router.push('/cart');
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }
        },
      ]
    );
  };

  const handleShareOrder = () => {
    Alert.alert('Share Order', 'Tracking link copied to clipboard!');
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Success Header */}
        <OrderSuccessHeader
          orderNumber={orderInfo.orderNumber}
          estimatedDelivery={orderInfo.estimatedDelivery}
        />

        {/* Order Details */}
        <OrderDetails orderInfo={orderInfo} />

        {/* Delivery Tracking */}
        <DeliveryTracking
          orderStatuses={orderStatuses}
          showMap={showTracking}
          deliveryAddress={orderInfo.deliveryAddress}
          restaurantName={orderInfo.restaurant.name}
        />

        {/* Delivery Person Info */}
        {deliveryPerson && (
          <DeliveryPersonInfo
            deliveryPerson={deliveryPerson}
            estimatedArrival="5-10 min"
          />
        )}

        {/* Action Buttons */}
        <OrderActions
          onTrackOrder={handleTrackOrder}
          onContactSupport={handleContactSupport}
          onReorder={handleReorder}
          onShareOrder={handleShareOrder}
          showTracking={showTracking}
          orderStatus={orderInfo.currentStatus}
        />

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  bottomSpacing: {
    height: 40,
  },
});