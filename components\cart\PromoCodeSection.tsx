import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface PromoCodeSectionProps {
  appliedPromo: string | null;
  onPromoApplied: (code: string | null) => void;
}

interface Coupon {
  code: string;
  title: string;
  description: string;
  discount: string;
  minOrder?: number;
  expiryDate: string;
  terms: string;
}

const availableCoupons: Coupon[] = [
  {
    code: 'SAVE20',
    title: '20% Off Your Order',
    description: 'Get 20% off on orders above $25',
    discount: '20% OFF',
    minOrder: 25,
    expiryDate: 'Dec 31, 2024',
    terms: 'Valid on orders above $25. Cannot be combined with other offers.',
  },
  {
    code: 'FIRST10',
    title: 'First Order Special',
    description: 'Get $10 off your first order',
    discount: '$10 OFF',
    minOrder: 20,
    expiryDate: 'Dec 31, 2024',
    terms: 'Valid for new customers only. Maximum discount $10.',
  },
  {
    code: 'FREESHIP',
    title: 'Free Delivery',
    description: 'Free delivery on any order',
    discount: 'FREE DELIVERY',
    expiryDate: 'Dec 31, 2024',
    terms: 'Free standard delivery. Express delivery charges apply.',
  },
];

export default function PromoCodeSection({ appliedPromo, onPromoApplied }: PromoCodeSectionProps) {
  const [promoCode, setPromoCode] = useState('');
  const [showOffersModal, setShowOffersModal] = useState(false);
  const [isApplying, setIsApplying] = useState(false);

  const handleApplyPromo = async () => {
    if (!promoCode.trim()) return;

    setIsApplying(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    const validCoupon = availableCoupons.find(
      coupon => coupon.code.toLowerCase() === promoCode.toLowerCase()
    );

    if (validCoupon) {
      onPromoApplied(validCoupon.code);
      setPromoCode('');
      Alert.alert('Success!', `${validCoupon.title} applied successfully!`);
    } else {
      Alert.alert('Invalid Code', 'Please enter a valid promo code.');
    }

    setIsApplying(false);
  };

  const handleRemovePromo = () => {
    Haptics.selectionAsync();
    onPromoApplied(null);
  };

  const handleApplyCoupon = (coupon: Coupon) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onPromoApplied(coupon.code);
    setShowOffersModal(false);
    Alert.alert('Success!', `${coupon.title} applied successfully!`);
  };

  const CouponCard = ({ coupon }: { coupon: Coupon }) => (
    <View style={styles.couponCard}>
      <View style={styles.couponHeader}>
        <View style={styles.discountBadge}>
          <Text style={styles.discountText}>{coupon.discount}</Text>
        </View>
        <View style={styles.couponDetails}>
          <Text style={styles.couponTitle}>{coupon.title}</Text>
          <Text style={styles.couponDescription}>{coupon.description}</Text>
          <Text style={styles.couponExpiry}>Expires: {coupon.expiryDate}</Text>
        </View>
      </View>
      
      <Text style={styles.couponTerms}>{coupon.terms}</Text>
      
      <TouchableOpacity
        style={[
          styles.applyCouponButton,
          appliedPromo === coupon.code && styles.appliedCouponButton,
        ]}
        onPress={() => handleApplyCoupon(coupon)}
        disabled={appliedPromo === coupon.code}
        activeOpacity={0.7}
      >
        <Text style={[
          styles.applyCouponText,
          appliedPromo === coupon.code && styles.appliedCouponText,
        ]}>
          {appliedPromo === coupon.code ? '✓ Applied' : 'Apply'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Promo Code</Text>
      
      {appliedPromo ? (
        <View style={styles.appliedPromoContainer}>
          <View style={styles.appliedPromoContent}>
            <Text style={styles.appliedPromoIcon}>✓</Text>
            <View style={styles.appliedPromoDetails}>
              <Text style={styles.appliedPromoCode}>{appliedPromo}</Text>
              <Text style={styles.appliedPromoText}>Coupon applied successfully!</Text>
            </View>
          </View>
          <TouchableOpacity
            onPress={handleRemovePromo}
            style={styles.removePromoButton}
            activeOpacity={0.7}
          >
            <Text style={styles.removePromoText}>Remove</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.promoInputContainer}>
          <TextInput
            style={styles.promoInput}
            placeholder="Enter promo code"
            value={promoCode}
            onChangeText={setPromoCode}
            autoCapitalize="characters"
            returnKeyType="done"
            onSubmitEditing={handleApplyPromo}
          />
          <TouchableOpacity
            style={[
              styles.applyButton,
              (!promoCode.trim() || isApplying) && styles.applyButtonDisabled,
            ]}
            onPress={handleApplyPromo}
            disabled={!promoCode.trim() || isApplying}
            activeOpacity={0.8}
          >
            <Text style={styles.applyButtonText}>
              {isApplying ? 'Applying...' : 'Apply'}
            </Text>
          </TouchableOpacity>
        </View>
      )}
      
      <TouchableOpacity
        style={styles.viewOffersButton}
        onPress={() => setShowOffersModal(true)}
        activeOpacity={0.7}
      >
        <Text style={styles.viewOffersText}>View all offers</Text>
      </TouchableOpacity>

      {/* Offers Modal */}
      <Modal
        visible={showOffersModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Available Offers</Text>
            <TouchableOpacity
              onPress={() => setShowOffersModal(false)}
              style={styles.closeButton}
              activeOpacity={0.7}
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
            {availableCoupons.map((coupon) => (
              <CouponCard key={coupon.code} coupon={coupon} />
            ))}
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#F0F0F0',
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  appliedPromoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F0FDF4',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#22C55E',
    marginBottom: 12,
  },
  appliedPromoContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  appliedPromoIcon: {
    fontSize: 16,
    color: '#22C55E',
    marginRight: 8,
  },
  appliedPromoDetails: {
    flex: 1,
  },
  appliedPromoCode: {
    fontSize: 14,
    fontWeight: '600',
    color: '#22C55E',
  },
  appliedPromoText: {
    fontSize: 12,
    color: '#16A34A',
  },
  removePromoButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  removePromoText: {
    fontSize: 14,
    color: '#EF4444',
    fontWeight: '500',
  },
  promoInputContainer: {
    flexDirection: 'row',
    marginBottom: 12,
    gap: 8,
  },
  promoInput: {
    flex: 1,
    height: 44,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 14,
    backgroundColor: '#FAFAFA',
  },
  applyButton: {
    paddingHorizontal: 20,
    height: 44,
    backgroundColor: '#FF4444',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  applyButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  applyButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  viewOffersButton: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  viewOffersText: {
    fontSize: 14,
    color: '#FF4444',
    fontWeight: '500',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#666666',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  couponCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#F0F0F0',
    padding: 16,
    marginBottom: 12,
  },
  couponHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  discountBadge: {
    backgroundColor: '#FF4444',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 12,
    alignSelf: 'flex-start',
  },
  discountText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  couponDetails: {
    flex: 1,
  },
  couponTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  couponDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  couponExpiry: {
    fontSize: 12,
    color: '#999999',
  },
  couponTerms: {
    fontSize: 12,
    color: '#666666',
    lineHeight: 16,
    marginBottom: 12,
  },
  applyCouponButton: {
    backgroundColor: '#FF4444',
    borderRadius: 8,
    paddingVertical: 10,
    alignItems: 'center',
  },
  appliedCouponButton: {
    backgroundColor: '#22C55E',
  },
  applyCouponText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  appliedCouponText: {
    color: '#FFFFFF',
  },
});