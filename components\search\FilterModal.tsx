import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Switch,
} from 'react-native';
import Slider from '@react-native-community/slider';
import * as Haptics from 'expo-haptics';

interface Filter {
  id: string;
  label: string;
  active: boolean;
  count?: number;
}

interface FilterModalProps {
  visible: boolean;
  onClose: () => void;
  filters: Filter[];
  onFiltersChange: (filters: Filter[]) => void;
}

interface FilterSection {
  id: string;
  title: string;
  expanded: boolean;
}

const SORT_OPTIONS = [
  { id: 'relevance', label: 'Relevance', selected: true },
  { id: 'rating', label: 'Rating', selected: false },
  { id: 'delivery_time', label: 'Delivery Time', selected: false },
  { id: 'price_low', label: 'Price: Low to High', selected: false },
  { id: 'price_high', label: 'Price: High to Low', selected: false },
];

const CUISINES = [
  { id: 'italian', label: 'Italian', icon: '🍝', selected: false },
  { id: 'chinese', label: 'Chinese', icon: '🥡', selected: false },
  { id: 'indian', label: 'Indian', icon: '🍛', selected: false },
  { id: 'mexican', label: 'Mexican', icon: '🌮', selected: false },
  { id: 'thai', label: 'Thai', icon: '🍜', selected: false },
  { id: 'japanese', label: 'Japanese', icon: '🍣', selected: false },
  { id: 'american', label: 'American', icon: '🍔', selected: false },
  { id: 'mediterranean', label: 'Mediterranean', icon: '🥙', selected: false },
];

const RATING_OPTIONS = [
  { id: '4_plus', label: '4.0 & above', stars: 4, selected: false },
  { id: '3_plus', label: '3.0 & above', stars: 3, selected: false },
  { id: '2_plus', label: '2.0 & above', stars: 2, selected: false },
];

const OFFER_TYPES = [
  { id: 'free_delivery', label: 'Free Delivery', enabled: false },
  { id: 'discount', label: 'Discounts Available', enabled: false },
  { id: 'buy_one_get_one', label: 'Buy 1 Get 1', enabled: false },
  { id: 'cashback', label: 'Cashback Offers', enabled: false },
];

export default function FilterModal({ visible, onClose, filters, onFiltersChange }: FilterModalProps) {
  const [sections, setSections] = useState<FilterSection[]>([
    { id: 'sort', title: 'Sort By', expanded: true },
    { id: 'cuisine', title: 'Cuisine', expanded: false },
    { id: 'price', title: 'Price Range', expanded: false },
    { id: 'rating', title: 'Rating', expanded: false },
    { id: 'delivery', title: 'Delivery Time', expanded: false },
    { id: 'offers', title: 'Offers', expanded: false },
    { id: 'dietary', title: 'Dietary Preferences', expanded: false },
  ]);

  const [sortOptions, setSortOptions] = useState(SORT_OPTIONS);
  const [cuisines, setCuisines] = useState(CUISINES);
  const [ratingOptions, setRatingOptions] = useState(RATING_OPTIONS);
  const [offerTypes, setOfferTypes] = useState(OFFER_TYPES);
  const [priceRange, setPriceRange] = useState([10, 50]);
  const [deliveryTime, setDeliveryTime] = useState([15, 60]);
  const [isVegetarian, setIsVegetarian] = useState(false);
  const [isVegan, setIsVegan] = useState(false);

  const toggleSection = (sectionId: string) => {
    Haptics.selectionAsync();
    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? { ...section, expanded: !section.expanded }
        : section
    ));
  };

  const handleSortSelect = (optionId: string) => {
    Haptics.selectionAsync();
    setSortOptions(prev => prev.map(option => ({
      ...option,
      selected: option.id === optionId
    })));
  };

  const handleCuisineToggle = (cuisineId: string) => {
    Haptics.selectionAsync();
    setCuisines(prev => prev.map(cuisine => 
      cuisine.id === cuisineId 
        ? { ...cuisine, selected: !cuisine.selected }
        : cuisine
    ));
  };

  const handleRatingSelect = (ratingId: string) => {
    Haptics.selectionAsync();
    setRatingOptions(prev => prev.map(option => ({
      ...option,
      selected: option.id === ratingId
    })));
  };

  const handleOfferToggle = (offerId: string) => {
    Haptics.selectionAsync();
    setOfferTypes(prev => prev.map(offer => 
      offer.id === offerId 
        ? { ...offer, enabled: !offer.enabled }
        : offer
    ));
  };

  const clearAllFilters = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setSortOptions(SORT_OPTIONS);
    setCuisines(CUISINES);
    setRatingOptions(RATING_OPTIONS);
    setOfferTypes(OFFER_TYPES);
    setPriceRange([10, 50]);
    setDeliveryTime([15, 60]);
    setIsVegetarian(false);
    setIsVegan(false);
  };

  const applyFilters = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    // Apply filter logic here
    onClose();
  };

  const renderStars = (count: number) => {
    return Array.from({ length: count }, (_, i) => (
      <Text key={i} style={styles.star}>★</Text>
    ));
  };

  const renderSection = (section: FilterSection) => {
    const isExpanded = section.expanded;

    return (
      <View key={section.id} style={styles.section}>
        <TouchableOpacity
          style={styles.sectionHeader}
          onPress={() => toggleSection(section.id)}
          activeOpacity={0.7}
        >
          <Text style={styles.sectionTitle}>{section.title}</Text>
          <Text style={[styles.expandIcon, isExpanded && styles.expandIconRotated]}>
            ▼
          </Text>
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.sectionContent}>
            {section.id === 'sort' && (
              <View>
                {sortOptions.map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={styles.radioOption}
                    onPress={() => handleSortSelect(option.id)}
                    activeOpacity={0.7}
                  >
                    <View style={[styles.radioButton, option.selected && styles.radioButtonSelected]}>
                      {option.selected && <View style={styles.radioButtonInner} />}
                    </View>
                    <Text style={styles.radioLabel}>{option.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {section.id === 'cuisine' && (
              <View style={styles.cuisineGrid}>
                {cuisines.map((cuisine) => (
                  <TouchableOpacity
                    key={cuisine.id}
                    style={[styles.cuisineChip, cuisine.selected && styles.cuisineChipSelected]}
                    onPress={() => handleCuisineToggle(cuisine.id)}
                    activeOpacity={0.7}
                  >
                    <Text style={styles.cuisineIcon}>{cuisine.icon}</Text>
                    <Text style={[
                      styles.cuisineLabel,
                      cuisine.selected && styles.cuisineLabelSelected
                    ]}>
                      {cuisine.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {section.id === 'price' && (
              <View style={styles.sliderContainer}>
                <Text style={styles.sliderLabel}>
                  Price Range: ${priceRange[0]} - ${priceRange[1]}
                </Text>
                <View style={styles.priceSymbols}>
                  <Text style={styles.priceSymbol}>$</Text>
                  <Text style={styles.priceSymbol}>$$</Text>
                  <Text style={styles.priceSymbol}>$$$</Text>
                  <Text style={styles.priceSymbol}>$$$$</Text>
                </View>
                <Slider
                  style={styles.slider}
                  minimumValue={5}
                  maximumValue={100}
                  value={priceRange[1]}
                  onValueChange={(value) => setPriceRange([priceRange[0], value])}
                  minimumTrackTintColor="#FF4444"
                  maximumTrackTintColor="#E0E0E0"
                  thumbStyle={styles.sliderThumb}
                />
              </View>
            )}

            {section.id === 'rating' && (
              <View>
                {ratingOptions.map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={styles.ratingOption}
                    onPress={() => handleRatingSelect(option.id)}
                    activeOpacity={0.7}
                  >
                    <View style={[styles.checkbox, option.selected && styles.checkboxSelected]}>
                      {option.selected && <Text style={styles.checkmark}>✓</Text>}
                    </View>
                    <View style={styles.ratingStars}>
                      {renderStars(option.stars)}
                    </View>
                    <Text style={styles.ratingLabel}>{option.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {section.id === 'delivery' && (
              <View style={styles.sliderContainer}>
                <Text style={styles.sliderLabel}>
                  Delivery Time: {deliveryTime[0]} - {deliveryTime[1]} minutes
                </Text>
                <Slider
                  style={styles.slider}
                  minimumValue={10}
                  maximumValue={90}
                  value={deliveryTime[1]}
                  onValueChange={(value) => setDeliveryTime([deliveryTime[0], value])}
                  minimumTrackTintColor="#FF4444"
                  maximumTrackTintColor="#E0E0E0"
                  thumbStyle={styles.sliderThumb}
                />
              </View>
            )}

            {section.id === 'offers' && (
              <View>
                {offerTypes.map((offer) => (
                  <View key={offer.id} style={styles.switchOption}>
                    <Text style={styles.switchLabel}>{offer.label}</Text>
                    <Switch
                      value={offer.enabled}
                      onValueChange={() => handleOfferToggle(offer.id)}
                      trackColor={{ false: '#E0E0E0', true: '#FF444440' }}
                      thumbColor={offer.enabled ? '#FF4444' : '#FFFFFF'}
                    />
                  </View>
                ))}
              </View>
            )}

            {section.id === 'dietary' && (
              <View>
                <View style={styles.switchOption}>
                  <Text style={styles.switchLabel}>Vegetarian</Text>
                  <Switch
                    value={isVegetarian}
                    onValueChange={setIsVegetarian}
                    trackColor={{ false: '#E0E0E0', true: '#34A85340' }}
                    thumbColor={isVegetarian ? '#34A853' : '#FFFFFF'}
                  />
                </View>
                <View style={styles.switchOption}>
                  <Text style={styles.switchLabel}>Vegan</Text>
                  <Switch
                    value={isVegan}
                    onValueChange={setIsVegan}
                    trackColor={{ false: '#E0E0E0', true: '#34A85340' }}
                    thumbColor={isVegan ? '#34A853' : '#FFFFFF'}
                  />
                </View>
              </View>
            )}
          </View>
        )}
      </View>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={clearAllFilters} style={styles.clearButton}>
            <Text style={styles.clearText}>Clear All</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Filters</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeText}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {sections.map(renderSection)}
        </ScrollView>

        {/* Apply Button */}
        <View style={styles.footer}>
          <TouchableOpacity style={styles.applyButton} onPress={applyFilters}>
            <Text style={styles.applyButtonText}>Apply Filters</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  clearButton: {
    padding: 4,
  },
  clearText: {
    fontSize: 16,
    color: '#FF4444',
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  closeButton: {
    padding: 4,
  },
  closeText: {
    fontSize: 18,
    color: '#666666',
  },
  content: {
    flex: 1,
  },
  section: {
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  expandIcon: {
    fontSize: 12,
    color: '#666666',
    transform: [{ rotate: '0deg' }],
  },
  expandIconRotated: {
    transform: [{ rotate: '180deg' }],
  },
  sectionContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    borderColor: '#FF4444',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#FF4444',
  },
  radioLabel: {
    fontSize: 16,
    color: '#2C2C2C',
  },
  cuisineGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  cuisineChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  cuisineChipSelected: {
    backgroundColor: '#FF4444',
  },
  cuisineIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  cuisineLabel: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  cuisineLabelSelected: {
    color: '#FFFFFF',
  },
  sliderContainer: {
    paddingVertical: 8,
  },
  sliderLabel: {
    fontSize: 16,
    color: '#2C2C2C',
    fontWeight: '500',
    marginBottom: 16,
  },
  priceSymbols: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  priceSymbol: {
    fontSize: 14,
    color: '#666666',
  },
  slider: {
    width: '100%',
    height: 40,
  },
  sliderThumb: {
    backgroundColor: '#FF4444',
    width: 20,
    height: 20,
  },
  ratingOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    borderColor: '#FF4444',
    backgroundColor: '#FF4444',
  },
  checkmark: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  ratingStars: {
    flexDirection: 'row',
    marginRight: 8,
  },
  star: {
    fontSize: 14,
    color: '#FBBC04',
  },
  ratingLabel: {
    fontSize: 16,
    color: '#2C2C2C',
  },
  switchOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  switchLabel: {
    fontSize: 16,
    color: '#2C2C2C',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  applyButton: {
    backgroundColor: '#FF4444',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});