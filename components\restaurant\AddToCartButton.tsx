import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  isVeg: boolean;
  spiceLevel: number;
  isBestseller: boolean;
  category: string;
}

interface AddToCartButtonProps {
  item: MenuItem;
  quantity: number;
  onAdd: () => void;
  onUpdateQuantity: (quantity: number) => void;
}

export default function AddToCartButton({
  item,
  quantity,
  onAdd,
  onUpdateQuantity,
}: AddToCartButtonProps) {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const widthAnim = useRef(new Animated.Value(60)).current;

  useEffect(() => {
    // Animate width when quantity changes
    Animated.timing(widthAnim, {
      toValue: quantity > 0 ? 100 : 60,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [quantity]);

  const handleAdd = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    // Bounce animation
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 1.1,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
    
    onAdd();
  };

  const handleDecrease = () => {
    Haptics.selectionAsync();
    const newQuantity = Math.max(0, quantity - 1);
    onUpdateQuantity(newQuantity);
  };

  const handleIncrease = () => {
    Haptics.selectionAsync();
    onUpdateQuantity(quantity + 1);
  };

  if (quantity === 0) {
    return (
      <Animated.View
        style={[
          styles.addButton,
          {
            transform: [{ scale: scaleAnim }],
            width: widthAnim,
          },
        ]}
      >
        <TouchableOpacity
          style={styles.addButtonTouchable}
          onPress={handleAdd}
          activeOpacity={0.8}
        >
          <Text style={styles.addButtonText}>ADD</Text>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View
      style={[
        styles.stepperContainer,
        {
          transform: [{ scale: scaleAnim }],
          width: widthAnim,
        },
      ]}
    >
      <TouchableOpacity
        style={styles.stepperButton}
        onPress={handleDecrease}
        activeOpacity={0.7}
      >
        <Text style={styles.stepperButtonText}>−</Text>
      </TouchableOpacity>
      
      <View style={styles.quantityContainer}>
        <Text style={styles.quantityText}>{quantity}</Text>
      </View>
      
      <TouchableOpacity
        style={styles.stepperButton}
        onPress={handleIncrease}
        activeOpacity={0.7}
      >
        <Text style={styles.stepperButtonText}>+</Text>
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  addButton: {
    height: 32,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#FF4444',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  addButtonTouchable: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FF4444',
  },
  stepperContainer: {
    height: 32,
    backgroundColor: '#FF4444',
    borderRadius: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  stepperButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 2,
  },
  stepperButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF4444',
  },
  quantityContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});