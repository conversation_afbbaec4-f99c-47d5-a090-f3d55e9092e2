import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface Restaurant {
  id: string;
  name: string;
  cuisine: string;
  rating: number;
  deliveryTime: string;
  deliveryFee: number;
  image: string;
  isOpen: boolean;
}

interface RestaurantInfoProps {
  restaurant: Restaurant;
}

interface InfoSection {
  id: string;
  title: string;
  expanded: boolean;
}

export default function RestaurantInfo({ restaurant }: RestaurantInfoProps) {
  const [sections, setSections] = useState<InfoSection[]>([
    { id: 'about', title: 'About', expanded: false },
    { id: 'reviews', title: 'Reviews', expanded: false },
    { id: 'location', title: 'Location', expanded: false },
    { id: 'timings', title: 'Timings', expanded: false },
    { id: 'offers', title: 'Offers', expanded: false },
  ]);

  const toggleSection = (sectionId: string) => {
    Haptics.selectionAsync();
    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? { ...section, expanded: !section.expanded }
        : section
    ));
  };

  const renderSectionContent = (sectionId: string) => {
    switch (sectionId) {
      case 'about':
        return (
          <View style={styles.sectionContent}>
            <Text style={styles.contentText}>
              Authentic Italian cuisine with fresh ingredients and traditional recipes. 
              Our chefs bring the taste of Italy to your doorstep with handmade pasta, 
              wood-fired pizzas, and classic desserts.
            </Text>
            <Text style={styles.specialtiesTitle}>Specialties:</Text>
            <Text style={styles.specialtiesText}>
              • Wood-fired Neapolitan Pizza{'\n'}
              • Handmade Fresh Pasta{'\n'}
              • Traditional Tiramisu{'\n'}
              • Authentic Risotto
            </Text>
          </View>
        );
      
      case 'reviews':
        return (
          <View style={styles.sectionContent}>
            <View style={styles.ratingBreakdown}>
              <Text style={styles.ratingTitle}>Rating Breakdown</Text>
              {[5, 4, 3, 2, 1].map(stars => (
                <View key={stars} style={styles.ratingRow}>
                  <Text style={styles.starsText}>{stars} ★</Text>
                  <View style={styles.ratingBar}>
                    <View style={[styles.ratingFill, { width: `${stars * 20}%` }]} />
                  </View>
                  <Text style={styles.ratingCount}>{Math.floor(Math.random() * 500)}</Text>
                </View>
              ))}
            </View>
            
            <View style={styles.reviewCard}>
              <View style={styles.reviewHeader}>
                <View style={styles.reviewerAvatar}>
                  <Text style={styles.reviewerInitial}>J</Text>
                </View>
                <View style={styles.reviewerInfo}>
                  <Text style={styles.reviewerName}>John D.</Text>
                  <Text style={styles.reviewDate}>2 days ago</Text>
                </View>
                <View style={styles.reviewRating}>
                  <Text style={styles.reviewStars}>★★★★★</Text>
                </View>
              </View>
              <Text style={styles.reviewText}>
                Amazing pizza! The crust was perfect and the toppings were fresh. 
                Delivery was quick and the food arrived hot. Highly recommended!
              </Text>
            </View>
          </View>
        );
      
      case 'location':
        return (
          <View style={styles.sectionContent}>
            <Text style={styles.addressText}>
              123 Main Street{'\n'}
              Downtown, City 12345{'\n'}
              Phone: (*************
            </Text>
            <View style={styles.mapPlaceholder}>
              <Text style={styles.mapText}>🗺️ Map View</Text>
            </View>
          </View>
        );
      
      case 'timings':
        return (
          <View style={styles.sectionContent}>
            {[
              { day: 'Monday', hours: '11:00 AM - 10:00 PM' },
              { day: 'Tuesday', hours: '11:00 AM - 10:00 PM' },
              { day: 'Wednesday', hours: '11:00 AM - 10:00 PM' },
              { day: 'Thursday', hours: '11:00 AM - 10:00 PM' },
              { day: 'Friday', hours: '11:00 AM - 11:00 PM' },
              { day: 'Saturday', hours: '10:00 AM - 11:00 PM' },
              { day: 'Sunday', hours: '10:00 AM - 10:00 PM' },
            ].map(({ day, hours }) => (
              <View key={day} style={styles.timingRow}>
                <Text style={styles.dayText}>{day}</Text>
                <Text style={styles.hoursText}>{hours}</Text>
              </View>
            ))}
          </View>
        );
      
      case 'offers':
        return (
          <View style={styles.sectionContent}>
            <View style={styles.offerCard}>
              <Text style={styles.offerTitle}>20% OFF</Text>
              <Text style={styles.offerDescription}>
                On orders above $25. Valid till end of month.
              </Text>
            </View>
            <View style={styles.offerCard}>
              <Text style={styles.offerTitle}>Free Delivery</Text>
              <Text style={styles.offerDescription}>
                On orders above $30. No minimum order required.
              </Text>
            </View>
          </View>
        );
      
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {sections.map((section) => (
        <View key={section.id} style={styles.section}>
          <TouchableOpacity
            style={styles.sectionHeader}
            onPress={() => toggleSection(section.id)}
            activeOpacity={0.7}
          >
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <Text style={[
              styles.expandIcon,
              section.expanded && styles.expandIconRotated
            ]}>
              ▼
            </Text>
          </TouchableOpacity>
          
          {section.expanded && renderSectionContent(section.id)}
        </View>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  section: {
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  expandIcon: {
    fontSize: 12,
    color: '#666666',
    transform: [{ rotate: '0deg' }],
  },
  expandIconRotated: {
    transform: [{ rotate: '180deg' }],
  },
  sectionContent: {
    paddingBottom: 16,
  },
  contentText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 12,
  },
  specialtiesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  specialtiesText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  ratingBreakdown: {
    marginBottom: 16,
  },
  ratingTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  starsText: {
    fontSize: 12,
    color: '#FBBC04',
    width: 30,
  },
  ratingBar: {
    flex: 1,
    height: 4,
    backgroundColor: '#F0F0F0',
    borderRadius: 2,
    marginHorizontal: 8,
  },
  ratingFill: {
    height: '100%',
    backgroundColor: '#FBBC04',
    borderRadius: 2,
  },
  ratingCount: {
    fontSize: 12,
    color: '#666666',
    width: 30,
    textAlign: 'right',
  },
  reviewCard: {
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 12,
  },
  reviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  reviewerAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  reviewerInitial: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  reviewerInfo: {
    flex: 1,
  },
  reviewerName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  reviewDate: {
    fontSize: 12,
    color: '#666666',
  },
  reviewRating: {
    marginLeft: 8,
  },
  reviewStars: {
    fontSize: 12,
    color: '#FBBC04',
  },
  reviewText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 18,
  },
  addressText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 12,
  },
  mapPlaceholder: {
    height: 120,
    backgroundColor: '#F0F0F0',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapText: {
    fontSize: 16,
    color: '#666666',
  },
  timingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  dayText: {
    fontSize: 14,
    color: '#2C2C2C',
    fontWeight: '500',
  },
  hoursText: {
    fontSize: 14,
    color: '#666666',
  },
  offerCard: {
    backgroundColor: '#34A85320',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  offerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#34A853',
    marginBottom: 4,
  },
  offerDescription: {
    fontSize: 14,
    color: '#666666',
  },
});