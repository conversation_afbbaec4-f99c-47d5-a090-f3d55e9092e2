import React, { useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Animated,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';

interface QuickAction {
  id: string;
  title: string;
  subtitle: string;
  icon: string;
  color: string;
  route?: string;
  count?: number;
}

const QUICK_ACTIONS: QuickAction[] = [
  {
    id: '1',
    title: 'Order Again',
    subtitle: 'Your recent orders',
    icon: '🔄',
    color: '#FF4444',
    route: '/orders/recent',
    count: 3,
  },
  {
    id: '2',
    title: 'Favorites',
    subtitle: 'Saved restaurants',
    icon: '❤️',
    color: '#E91E63',
    route: '/favorites',
    count: 8,
  },
  {
    id: '3',
    title: 'Offers',
    subtitle: 'Special deals',
    icon: '🎁',
    color: '#FF9800',
    route: '/offers',
    count: 5,
  },
  {
    id: '4',
    title: 'Track Order',
    subtitle: 'Live tracking',
    icon: '📍',
    color: '#4CAF50',
    route: '/orders/track',
  },
];

export default function QuickActions() {
  const handleActionPress = (action: QuickAction) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    if (action.route) {
      router.push(action.route as any);
    } else {
      console.log('Action pressed:', action.title);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        style={styles.scrollView}
      >
        {QUICK_ACTIONS.map((action, index) => (
          <QuickActionCard
            key={action.id}
            action={action}
            onPress={() => handleActionPress(action)}
            index={index}
          />
        ))}
      </ScrollView>
    </View>
  );
}

interface QuickActionCardProps {
  action: QuickAction;
  onPress: () => void;
  index: number;
}

function QuickActionCard({ action, onPress, index }: QuickActionCardProps) {
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    // Staggered entrance animation
    Animated.sequence([
      Animated.delay(index * 150),
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          friction: 8,
          tension: 100,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, []);

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={1}
      style={styles.actionContainer}
    >
      <Animated.View
        style={[
          styles.actionCard,
          {
            transform: [{ scale: scaleAnim }],
            opacity: fadeAnim,
          },
        ]}
      >
        {/* Icon Container */}
        <View style={[styles.iconContainer, { backgroundColor: `${action.color}1A` }]}>
          <Text style={styles.actionIcon}>{action.icon}</Text>
          
          {/* Count Badge */}
          {action.count && (
            <View style={[styles.countBadge, { backgroundColor: action.color }]}>
              <Text style={styles.countText}>{action.count}</Text>
            </View>
          )}
        </View>

        {/* Text Content */}
        <View style={styles.textContainer}>
          <Text style={styles.actionTitle}>{action.title}</Text>
          <Text style={styles.actionSubtitle}>{action.subtitle}</Text>
        </View>

        {/* Arrow */}
        <View style={styles.arrowContainer}>
          <Text style={styles.arrow}>→</Text>
        </View>
      </Animated.View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  scrollView: {
    paddingLeft: 16,
  },
  scrollContent: {
    paddingRight: 16,
  },
  actionContainer: {
    marginRight: 12,
  },
  actionCard: {
    width: 280,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#F0F0F0',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  actionIcon: {
    fontSize: 20,
  },
  countBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  countText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  textContainer: {
    flex: 1,
    marginLeft: 12,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  actionSubtitle: {
    fontSize: 12,
    color: '#666666',
  },
  arrowContainer: {
    marginLeft: 8,
  },
  arrow: {
    fontSize: 16,
    color: '#CCCCCC',
  },
});