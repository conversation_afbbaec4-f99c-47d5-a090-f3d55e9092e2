import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';

interface IssueCategory {
  id: string;
  title: string;
  icon: string;
  subcategories: string[];
}

interface ReportForm {
  category: string;
  subcategory: string;
  orderNumber: string;
  description: string;
  urgency: 'low' | 'medium' | 'high';
  contactMethod: 'email' | 'phone' | 'chat';
  attachments: string[];
}

export default function ReportIssueScreen() {
  const router = useRouter();
  const [formData, setFormData] = useState<ReportForm>({
    category: '',
    subcategory: '',
    orderNumber: '',
    description: '',
    urgency: 'medium',
    contactMethod: 'email',
    attachments: [],
  });

  const issueCategories: IssueCategory[] = [
    {
      id: 'order',
      title: 'Order Issues',
      icon: '🛍️',
      subcategories: [
        'Wrong items received',
        'Missing items',
        'Food quality problems',
        'Late delivery',
        'Damaged packaging',
        'Order not delivered',
      ],
    },
    {
      id: 'app',
      title: 'App Issues',
      icon: '📱',
      subcategories: [
        'App crashes or freezes',
        'Login problems',
        'Payment failures',
        'GPS/location issues',
        'Notification problems',
        'Feature not working',
      ],
    },
    {
      id: 'account',
      title: 'Account Issues',
      icon: '👤',
      subcategories: [
        'Unauthorized charges',
        'Account security concerns',
        'Profile update problems',
        'Loyalty points discrepancies',
        'Password reset issues',
        'Account deletion request',
      ],
    },
    {
      id: 'delivery',
      title: 'Delivery Issues',
      icon: '🚚',
      subcategories: [
        'Delivery partner behavior',
        'Wrong delivery address',
        'Delivery instructions ignored',
        'Unable to contact driver',
        'Delivery fee disputes',
        'Delivery time issues',
      ],
    },
  ];

  const urgencyLevels = [
    { id: 'low', label: 'Low', description: 'General inquiry', color: '#4CAF50' },
    { id: 'medium', label: 'Medium', description: 'Needs attention', color: '#FF9800' },
    { id: 'high', label: 'High', description: 'Urgent issue', color: '#F44336' },
  ];

  const contactMethods = [
    { id: 'email', label: 'Email', description: 'Response in 2-4 hours', icon: '📧' },
    { id: 'phone', label: 'Phone', description: 'Call back within 1 hour', icon: '📞' },
    { id: 'chat', label: 'Live Chat', description: 'Immediate assistance', icon: '💬' },
  ];

  const selectedCategory = issueCategories.find(cat => cat.id === formData.category);

  const handleSubmit = () => {
    if (!formData.category || !formData.subcategory || !formData.description.trim()) {
      Alert.alert('Missing Information', 'Please fill in all required fields.');
      return;
    }

    // Generate ticket number
    const ticketNumber = `TKT-${Date.now().toString().slice(-6)}`;
    
    Alert.alert(
      'Report Submitted',
      `Your report has been submitted successfully.\n\nTicket Number: ${ticketNumber}\n\nYou will receive updates via ${formData.contactMethod}.`,
      [
        {
          text: 'Track Ticket',
          onPress: () => router.push(`/support/ticket/${ticketNumber}`),
        },
        {
          text: 'OK',
          onPress: () => router.back(),
        },
      ]
    );
  };

  const renderCategorySelection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>What type of issue are you experiencing?</Text>
      
      <View style={styles.categoryGrid}>
        {issueCategories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryCard,
              formData.category === category.id && styles.categoryCardSelected,
            ]}
            onPress={() => setFormData(prev => ({ 
              ...prev, 
              category: category.id, 
              subcategory: '' 
            }))}
            activeOpacity={0.7}
          >
            <Text style={styles.categoryIcon}>{category.icon}</Text>
            <Text style={[
              styles.categoryTitle,
              formData.category === category.id && styles.categoryTitleSelected,
            ]}>
              {category.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderSubcategorySelection = () => {
    if (!selectedCategory) return null;

    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Select specific issue:</Text>
        
        <View style={styles.subcategoryList}>
          {selectedCategory.subcategories.map((subcategory) => (
            <TouchableOpacity
              key={subcategory}
              style={[
                styles.subcategoryItem,
                formData.subcategory === subcategory && styles.subcategoryItemSelected,
              ]}
              onPress={() => setFormData(prev => ({ ...prev, subcategory }))}
              activeOpacity={0.7}
            >
              <View style={[
                styles.subcategoryRadio,
                formData.subcategory === subcategory && styles.subcategoryRadioSelected,
              ]}>
                {formData.subcategory === subcategory && (
                  <View style={styles.subcategoryRadioDot} />
                )}
              </View>
              <Text style={[
                styles.subcategoryText,
                formData.subcategory === subcategory && styles.subcategoryTextSelected,
              ]}>
                {subcategory}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderOrderNumber = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Order Number (if applicable)</Text>
      <Text style={styles.sectionSubtitle}>
        You can find this in your order confirmation or "My Orders"
      </Text>
      
      <TextInput
        style={styles.textInput}
        placeholder="e.g., ORD-123456"
        value={formData.orderNumber}
        onChangeText={(text) => setFormData(prev => ({ ...prev, orderNumber: text }))}
        autoCapitalize="characters"
      />
    </View>
  );

  const renderDescription = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Describe your issue *</Text>
      <Text style={styles.sectionSubtitle}>
        Please provide as much detail as possible to help us resolve your issue quickly
      </Text>
      
      <TextInput
        style={styles.textArea}
        placeholder="Describe what happened, when it occurred, and any steps you've already taken..."
        value={formData.description}
        onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
        multiline
        numberOfLines={6}
        maxLength={1000}
        textAlignVertical="top"
      />
      
      <Text style={styles.characterCount}>
        {formData.description.length}/1000 characters
      </Text>
    </View>
  );

  const renderUrgencyLevel = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Priority Level</Text>
      
      <View style={styles.urgencyList}>
        {urgencyLevels.map((level) => (
          <TouchableOpacity
            key={level.id}
            style={[
              styles.urgencyItem,
              formData.urgency === level.id && styles.urgencyItemSelected,
            ]}
            onPress={() => setFormData(prev => ({ ...prev, urgency: level.id as any }))}
            activeOpacity={0.7}
          >
            <View style={styles.urgencyContent}>
              <View style={[
                styles.urgencyIndicator,
                { backgroundColor: level.color },
              ]} />
              <View style={styles.urgencyText}>
                <Text style={[
                  styles.urgencyLabel,
                  formData.urgency === level.id && styles.urgencyLabelSelected,
                ]}>
                  {level.label}
                </Text>
                <Text style={styles.urgencyDescription}>
                  {level.description}
                </Text>
              </View>
            </View>
            <View style={[
              styles.urgencyRadio,
              formData.urgency === level.id && styles.urgencyRadioSelected,
            ]}>
              {formData.urgency === level.id && (
                <View style={styles.urgencyRadioDot} />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderContactMethod = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>How would you like us to contact you?</Text>
      
      <View style={styles.contactList}>
        {contactMethods.map((method) => (
          <TouchableOpacity
            key={method.id}
            style={[
              styles.contactItem,
              formData.contactMethod === method.id && styles.contactItemSelected,
            ]}
            onPress={() => setFormData(prev => ({ ...prev, contactMethod: method.id as any }))}
            activeOpacity={0.7}
          >
            <Text style={styles.contactIcon}>{method.icon}</Text>
            <View style={styles.contactContent}>
              <Text style={[
                styles.contactLabel,
                formData.contactMethod === method.id && styles.contactLabelSelected,
              ]}>
                {method.label}
              </Text>
              <Text style={styles.contactDescription}>
                {method.description}
              </Text>
            </View>
            <View style={[
              styles.contactRadio,
              formData.contactMethod === method.id && styles.contactRadioSelected,
            ]}>
              {formData.contactMethod === method.id && (
                <View style={styles.contactRadioDot} />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderAttachments = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Add Photos (Optional)</Text>
      <Text style={styles.sectionSubtitle}>
        Screenshots or photos can help us understand your issue better
      </Text>
      
      <TouchableOpacity
        style={styles.attachmentButton}
        activeOpacity={0.7}
      >
        <Text style={styles.attachmentIcon}>📷</Text>
        <Text style={styles.attachmentText}>Add Photos</Text>
      </TouchableOpacity>
      
      {formData.attachments.length > 0 && (
        <View style={styles.attachmentsList}>
          {formData.attachments.map((attachment, index) => (
            <View key={index} style={styles.attachmentItem}>
              <Text style={styles.attachmentName}>{attachment}</Text>
              <TouchableOpacity
                style={styles.removeAttachment}
                activeOpacity={0.7}
              >
                <Text style={styles.removeAttachmentIcon}>✕</Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Report an Issue</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderCategorySelection()}
        {renderSubcategorySelection()}
        {renderOrderNumber()}
        {renderDescription()}
        {renderUrgencyLevel()}
        {renderContactMethod()}
        {renderAttachments()}
        
        <View style={styles.bottomSection}>
          <TouchableOpacity
            style={[
              styles.submitButton,
              (!formData.category || !formData.subcategory || !formData.description.trim()) &&
              styles.submitButtonDisabled,
            ]}
            onPress={handleSubmit}
            disabled={!formData.category || !formData.subcategory || !formData.description.trim()}
            activeOpacity={0.7}
          >
            <Text style={styles.submitButtonText}>Submit Report</Text>
          </TouchableOpacity>
          
          <Text style={styles.submitNote}>
            By submitting this report, you agree to our Terms of Service and Privacy Policy
          </Text>
        </View>
        
        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 16,
    lineHeight: 18,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  categoryCard: {
    width: '48%',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  categoryCardSelected: {
    backgroundColor: '#FFF3F3',
    borderColor: '#FF4444',
  },
  categoryIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  categoryTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#2C2C2C',
    textAlign: 'center',
  },
  categoryTitleSelected: {
    color: '#FF4444',
    fontWeight: '600',
  },
  subcategoryList: {
    gap: 12,
  },
  subcategoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  subcategoryItemSelected: {
    backgroundColor: '#FFF3F3',
    borderColor: '#FF4444',
  },
  subcategoryRadio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  subcategoryRadioSelected: {
    borderColor: '#FF4444',
  },
  subcategoryRadioDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF4444',
  },
  subcategoryText: {
    flex: 1,
    fontSize: 14,
    color: '#2C2C2C',
  },
  subcategoryTextSelected: {
    color: '#FF4444',
    fontWeight: '500',
  },
  textInput: {
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 14,
    color: '#2C2C2C',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  textArea: {
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 14,
    color: '#2C2C2C',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    minHeight: 120,
  },
  characterCount: {
    fontSize: 12,
    color: '#999999',
    textAlign: 'right',
    marginTop: 8,
  },
  urgencyList: {
    gap: 12,
  },
  urgencyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  urgencyItemSelected: {
    backgroundColor: '#FFF3F3',
    borderColor: '#FF4444',
  },
  urgencyContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  urgencyIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  urgencyText: {
    flex: 1,
  },
  urgencyLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  urgencyLabelSelected: {
    color: '#FF4444',
  },
  urgencyDescription: {
    fontSize: 12,
    color: '#666666',
  },
  urgencyRadio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  urgencyRadioSelected: {
    borderColor: '#FF4444',
  },
  urgencyRadioDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF4444',
  },
  contactList: {
    gap: 12,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  contactItemSelected: {
    backgroundColor: '#FFF3F3',
    borderColor: '#FF4444',
  },
  contactIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  contactContent: {
    flex: 1,
  },
  contactLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  contactLabelSelected: {
    color: '#FF4444',
  },
  contactDescription: {
    fontSize: 12,
    color: '#666666',
  },
  contactRadio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  contactRadioSelected: {
    borderColor: '#FF4444',
  },
  contactRadioDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF4444',
  },
  attachmentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 16,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    borderStyle: 'dashed',
  },
  attachmentIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  attachmentText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  attachmentsList: {
    marginTop: 12,
    gap: 8,
  },
  attachmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#E8F5E8',
    borderRadius: 6,
    padding: 8,
  },
  attachmentName: {
    fontSize: 12,
    color: '#4CAF50',
    flex: 1,
  },
  removeAttachment: {
    padding: 4,
  },
  removeAttachmentIcon: {
    fontSize: 12,
    color: '#666666',
  },
  bottomSection: {
    paddingHorizontal: 16,
    paddingVertical: 24,
  },
  submitButton: {
    backgroundColor: '#FF4444',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  submitButtonDisabled: {
    backgroundColor: '#E0E0E0',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  submitNote: {
    fontSize: 12,
    color: '#999999',
    textAlign: 'center',
    lineHeight: 16,
  },
  bottomPadding: {
    height: 40,
  },
});