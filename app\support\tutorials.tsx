import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';

interface Tutorial {
  id: string;
  title: string;
  duration: string;
  category: string;
  thumbnail: string;
  description: string;
  views: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
}

interface TutorialCategory {
  id: string;
  title: string;
  icon: string;
  count: number;
}

const { width } = Dimensions.get('window');

export default function VideoTutorialsScreen() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const tutorialCategories: TutorialCategory[] = [
    { id: 'all', title: 'All Videos', icon: '🎥', count: 12 },
    { id: 'basics', title: 'App Basics', icon: '📱', count: 4 },
    { id: 'advanced', title: 'Advanced Features', icon: '⚡', count: 4 },
    { id: 'troubleshooting', title: 'Troubleshooting', icon: '🔧', count: 4 },
  ];

  const tutorials: Tutorial[] = [
    {
      id: '1',
      title: 'How to place your first order',
      duration: '2:30',
      category: 'basics',
      thumbnail: '🍕',
      description: 'Learn the basics of ordering food through our app',
      views: 15420,
      difficulty: 'beginner',
      tags: ['ordering', 'basics', 'first-time'],
    },
    {
      id: '2',
      title: 'Using search and filters',
      duration: '1:45',
      category: 'basics',
      thumbnail: '🔍',
      description: 'Find exactly what you want with search and filter options',
      views: 8930,
      difficulty: 'beginner',
      tags: ['search', 'filters', 'navigation'],
    },
    {
      id: '3',
      title: 'Customizing your food',
      duration: '2:15',
      category: 'basics',
      thumbnail: '🎛️',
      description: 'Personalize your orders with customization options',
      views: 12650,
      difficulty: 'beginner',
      tags: ['customization', 'preferences', 'ordering'],
    },
    {
      id: '4',
      title: 'Tracking your delivery',
      duration: '1:30',
      category: 'basics',
      thumbnail: '📍',
      description: 'Monitor your order from kitchen to your door',
      views: 18750,
      difficulty: 'beginner',
      tags: ['tracking', 'delivery', 'real-time'],
    },
    {
      id: '5',
      title: 'Using loyalty points',
      duration: '2:00',
      category: 'advanced',
      thumbnail: '⭐',
      description: 'Maximize savings with our loyalty program',
      views: 6420,
      difficulty: 'intermediate',
      tags: ['loyalty', 'points', 'rewards'],
    },
    {
      id: '6',
      title: 'Group ordering guide',
      duration: '3:15',
      category: 'advanced',
      thumbnail: '👥',
      description: 'Order for multiple people with group ordering',
      views: 4280,
      difficulty: 'intermediate',
      tags: ['group', 'multiple', 'sharing'],
    },
    {
      id: '7',
      title: 'Setting up multiple addresses',
      duration: '1:50',
      category: 'advanced',
      thumbnail: '🏠',
      description: 'Manage home, work, and other delivery locations',
      views: 7650,
      difficulty: 'intermediate',
      tags: ['addresses', 'locations', 'management'],
    },
    {
      id: '8',
      title: 'Managing payment methods',
      duration: '2:30',
      category: 'advanced',
      thumbnail: '💳',
      description: 'Add, remove, and organize your payment options',
      views: 9840,
      difficulty: 'intermediate',
      tags: ['payments', 'cards', 'security'],
    },
    {
      id: '9',
      title: 'Fixing app crashes',
      duration: '2:45',
      category: 'troubleshooting',
      thumbnail: '🔧',
      description: 'Resolve common app stability issues',
      views: 3210,
      difficulty: 'advanced',
      tags: ['crashes', 'bugs', 'fixes'],
    },
    {
      id: '10',
      title: 'Login and password issues',
      duration: '2:10',
      category: 'troubleshooting',
      thumbnail: '🔐',
      description: 'Solve authentication and access problems',
      views: 5670,
      difficulty: 'beginner',
      tags: ['login', 'password', 'access'],
    },
    {
      id: '11',
      title: 'Payment troubleshooting',
      duration: '3:00',
      category: 'troubleshooting',
      thumbnail: '💸',
      description: 'Fix payment failures and billing issues',
      views: 4890,
      difficulty: 'intermediate',
      tags: ['payment', 'billing', 'errors'],
    },
    {
      id: '12',
      title: 'GPS and location problems',
      duration: '2:20',
      category: 'troubleshooting',
      thumbnail: '🗺️',
      description: 'Resolve location detection and accuracy issues',
      views: 6120,
      difficulty: 'intermediate',
      tags: ['gps', 'location', 'accuracy'],
    },
  ];

  const filteredTutorials = tutorials.filter(tutorial => {
    const matchesCategory = selectedCategory === 'all' || tutorial.category === selectedCategory;
    const matchesSearch = tutorial.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         tutorial.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         tutorial.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesCategory && matchesSearch;
  });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return '#4CAF50';
      case 'intermediate': return '#FF9800';
      case 'advanced': return '#F44336';
      default: return '#666666';
    }
  };

  const formatViews = (views: number) => {
    if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K`;
    }
    return views.toString();
  };

  const renderSearchAndFilters = () => (
    <View style={styles.searchSection}>
      <View style={styles.searchBar}>
        <Text style={styles.searchIcon}>🔍</Text>
        <TextInput
          style={styles.searchInput}
          placeholder="Search tutorials..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999999"
        />
      </View>
      
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.categoryFilters}
        contentContainerStyle={styles.categoryFiltersContent}
      >
        {tutorialCategories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryFilter,
              selectedCategory === category.id && styles.categoryFilterActive,
            ]}
            onPress={() => setSelectedCategory(category.id)}
            activeOpacity={0.7}
          >
            <Text style={styles.categoryFilterIcon}>{category.icon}</Text>
            <Text style={[
              styles.categoryFilterText,
              selectedCategory === category.id && styles.categoryFilterTextActive,
            ]}>
              {category.title}
            </Text>
            <Text style={[
              styles.categoryFilterCount,
              selectedCategory === category.id && styles.categoryFilterCountActive,
            ]}>
              {category.count}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderTutorialCard = (tutorial: Tutorial) => (
    <TouchableOpacity
      key={tutorial.id}
      style={styles.tutorialCard}
      onPress={() => router.push(`/support/tutorial/${tutorial.id}`)}
      activeOpacity={0.7}
    >
      <View style={styles.tutorialThumbnail}>
        <Text style={styles.tutorialThumbnailIcon}>{tutorial.thumbnail}</Text>
        <View style={styles.tutorialDuration}>
          <Text style={styles.tutorialDurationText}>{tutorial.duration}</Text>
        </View>
        <TouchableOpacity style={styles.playButton} activeOpacity={0.7}>
          <Text style={styles.playIcon}>▶️</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.tutorialContent}>
        <View style={styles.tutorialHeader}>
          <Text style={styles.tutorialTitle} numberOfLines={2}>
            {tutorial.title}
          </Text>
          <View style={[
            styles.difficultyBadge,
            { backgroundColor: getDifficultyColor(tutorial.difficulty) },
          ]}>
            <Text style={styles.difficultyText}>
              {tutorial.difficulty.charAt(0).toUpperCase() + tutorial.difficulty.slice(1)}
            </Text>
          </View>
        </View>
        
        <Text style={styles.tutorialDescription} numberOfLines={2}>
          {tutorial.description}
        </Text>
        
        <View style={styles.tutorialMeta}>
          <View style={styles.tutorialViews}>
            <Text style={styles.tutorialViewsIcon}>👁</Text>
            <Text style={styles.tutorialViewsText}>
              {formatViews(tutorial.views)} views
            </Text>
          </View>
          
          <View style={styles.tutorialActions}>
            <TouchableOpacity style={styles.bookmarkButton} activeOpacity={0.7}>
              <Text style={styles.bookmarkIcon}>🔖</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.shareButton} activeOpacity={0.7}>
              <Text style={styles.shareIcon}>📤</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.tutorialTags}>
          {tutorial.tags.slice(0, 3).map((tag, index) => (
            <View key={index} style={styles.tutorialTag}>
              <Text style={styles.tutorialTagText}>#{tag}</Text>
            </View>
          ))}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderFeaturedSection = () => {
    const featuredTutorial = tutorials.find(t => t.id === '1');
    if (!featuredTutorial) return null;

    return (
      <View style={styles.featuredSection}>
        <Text style={styles.sectionTitle}>Featured Tutorial</Text>
        
        <TouchableOpacity
          style={styles.featuredCard}
          onPress={() => router.push(`/support/tutorial/${featuredTutorial.id}`)}
          activeOpacity={0.7}
        >
          <View style={styles.featuredThumbnail}>
            <Text style={styles.featuredThumbnailIcon}>{featuredTutorial.thumbnail}</Text>
            <View style={styles.featuredDuration}>
              <Text style={styles.featuredDurationText}>{featuredTutorial.duration}</Text>
            </View>
            <TouchableOpacity style={styles.featuredPlayButton} activeOpacity={0.7}>
              <Text style={styles.featuredPlayIcon}>▶️</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.featuredContent}>
            <Text style={styles.featuredTitle}>{featuredTutorial.title}</Text>
            <Text style={styles.featuredDescription}>{featuredTutorial.description}</Text>
            
            <View style={styles.featuredMeta}>
              <View style={styles.featuredViews}>
                <Text style={styles.featuredViewsIcon}>👁</Text>
                <Text style={styles.featuredViewsText}>
                  {formatViews(featuredTutorial.views)} views
                </Text>
              </View>
              <View style={[
                styles.featuredDifficulty,
                { backgroundColor: getDifficultyColor(featuredTutorial.difficulty) },
              ]}>
                <Text style={styles.featuredDifficultyText}>
                  {featuredTutorial.difficulty}
                </Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const renderQuickAccess = () => (
    <View style={styles.quickAccessSection}>
      <Text style={styles.sectionTitle}>Quick Access</Text>
      
      <View style={styles.quickAccessGrid}>
        <TouchableOpacity
          style={styles.quickAccessItem}
          onPress={() => router.push('/support/tutorial/downloads')}
          activeOpacity={0.7}
        >
          <Text style={styles.quickAccessIcon}>⬇️</Text>
          <Text style={styles.quickAccessText}>Downloaded</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.quickAccessItem}
          onPress={() => router.push('/support/tutorial/bookmarks')}
          activeOpacity={0.7}
        >
          <Text style={styles.quickAccessIcon}>🔖</Text>
          <Text style={styles.quickAccessText}>Bookmarked</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.quickAccessItem}
          onPress={() => router.push('/support/tutorial/history')}
          activeOpacity={0.7}
        >
          <Text style={styles.quickAccessIcon}>📺</Text>
          <Text style={styles.quickAccessText}>Watch History</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.quickAccessItem}
          onPress={() => router.push('/support/tutorial/playlists')}
          activeOpacity={0.7}
        >
          <Text style={styles.quickAccessIcon}>📋</Text>
          <Text style={styles.quickAccessText}>Playlists</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Video Tutorials</Text>
        <TouchableOpacity
          style={styles.settingsButton}
          onPress={() => router.push('/support/tutorial/settings')}
          activeOpacity={0.7}
        >
          <Text style={styles.settingsIcon}>⚙️</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderSearchAndFilters()}
        {renderFeaturedSection()}
        {renderQuickAccess()}
        
        <View style={styles.tutorialsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {selectedCategory === 'all' ? 'All Tutorials' : 
               tutorialCategories.find(c => c.id === selectedCategory)?.title}
            </Text>
            <Text style={styles.resultsCount}>
              {filteredTutorials.length} video{filteredTutorials.length !== 1 ? 's' : ''}
            </Text>
          </View>
          
          <View style={styles.tutorialsList}>
            {filteredTutorials.map(renderTutorialCard)}
          </View>
        </View>
        
        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  settingsButton: {
    padding: 8,
  },
  settingsIcon: {
    fontSize: 20,
  },
  scrollView: {
    flex: 1,
  },
  searchSection: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
  },
  searchIcon: {
    fontSize: 16,
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#2C2C2C',
  },
  categoryFilters: {
    marginBottom: 8,
  },
  categoryFiltersContent: {
    paddingRight: 16,
  },
  categoryFilter: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
  },
  categoryFilterActive: {
    backgroundColor: '#FF4444',
  },
  categoryFilterIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  categoryFilterText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
    marginRight: 6,
  },
  categoryFilterTextActive: {
    color: '#FFFFFF',
  },
  categoryFilterCount: {
    fontSize: 12,
    color: '#999999',
    backgroundColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    textAlign: 'center',
  },
  categoryFilterCountActive: {
    color: '#FF4444',
    backgroundColor: '#FFFFFF',
  },
  featuredSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  featuredCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  featuredThumbnail: {
    height: 180,
    backgroundColor: '#F8F8F8',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  featuredThumbnailIcon: {
    fontSize: 48,
  },
  featuredDuration: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  featuredDurationText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  featuredPlayButton: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 68, 68, 0.9)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  featuredPlayIcon: {
    fontSize: 24,
  },
  featuredContent: {
    padding: 16,
  },
  featuredTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  featuredDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 18,
    marginBottom: 12,
  },
  featuredMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  featuredViews: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  featuredViewsIcon: {
    fontSize: 12,
    marginRight: 4,
  },
  featuredViewsText: {
    fontSize: 12,
    color: '#666666',
  },
  featuredDifficulty: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  featuredDifficultyText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  quickAccessSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  quickAccessGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickAccessItem: {
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    width: (width - 48) / 4,
  },
  quickAccessIcon: {
    fontSize: 20,
    marginBottom: 8,
  },
  quickAccessText: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
    textAlign: 'center',
  },
  tutorialsSection: {
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  resultsCount: {
    fontSize: 14,
    color: '#666666',
  },
  tutorialsList: {
    gap: 16,
  },
  tutorialCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  tutorialThumbnail: {
    height: 120,
    backgroundColor: '#F8F8F8',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  tutorialThumbnailIcon: {
    fontSize: 32,
  },
  tutorialDuration: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 4,
    paddingHorizontal: 4,
    paddingVertical: 2,
  },
  tutorialDurationText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  playButton: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 68, 68, 0.9)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  playIcon: {
    fontSize: 16,
  },
  tutorialContent: {
    padding: 12,
  },
  tutorialHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  tutorialTitle: {
    flex: 1,
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginRight: 8,
  },
  difficultyBadge: {
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  difficultyText: {
    fontSize: 9,
    color: '#FFFFFF',
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  tutorialDescription: {
    fontSize: 12,
    color: '#666666',
    lineHeight: 16,
    marginBottom: 8,
  },
  tutorialMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  tutorialViews: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tutorialViewsIcon: {
    fontSize: 10,
    marginRight: 4,
  },
  tutorialViewsText: {
    fontSize: 11,
    color: '#666666',
  },
  tutorialActions: {
    flexDirection: 'row',
    gap: 8,
  },
  bookmarkButton: {
    padding: 4,
  },
  bookmarkIcon: {
    fontSize: 12,
  },
  shareButton: {
    padding: 4,
  },
  shareIcon: {
    fontSize: 12,
  },
  tutorialTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  tutorialTag: {
    backgroundColor: '#E3F2FD',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  tutorialTagText: {
    fontSize: 10,
    color: '#1976D2',
    fontWeight: '500',
  },
  bottomPadding: {
    height: 40,
  },
});