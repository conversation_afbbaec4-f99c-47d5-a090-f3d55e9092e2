import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  Animated,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface ReviewRewardsProps {
  visible: boolean;
  onClose: () => void;
  rewardType: 'review_submitted' | 'helpful_votes' | 'milestone' | 'streak';
  rewardData: {
    points: number;
    badge?: string;
    milestone?: string;
    streak?: number;
  };
}

interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  earned: boolean;
  progress?: number;
  maxProgress?: number;
}

export function ReviewRewards({
  visible,
  onClose,
  rewardType,
  rewardData,
}: ReviewRewardsProps) {
  const [animationValue] = useState(new Animated.Value(0));
  const [confettiAnimation] = useState(new Animated.Value(0));
  const [showBadges, setShowBadges] = useState(false);

  const badges: Badge[] = [
    {
      id: 'first_review',
      name: 'First Steps',
      description: 'Write your first review',
      icon: '🌟',
      rarity: 'common',
      earned: true,
    },
    {
      id: 'helpful_reviewer',
      name: 'Helpful Guide',
      description: 'Receive 50 helpful votes',
      icon: '👍',
      rarity: 'rare',
      earned: true,
    },
    {
      id: 'photo_master',
      name: 'Photo Master',
      description: 'Add photos to 25 reviews',
      icon: '📸',
      rarity: 'rare',
      earned: false,
      progress: 18,
      maxProgress: 25,
    },
    {
      id: 'streak_champion',
      name: 'Streak Champion',
      description: 'Maintain a 30-day review streak',
      icon: '🔥',
      rarity: 'epic',
      earned: false,
      progress: 12,
      maxProgress: 30,
    },
    {
      id: 'top_reviewer',
      name: 'Top Reviewer',
      description: 'Write 100 reviews',
      icon: '👑',
      rarity: 'legendary',
      earned: false,
      progress: 47,
      maxProgress: 100,
    },
  ];

  const rewardMessages = {
    review_submitted: {
      title: '🎉 Review Submitted!',
      subtitle: 'Thank you for sharing your experience',
    },
    helpful_votes: {
      title: '👍 Helpful Review!',
      subtitle: 'Others found your review helpful',
    },
    milestone: {
      title: '🏆 Milestone Reached!',
      subtitle: 'You\'ve achieved something special',
    },
    streak: {
      title: '🔥 Streak Bonus!',
      subtitle: 'Keep up the great reviewing',
    },
  };

  useEffect(() => {
    if (visible) {
      // Start animations
      Animated.parallel([
        Animated.spring(animationValue, {
          toValue: 1,
          useNativeDriver: true,
          tension: 50,
          friction: 8,
        }),
        Animated.sequence([
          Animated.delay(300),
          Animated.timing(confettiAnimation, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
      ]).start();

      // Haptic feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } else {
      animationValue.setValue(0);
      confettiAnimation.setValue(0);
    }
  }, [visible]);

  const getRarityColor = (rarity: Badge['rarity']) => {
    switch (rarity) {
      case 'common': return '#4CAF50';
      case 'rare': return '#2196F3';
      case 'epic': return '#9C27B0';
      case 'legendary': return '#FF9800';
      default: return '#666666';
    }
  };

  const renderBadge = (badge: Badge) => (
    <View key={badge.id} style={styles.badgeItem}>
      <View style={[
        styles.badgeIcon,
        { borderColor: getRarityColor(badge.rarity) },
        !badge.earned && styles.badgeIconLocked,
      ]}>
        <Text style={[
          styles.badgeIconText,
          !badge.earned && styles.badgeIconTextLocked,
        ]}>
          {badge.earned ? badge.icon : '🔒'}
        </Text>
      </View>
      
      <View style={styles.badgeDetails}>
        <Text style={[
          styles.badgeName,
          !badge.earned && styles.badgeNameLocked,
        ]}>
          {badge.name}
        </Text>
        <Text style={[
          styles.badgeDescription,
          !badge.earned && styles.badgeDescriptionLocked,
        ]}>
          {badge.description}
        </Text>
        
        {!badge.earned && badge.progress !== undefined && badge.maxProgress && (
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill,
                  { 
                    width: `${(badge.progress / badge.maxProgress) * 100}%`,
                    backgroundColor: getRarityColor(badge.rarity),
                  }
                ]} 
              />
            </View>
            <Text style={styles.progressText}>
              {badge.progress}/{badge.maxProgress}
            </Text>
          </View>
        )}
      </View>
      
      <View style={[
        styles.rarityBadge,
        { backgroundColor: getRarityColor(badge.rarity) },
      ]}>
        <Text style={styles.rarityText}>
          {badge.rarity.toUpperCase()}
        </Text>
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <Animated.View 
          style={[
            styles.container,
            {
              transform: [
                {
                  scale: animationValue.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.8, 1],
                  }),
                },
              ],
              opacity: animationValue,
            },
          ]}
        >
          {/* Confetti Animation */}
          <Animated.View 
            style={[
              styles.confetti,
              {
                opacity: confettiAnimation,
                transform: [
                  {
                    translateY: confettiAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: [-50, 50],
                    }),
                  },
                ],
              },
            ]}
          >
            <Text style={styles.confettiText}>🎉✨🎊✨🎉</Text>
          </Animated.View>

          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>
              {rewardMessages[rewardType].title}
            </Text>
            <Text style={styles.subtitle}>
              {rewardMessages[rewardType].subtitle}
            </Text>
          </View>

          {/* Reward Details */}
          <View style={styles.rewardDetails}>
            <View style={styles.pointsContainer}>
              <Text style={styles.pointsIcon}>⭐</Text>
              <Text style={styles.pointsText}>
                +{rewardData.points} Points
              </Text>
            </View>

            {rewardData.badge && (
              <View style={styles.badgeReward}>
                <Text style={styles.badgeRewardIcon}>🏆</Text>
                <Text style={styles.badgeRewardText}>
                  New Badge: {rewardData.badge}
                </Text>
              </View>
            )}

            {rewardData.milestone && (
              <View style={styles.milestoneReward}>
                <Text style={styles.milestoneIcon}>🎯</Text>
                <Text style={styles.milestoneText}>
                  {rewardData.milestone}
                </Text>
              </View>
            )}

            {rewardData.streak && (
              <View style={styles.streakReward}>
                <Text style={styles.streakIcon}>🔥</Text>
                <Text style={styles.streakText}>
                  {rewardData.streak} Day Streak!
                </Text>
              </View>
            )}
          </View>

          {/* Action Buttons */}
          <View style={styles.actions}>
            <TouchableOpacity
              style={styles.badgesButton}
              onPress={() => setShowBadges(!showBadges)}
              activeOpacity={0.8}
            >
              <Text style={styles.badgesButtonText}>
                {showBadges ? 'Hide Badges' : 'View All Badges'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.continueButton}
              onPress={onClose}
              activeOpacity={0.8}
            >
              <Text style={styles.continueButtonText}>Continue</Text>
            </TouchableOpacity>
          </View>

          {/* Badges List */}
          {showBadges && (
            <ScrollView 
              style={styles.badgesList}
              showsVerticalScrollIndicator={false}
            >
              <Text style={styles.badgesTitle}>Your Badges</Text>
              {badges.map(renderBadge)}
            </ScrollView>
          )}
        </Animated.View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  confetti: {
    position: 'absolute',
    top: -30,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 1,
  },
  confettiText: {
    fontSize: 24,
    letterSpacing: 8,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C2C2C',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  rewardDetails: {
    alignItems: 'center',
    marginBottom: 32,
  },
  pointsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    marginBottom: 16,
  },
  pointsIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  pointsText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FF9800',
  },
  badgeReward: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 8,
  },
  badgeRewardIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  badgeRewardText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#34A853',
  },
  milestoneReward: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 8,
  },
  milestoneIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  milestoneText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#2196F3',
  },
  streakReward: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFEBEE',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  streakIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  streakText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#F44336',
  },
  actions: {
    gap: 12,
  },
  badgesButton: {
    backgroundColor: '#F8F8F8',
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: 'center',
  },
  badgesButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666666',
  },
  continueButton: {
    backgroundColor: '#FF4444',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  badgesList: {
    maxHeight: 300,
    marginTop: 20,
  },
  badgesTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
    textAlign: 'center',
  },
  badgeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
  },
  badgeIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    borderWidth: 2,
  },
  badgeIconLocked: {
    backgroundColor: '#F0F0F0',
    borderColor: '#CCCCCC',
  },
  badgeIconText: {
    fontSize: 24,
  },
  badgeIconTextLocked: {
    fontSize: 20,
  },
  badgeDetails: {
    flex: 1,
  },
  badgeName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  badgeNameLocked: {
    color: '#999999',
  },
  badgeDescription: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 4,
  },
  badgeDescriptionLocked: {
    color: '#CCCCCC',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 10,
    color: '#666666',
    fontWeight: '500',
  },
  rarityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  rarityText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
});