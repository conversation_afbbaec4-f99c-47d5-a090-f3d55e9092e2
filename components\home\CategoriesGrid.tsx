import React, { useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Animated,
  Dimensions,
} from 'react-native';
import * as Haptics from 'expo-haptics';

const { width: screenWidth } = Dimensions.get('window');
const ITEM_WIDTH = (screenWidth - 48) / 4; // 4 items per row with padding

interface Category {
  id: string;
  name: string;
  icon: string;
  color: string;
}

const CATEGORIES: Category[] = [
  { id: '1', name: 'Pizza', icon: '🍕', color: '#FF6B35' },
  { id: '2', name: 'Burgers', icon: '🍔', color: '#34A853' },
  { id: '3', name: 'Asian', icon: '🍜', color: '#FBBC04' },
  { id: '4', name: 'Desserts', icon: '🍰', color: '#FF4444' },
  { id: '5', name: 'Coffee', icon: '☕', color: '#8B4513' },
  { id: '6', name: 'Healthy', icon: '🥗', color: '#4CAF50' },
  { id: '7', name: 'Mexican', icon: '🌮', color: '#FF9800' },
  { id: '8', name: 'Indian', icon: '🍛', color: '#E91E63' },
];

interface CategoriesGridProps {
  selectedCategory: string | null;
  onCategorySelect: (categoryId: string) => void;
}

export default function CategoriesGrid({ selectedCategory, onCategorySelect }: CategoriesGridProps) {
  const renderCategory = ({ item, index }: { item: Category; index: number }) => (
    <CategoryItem
      category={item}
      isSelected={selectedCategory === item.id}
      onPress={() => onCategorySelect(item.id)}
      index={index}
    />
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={CATEGORIES}
        renderItem={renderCategory}
        keyExtractor={(item) => item.id}
        numColumns={4}
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.grid}
        columnWrapperStyle={styles.row}
      />
    </View>
  );
}

interface CategoryItemProps {
  category: Category;
  isSelected: boolean;
  onPress: () => void;
  index: number;
}

function CategoryItem({ category, isSelected, onPress, index }: CategoryItemProps) {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    // Staggered entrance animation
    Animated.sequence([
      Animated.delay(index * 100),
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          friction: 8,
          tension: 100,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, []);

  React.useEffect(() => {
    // Selection animation
    Animated.spring(scaleAnim, {
      toValue: isSelected ? 1.05 : 1,
      friction: 8,
      tension: 100,
      useNativeDriver: true,
    }).start();
  }, [isSelected]);

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: isSelected ? 1.05 : 1,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = () => {
    onPress();
    Haptics.selectionAsync();
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={1}
      style={styles.categoryContainer}
    >
      <Animated.View
        style={[
          styles.categoryItem,
          {
            backgroundColor: `${category.color}1A`, // 10% opacity
            borderColor: isSelected ? category.color : 'transparent',
            transform: [{ scale: scaleAnim }],
            opacity: fadeAnim,
          },
        ]}
      >
        <View style={[styles.iconContainer, { backgroundColor: `${category.color}33` }]}>
          <Text style={styles.categoryIcon}>{category.icon}</Text>
        </View>
        
        {isSelected && (
          <Animated.View style={styles.selectedIndicator}>
            <View style={[styles.selectedDot, { backgroundColor: category.color }]} />
          </Animated.View>
        )}
      </Animated.View>
      
      <Text style={[styles.categoryName, isSelected && { color: category.color }]}>
        {category.name}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
  },
  grid: {
    paddingBottom: 8,
  },
  row: {
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  categoryContainer: {
    width: ITEM_WIDTH,
    alignItems: 'center',
  },
  categoryItem: {
    width: 64,
    height: 64,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    marginBottom: 8,
    position: 'relative',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryIcon: {
    fontSize: 20,
  },
  categoryName: {
    fontSize: 12,
    fontWeight: '500',
    color: '#2C2C2C',
    textAlign: 'center',
  },
  selectedIndicator: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
});