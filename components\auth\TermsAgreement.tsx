import React, { useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Linking,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface TermsAgreementProps {
  agreed: boolean;
  onToggle: (agreed: boolean) => void;
  error?: string;
}

export default function TermsAgreement({ agreed, onToggle, error }: TermsAgreementProps) {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handleToggle = () => {
    onToggle(!agreed);
    Haptics.selectionAsync();
    
    // Animate checkbox
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.8,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const openTerms = () => {
    Linking.openURL('https://foodway.com/terms');
  };

  const openPrivacy = () => {
    Linking.openURL('https://foodway.com/privacy');
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.checkboxContainer} onPress={handleToggle}>
        <Animated.View
          style={[
            styles.checkbox,
            {
              backgroundColor: agreed ? '#FF4444' : 'transparent',
              borderColor: error ? '#FF4444' : agreed ? '#FF4444' : '#E0E0E0',
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          {agreed && <Text style={styles.checkmark}>✓</Text>}
        </Animated.View>
        
        <View style={styles.textContainer}>
          <Text style={styles.agreementText}>
            I agree to the{' '}
            <Text style={styles.link} onPress={openTerms}>
              Terms of Service
            </Text>
            {' '}and{' '}
            <Text style={styles.link} onPress={openPrivacy}>
              Privacy Policy
            </Text>
          </Text>
        </View>
      </TouchableOpacity>
      
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 4,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  checkmark: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  textContainer: {
    flex: 1,
  },
  agreementText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  link: {
    color: '#FF4444',
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
  errorText: {
    fontSize: 12,
    color: '#FF4444',
    marginTop: 4,
    marginLeft: 32,
  },
});