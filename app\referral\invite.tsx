import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Linking,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import * as Contacts from 'expo-contacts';
import * as SMS from 'expo-sms';
import * as MailComposer from 'expo-mail-composer';
import * as Haptics from 'expo-haptics';

interface Contact {
  id: string;
  name: string;
  phoneNumber?: string;
  email?: string;
  selected: boolean;
}

interface MessageTemplate {
  id: string;
  name: string;
  title: string;
  message: string;
  tone: 'casual' | 'benefit' | 'personal' | 'urgent';
}

export default function InviteFriendsScreen() {
  const router = useRouter();
  const [selectedTab, setSelectedTab] = useState<'templates' | 'contacts' | 'custom'>('templates');
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [selectedContacts, setSelectedContacts] = useState<Contact[]>([]);
  const [customMessage, setCustomMessage] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('1');
  const [searchQuery, setSearchQuery] = useState('');
  const [sendingAnimation] = useState(new Animated.Value(0));

  const referralCode = "JOHN2024";
  const referralLink = `https://foodway.app/ref/${referralCode}`;

  const messageTemplates: MessageTemplate[] = [
    {
      id: '1',
      name: 'Casual',
      title: 'Hey! Try FoodWay',
      message: `Hey! I've been using FoodWay for food delivery and it's amazing! 🍕 Use my code ${referralCode} and get $10 off your first order. Download here: ${referralLink}`,
      tone: 'casual',
    },
    {
      id: '2',
      name: 'Benefit-Focused',
      title: 'Get $10 off your first order',
      message: `🎉 Get $10 off your first FoodWay order! I'm sharing my referral code ${referralCode} with you. Fast delivery, great restaurants, and now a discount too! ${referralLink}`,
      tone: 'benefit',
    },
    {
      id: '3',
      name: 'Personal',
      title: 'I love this food app',
      message: `I've been loving FoodWay for ordering food - thought you'd like it too! 😊 The delivery is super fast and they have all my favorite restaurants. Use ${referralCode} for $10 off: ${referralLink}`,
      tone: 'personal',
    },
    {
      id: '4',
      name: 'Urgent',
      title: 'Limited time offer',
      message: `⏰ Limited time: Get $15 off your first FoodWay order with my referral code ${referralCode}! Don't miss out on this deal. Download now: ${referralLink}`,
      tone: 'urgent',
    },
  ];

  useEffect(() => {
    loadContacts();
  }, []);

  const loadContacts = async () => {
    try {
      const { status } = await Contacts.requestPermissionsAsync();
      if (status === 'granted') {
        const { data } = await Contacts.getContactsAsync({
          fields: [Contacts.Fields.Name, Contacts.Fields.PhoneNumbers, Contacts.Fields.Emails],
        });

        const formattedContacts: Contact[] = data.map((contact) => ({
          id: contact.id || Math.random().toString(),
          name: contact.name || 'Unknown',
          phoneNumber: contact.phoneNumbers?.[0]?.number,
          email: contact.emails?.[0]?.email,
          selected: false,
        })).filter(contact => contact.phoneNumber || contact.email);

        setContacts(formattedContacts);
      }
    } catch (error) {
      console.error('Error loading contacts:', error);
    }
  };

  const toggleContactSelection = (contactId: string) => {
    setContacts(prev => prev.map(contact => 
      contact.id === contactId 
        ? { ...contact, selected: !contact.selected }
        : contact
    ));
  };

  const getSelectedContacts = () => {
    return contacts.filter(contact => contact.selected);
  };

  const sendInvitations = async () => {
    const selected = getSelectedContacts();
    if (selected.length === 0) {
      Alert.alert('No contacts selected', 'Please select at least one contact to send invitations.');
      return;
    }

    const template = messageTemplates.find(t => t.id === selectedTemplate);
    const message = selectedTab === 'custom' ? customMessage : template?.message || '';

    // Animate sending
    Animated.sequence([
      Animated.timing(sendingAnimation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(sendingAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

    // Simulate sending invitations
    setTimeout(() => {
      Alert.alert(
        'Invitations Sent!',
        `Successfully sent ${selected.length} invitation${selected.length > 1 ? 's' : ''}`,
        [{ text: 'OK', onPress: () => router.back() }]
      );
    }, 600);
  };

  const shareViaWhatsApp = async () => {
    const template = messageTemplates.find(t => t.id === selectedTemplate);
    const message = encodeURIComponent(template?.message || '');
    const url = `whatsapp://send?text=${message}`;
    
    try {
      await Linking.openURL(url);
    } catch (error) {
      Alert.alert('WhatsApp not installed', 'Please install WhatsApp to use this feature.');
    }
  };

  const shareViaSMS = async () => {
    const template = messageTemplates.find(t => t.id === selectedTemplate);
    const message = template?.message || '';
    
    const isAvailable = await SMS.isAvailableAsync();
    if (isAvailable) {
      await SMS.sendSMSAsync([], message);
    } else {
      Alert.alert('SMS not available', 'SMS is not available on this device.');
    }
  };

  const shareViaEmail = async () => {
    const template = messageTemplates.find(t => t.id === selectedTemplate);
    const message = template?.message || '';
    
    const isAvailable = await MailComposer.isAvailableAsync();
    if (isAvailable) {
      await MailComposer.composeAsync({
        subject: 'Try FoodWay - Food Delivery App',
        body: message,
      });
    } else {
      Alert.alert('Email not available', 'Email is not available on this device.');
    }
  };

  const getToneColor = (tone: string) => {
    switch (tone) {
      case 'casual': return '#4ECDC4';
      case 'benefit': return '#45B7D1';
      case 'personal': return '#96CEB4';
      case 'urgent': return '#FFEAA7';
      default: return '#4ECDC4';
    }
  };

  const getToneIcon = (tone: string) => {
    switch (tone) {
      case 'casual': return '😊';
      case 'benefit': return '💰';
      case 'personal': return '❤️';
      case 'urgent': return '⚡';
      default: return '😊';
    }
  };

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {[
        { key: 'templates', label: 'Templates' },
        { key: 'contacts', label: 'Contacts' },
        { key: 'custom', label: 'Custom' },
      ].map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tabButton,
            selectedTab === tab.key && styles.tabButtonActive,
          ]}
          onPress={() => setSelectedTab(tab.key as any)}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.tabButtonText,
            selectedTab === tab.key && styles.tabButtonTextActive,
          ]}>
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderTemplates = () => (
    <View style={styles.templatesSection}>
      <Text style={styles.sectionTitle}>Choose a Message Template</Text>
      
      <View style={styles.templatesList}>
        {messageTemplates.map((template) => (
          <TouchableOpacity
            key={template.id}
            style={[
              styles.templateCard,
              selectedTemplate === template.id && styles.templateCardSelected,
              { borderLeftColor: getToneColor(template.tone) },
            ]}
            onPress={() => setSelectedTemplate(template.id)}
            activeOpacity={0.8}
          >
            <View style={styles.templateHeader}>
              <View style={styles.templateInfo}>
                <Text style={styles.templateIcon}>
                  {getToneIcon(template.tone)}
                </Text>
                <View>
                  <Text style={styles.templateName}>{template.name}</Text>
                  <Text style={styles.templateTitle}>{template.title}</Text>
                </View>
              </View>
              
              <View style={[
                styles.templateRadio,
                selectedTemplate === template.id && styles.templateRadioSelected,
              ]}>
                {selectedTemplate === template.id && (
                  <View style={styles.templateRadioDot} />
                )}
              </View>
            </View>
            
            <Text style={styles.templateMessage}>{template.message}</Text>
            
            <View style={styles.templateMeta}>
              <Text style={styles.templateLength}>
                {template.message.length} characters
              </Text>
              <Text style={[
                styles.templateTone,
                { color: getToneColor(template.tone) },
              ]}>
                {template.tone}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderContacts = () => {
    const filteredContacts = contacts.filter(contact =>
      contact.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    return (
      <View style={styles.contactsSection}>
        <Text style={styles.sectionTitle}>Select Friends to Invite</Text>
        
        <View style={styles.searchContainer}>
          <Text style={styles.searchIcon}>🔍</Text>
          <TextInput
            style={styles.searchInput}
            placeholder="Search contacts..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
        
        <View style={styles.selectedCount}>
          <Text style={styles.selectedCountText}>
            {getSelectedContacts().length} contact{getSelectedContacts().length !== 1 ? 's' : ''} selected
          </Text>
        </View>
        
        <ScrollView style={styles.contactsList}>
          {filteredContacts.map((contact) => (
            <TouchableOpacity
              key={contact.id}
              style={[
                styles.contactItem,
                contact.selected && styles.contactItemSelected,
              ]}
              onPress={() => toggleContactSelection(contact.id)}
              activeOpacity={0.7}
            >
              <View style={styles.contactInfo}>
                <View style={styles.contactAvatar}>
                  <Text style={styles.contactAvatarText}>
                    {contact.name.charAt(0).toUpperCase()}
                  </Text>
                </View>
                
                <View style={styles.contactDetails}>
                  <Text style={styles.contactName}>{contact.name}</Text>
                  <Text style={styles.contactMethod}>
                    {contact.phoneNumber || contact.email}
                  </Text>
                </View>
              </View>
              
              <View style={[
                styles.contactCheckbox,
                contact.selected && styles.contactCheckboxSelected,
              ]}>
                {contact.selected && (
                  <Text style={styles.contactCheckmark}>✓</Text>
                )}
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderCustomMessage = () => (
    <View style={styles.customSection}>
      <Text style={styles.sectionTitle}>Write Custom Message</Text>
      
      <View style={styles.customEditor}>
        <TextInput
          style={styles.customInput}
          placeholder={`Write your personalized invitation message...\n\nTip: Include your referral code ${referralCode} and the app link ${referralLink}`}
          value={customMessage}
          onChangeText={setCustomMessage}
          multiline
          textAlignVertical="top"
        />
        
        <View style={styles.customMeta}>
          <Text style={styles.characterCount}>
            {customMessage.length} characters
          </Text>
          <Text style={styles.characterGuide}>
            {customMessage.length < 100 ? 'Too short' : 
             customMessage.length > 300 ? 'Too long' : 'Good length'}
          </Text>
        </View>
      </View>
      
      <View style={styles.customTips}>
        <Text style={styles.customTipsTitle}>💡 Tips for effective messages:</Text>
        <Text style={styles.customTip}>• Keep it personal and friendly</Text>
        <Text style={styles.customTip}>• Mention the $10 discount benefit</Text>
        <Text style={styles.customTip}>• Include your referral code</Text>
        <Text style={styles.customTip}>• Add the download link</Text>
      </View>
    </View>
  );

  const renderQuickShare = () => (
    <View style={styles.quickShareSection}>
      <Text style={styles.sectionTitle}>Quick Share Options</Text>
      
      <View style={styles.shareButtons}>
        <TouchableOpacity
          style={styles.shareButton}
          onPress={shareViaWhatsApp}
          activeOpacity={0.7}
        >
          <Text style={styles.shareButtonIcon}>💬</Text>
          <Text style={styles.shareButtonText}>WhatsApp</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.shareButton}
          onPress={shareViaSMS}
          activeOpacity={0.7}
        >
          <Text style={styles.shareButtonIcon}>📱</Text>
          <Text style={styles.shareButtonText}>SMS</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.shareButton}
          onPress={shareViaEmail}
          activeOpacity={0.7}
        >
          <Text style={styles.shareButtonIcon}>📧</Text>
          <Text style={styles.shareButtonText}>Email</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.shareButton}
          onPress={() => {/* Social media sharing */}}
          activeOpacity={0.7}
        >
          <Text style={styles.shareButtonIcon}>📱</Text>
          <Text style={styles.shareButtonText}>Social</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderContent = () => {
    switch (selectedTab) {
      case 'templates':
        return renderTemplates();
      case 'contacts':
        return renderContacts();
      case 'custom':
        return renderCustomMessage();
      default:
        return renderTemplates();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Invite Friends</Text>
        <View style={styles.placeholder} />
      </View>

      {renderTabBar()}

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderContent()}
        {renderQuickShare()}
      </ScrollView>

      {/* Send Button */}
      <View style={styles.sendSection}>
        <Animated.View style={[
          styles.sendButton,
          {
            transform: [{
              scale: sendingAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [1, 0.95],
              }),
            }],
          },
        ]}>
          <TouchableOpacity
            style={styles.sendButtonInner}
            onPress={sendInvitations}
            activeOpacity={0.8}
          >
            <Text style={styles.sendButtonText}>
              {selectedTab === 'contacts' 
                ? `Send to ${getSelectedContacts().length} friend${getSelectedContacts().length !== 1 ? 's' : ''}`
                : 'Send Invitations'
              }
            </Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  placeholder: {
    width: 40,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#F8F8F8',
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 8,
    padding: 4,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  tabButtonActive: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  tabButtonTextActive: {
    color: '#2C2C2C',
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  templatesSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  templatesList: {
    gap: 12,
  },
  templateCard: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
  },
  templateCardSelected: {
    backgroundColor: '#FFF3E0',
    borderColor: '#FF9800',
  },
  templateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  templateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  templateIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  templateName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  templateTitle: {
    fontSize: 12,
    color: '#666666',
  },
  templateRadio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  templateRadioSelected: {
    borderColor: '#FF9800',
  },
  templateRadioDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#FF9800',
  },
  templateMessage: {
    fontSize: 12,
    color: '#2C2C2C',
    lineHeight: 16,
    marginBottom: 8,
  },
  templateMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  templateLength: {
    fontSize: 10,
    color: '#666666',
  },
  templateTone: {
    fontSize: 10,
    fontWeight: '500',
  },
  contactsSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 12,
  },
  searchIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 14,
    color: '#2C2C2C',
  },
  selectedCount: {
    marginBottom: 12,
  },
  selectedCountText: {
    fontSize: 12,
    color: '#666666',
  },
  contactsList: {
    maxHeight: 300,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  contactItemSelected: {
    backgroundColor: '#E8F5E8',
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  contactInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  contactAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FF4444',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  contactAvatarText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  contactDetails: {
    flex: 1,
  },
  contactName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  contactMethod: {
    fontSize: 12,
    color: '#666666',
  },
  contactCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  contactCheckboxSelected: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  contactCheckmark: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  customSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  customEditor: {
    marginBottom: 16,
  },
  customInput: {
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: '#2C2C2C',
    minHeight: 120,
    marginBottom: 8,
  },
  customMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  characterCount: {
    fontSize: 12,
    color: '#666666',
  },
  characterGuide: {
    fontSize: 12,
    color: '#666666',
  },
  customTips: {
    backgroundColor: '#E3F2FD',
    borderRadius: 8,
    padding: 12,
  },
  customTipsTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#1976D2',
    marginBottom: 8,
  },
  customTip: {
    fontSize: 11,
    color: '#1976D2',
    marginBottom: 4,
  },
  quickShareSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  shareButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 8,
  },
  shareButton: {
    flex: 1,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  shareButtonIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  shareButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#2C2C2C',
  },
  sendSection: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  sendButton: {
    borderRadius: 8,
  },
  sendButtonInner: {
    backgroundColor: '#FF4444',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  sendButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});