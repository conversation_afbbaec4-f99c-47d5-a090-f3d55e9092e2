import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { useThemeColor } from '@/hooks/useThemeColor';

export default function ManualLocationScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const [address, setAddress] = useState('');

  const handleSaveAddress = () => {
    // Save address logic
    router.back();
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <View style={styles.content}>
        <Text style={styles.title}>Enter Address Manually</Text>
        
        <TextInput
          style={styles.input}
          placeholder="Enter your full address"
          value={address}
          onChangeText={setAddress}
          multiline
        />

        <TouchableOpacity style={styles.saveButton} onPress={handleSaveAddress}>
          <Text style={styles.saveButtonText}>Save Address</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { padding: 20 },
  title: { fontSize: 24, fontWeight: 'bold', marginBottom: 20 },
  input: { 
    borderWidth: 1, 
    borderColor: '#ddd', 
    padding: 16, 
    borderRadius: 8, 
    marginBottom: 20,
    minHeight: 100,
    textAlignVertical: 'top'
  },
  saveButton: { backgroundColor: '#007AFF', padding: 16, borderRadius: 8 },
  saveButtonText: { color: 'white', textAlign: 'center', fontWeight: 'bold', fontSize: 16 },
});