import { DeliveryInfo } from '@/app/checkout';
import DateTimePicker from '@react-native-community/datetimepicker';
import * as Haptics from 'expo-haptics';
import React, { useState } from 'react';
import {
  Modal,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

interface DeliveryDetailsProps {
  deliveryInfo: DeliveryInfo;
  onDeliveryInfoChange: (info: DeliveryInfo) => void;
}

export default function DeliveryDetails({ deliveryInfo, onDeliveryInfoChange }: DeliveryDetailsProps) {
  const [showAddressModal, setShowAddressModal] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);

  const addressTypes = [
    { id: 'home' as const, label: 'Home', icon: '🏠' },
    { id: 'work' as const, label: 'Work', icon: '🏢' },
    { id: 'other' as const, label: 'Other', icon: '📍' },
  ];

  const savedAddresses = [
    { address: '123 Main St, City, State 12345', type: 'home' as const },
    { address: '456 Oak Ave, City, State 12345', type: 'work' as const },
    { address: '789 Pine Rd, City, State 12345', type: 'other' as const },
  ];

  const timeSlots = [
    '11:00 AM - 11:30 AM',
    '11:30 AM - 12:00 PM',
    '12:00 PM - 12:30 PM',
    '12:30 PM - 1:00 PM',
    '1:00 PM - 1:30 PM',
    '1:30 PM - 2:00 PM',
  ];

  const updateDeliveryInfo = (updates: Partial<DeliveryInfo>) => {
    onDeliveryInfoChange({ ...deliveryInfo, ...updates });
  };

  const handleAddressSelect = (address: string, type: 'home' | 'work' | 'other') => {
    updateDeliveryInfo({ address, addressType: type });
    setShowAddressModal(false);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      updateDeliveryInfo({ scheduledTime: selectedDate });
    }
  };

  const handleTimeChange = (event: any, selectedTime?: Date) => {
    setShowTimePicker(false);
    if (selectedTime) {
      const currentDate = deliveryInfo.scheduledTime || new Date();
      const newDateTime = new Date(currentDate);
      newDateTime.setHours(selectedTime.getHours());
      newDateTime.setMinutes(selectedTime.getMinutes());
      updateDeliveryInfo({ scheduledTime: newDateTime });
    }
  };

  return (
    <View style={styles.container}>
      {/* Address Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Delivery Address</Text>
        
        <TouchableOpacity
          style={styles.addressCard}
          onPress={() => setShowAddressModal(true)}
          activeOpacity={0.7}
        >
          <View style={styles.addressContent}>
            <View style={styles.addressHeader}>
              <View style={styles.addressTypeContainer}>
                <Text style={styles.addressTypeIcon}>
                  {addressTypes.find(t => t.id === deliveryInfo.addressType)?.icon}
                </Text>
                <Text style={styles.addressType}>
                  {addressTypes.find(t => t.id === deliveryInfo.addressType)?.label}
                </Text>
              </View>
              <TouchableOpacity style={styles.editButton} activeOpacity={0.7}>
                <Text style={styles.editButtonText}>✏️ Edit</Text>
              </TouchableOpacity>
            </View>
            
            <Text style={styles.addressText}>{deliveryInfo.address}</Text>
            
            <View style={styles.contactInfo}>
              <Text style={styles.contactNumber}>{deliveryInfo.contactNumber}</Text>
              {deliveryInfo.isVerified && (
                <Text style={styles.verifiedBadge}>✓ Verified</Text>
              )}
            </View>
          </View>
        </TouchableOpacity>

        {/* Delivery Instructions */}
        <View style={styles.instructionsContainer}>
          <Text style={styles.inputLabel}>Delivery Instructions (Optional)</Text>
          <TextInput
            style={styles.instructionsInput}
            placeholder="e.g., Ring doorbell, Leave at door"
            value={deliveryInfo.instructions}
            onChangeText={(text) => updateDeliveryInfo({ instructions: text })}
            multiline
            numberOfLines={3}
          />
        </View>
      </View>

      {/* Delivery Time */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Delivery Time</Text>
        
        <View style={styles.deliveryTimeOptions}>
          <TouchableOpacity
            style={[
              styles.timeOption,
              deliveryInfo.deliveryTime === 'asap' && styles.timeOptionSelected,
            ]}
            onPress={() => {
              updateDeliveryInfo({ deliveryTime: 'asap' });
              Haptics.selectionAsync();
            }}
            activeOpacity={0.7}
          >
            <View style={styles.timeOptionContent}>
              <Text style={[
                styles.timeOptionTitle,
                deliveryInfo.deliveryTime === 'asap' && styles.timeOptionTitleSelected,
              ]}>
                ASAP
              </Text>
              <Text style={styles.timeOptionSubtitle}>
                {deliveryInfo.isExpress ? '15-25 min' : '30-45 min'}
              </Text>
            </View>
            <View style={[
              styles.radioButton,
              deliveryInfo.deliveryTime === 'asap' && styles.radioButtonSelected,
            ]}>
              {deliveryInfo.deliveryTime === 'asap' && (
                <View style={styles.radioButtonInner} />
              )}
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.timeOption,
              deliveryInfo.deliveryTime === 'scheduled' && styles.timeOptionSelected,
            ]}
            onPress={() => {
              updateDeliveryInfo({ deliveryTime: 'scheduled' });
              Haptics.selectionAsync();
            }}
            activeOpacity={0.7}
          >
            <View style={styles.timeOptionContent}>
              <Text style={[
                styles.timeOptionTitle,
                deliveryInfo.deliveryTime === 'scheduled' && styles.timeOptionTitleSelected,
              ]}>
                Schedule Delivery
              </Text>
              <Text style={styles.timeOptionSubtitle}>
                {deliveryInfo.scheduledTime 
                  ? deliveryInfo.scheduledTime.toLocaleString()
                  : 'Choose date and time'
                }
              </Text>
            </View>
            <View style={[
              styles.radioButton,
              deliveryInfo.deliveryTime === 'scheduled' && styles.radioButtonSelected,
            ]}>
              {deliveryInfo.deliveryTime === 'scheduled' && (
                <View style={styles.radioButtonInner} />
              )}
            </View>
          </TouchableOpacity>
        </View>

        {/* Date/Time Pickers for Scheduled Delivery */}
        {deliveryInfo.deliveryTime === 'scheduled' && (
          <View style={styles.scheduledOptions}>
            <TouchableOpacity
              style={styles.dateTimeButton}
              onPress={() => setShowDatePicker(true)}
              activeOpacity={0.7}
            >
              <Text style={styles.dateTimeButtonText}>
                📅 {deliveryInfo.scheduledTime?.toDateString() || 'Select Date'}
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.dateTimeButton}
              onPress={() => setShowTimePicker(true)}
              activeOpacity={0.7}
            >
              <Text style={styles.dateTimeButtonText}>
                🕐 {deliveryInfo.scheduledTime?.toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                }) || 'Select Time'}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Express Delivery Toggle */}
        <View style={styles.expressOption}>
          <View style={styles.expressInfo}>
            <Text style={styles.expressTitle}>Express Delivery</Text>
            <Text style={styles.expressSubtitle}>Get it faster (+$2.00)</Text>
          </View>
          <Switch
            value={deliveryInfo.isExpress}
            onValueChange={(value) => {
              updateDeliveryInfo({ isExpress: value });
              Haptics.selectionAsync();
            }}
            trackColor={{ false: '#E0E0E0', true: '#FF4444' }}
            thumbColor={deliveryInfo.isExpress ? '#FFFFFF' : '#FFFFFF'}
          />
        </View>
      </View>

      {/* Address Selection Modal */}
      <Modal
        visible={showAddressModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Address</Text>
            <TouchableOpacity
              onPress={() => setShowAddressModal(false)}
              style={styles.closeButton}
              activeOpacity={0.7}
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            {savedAddresses.map((item, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.addressOption,
                  deliveryInfo.address === item.address && styles.selectedAddressOption,
                ]}
                onPress={() => handleAddressSelect(item.address, item.type)}
                activeOpacity={0.7}
              >
                <View style={styles.addressOptionContent}>
                  <Text style={styles.addressOptionType}>
                    {addressTypes.find(t => t.id === item.type)?.icon} {addressTypes.find(t => t.id === item.type)?.label}
                  </Text>
                  <Text style={styles.addressOptionText}>{item.address}</Text>
                </View>
                {deliveryInfo.address === item.address && (
                  <Text style={styles.checkmark}>✓</Text>
                )}
              </TouchableOpacity>
            ))}
            
            <TouchableOpacity style={styles.addAddressButton} activeOpacity={0.7}>
              <Text style={styles.addAddressText}>+ Add New Address</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </Modal>

      {/* Date/Time Pickers */}
      {showDatePicker && (
        <DateTimePicker
          value={deliveryInfo.scheduledTime || new Date()}
          mode="date"
          display="default"
          onChange={handleDateChange}
          minimumDate={new Date()}
        />
      )}
      
      {showTimePicker && (
        <DateTimePicker
          value={deliveryInfo.scheduledTime || new Date()}
          mode="time"
          display="default"
          onChange={handleTimeChange}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  addressCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#F0F0F0',
    padding: 16,
    marginBottom: 16,
  },
  addressContent: {
    gap: 12,
  },
  addressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  addressTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  addressTypeIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  addressType: {
    fontSize: 14,
    fontWeight: '500',
    color: '#2C2C2C',
  },
  editButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  editButtonText: {
    fontSize: 14,
    color: '#FF4444',
  },
  addressText: {
    fontSize: 16,
    color: '#2C2C2C',
    lineHeight: 22,
  },
  contactInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  contactNumber: {
    fontSize: 14,
    color: '#666666',
  },
  verifiedBadge: {
    fontSize: 12,
    color: '#34A853',
    fontWeight: '500',
  },
  instructionsContainer: {
    gap: 8,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  instructionsInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    backgroundColor: '#FAFAFA',
    textAlignVertical: 'top',
    minHeight: 80,
  },
  deliveryTimeOptions: {
    gap: 12,
    marginBottom: 16,
  },
  timeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 16,
  },
  timeOptionSelected: {
    borderColor: '#FF4444',
    backgroundColor: '#FFF5F5',
  },
  timeOptionContent: {
    flex: 1,
  },
  timeOptionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  timeOptionTitleSelected: {
    color: '#FF4444',
  },
  timeOptionSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    borderColor: '#FF4444',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#FF4444',
  },
  scheduledOptions: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  dateTimeButton: {
    flex: 1,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  dateTimeButtonText: {
    fontSize: 14,
    color: '#2C2C2C',
  },
  expressOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 16,
  },
  expressInfo: {
    flex: 1,
  },
  expressTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  expressSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#666666',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  addressOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#F0F0F0',
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
  },
  selectedAddressOption: {
    borderColor: '#FF4444',
    backgroundColor: '#FFF5F5',
  },
  addressOptionContent: {
    flex: 1,
  },
  addressOptionType: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
    marginBottom: 4,
  },
  addressOptionText: {
    fontSize: 14,
    color: '#2C2C2C',
  },
  checkmark: {
    fontSize: 16,
    color: '#FF4444',
    fontWeight: 'bold',
  },
  addAddressButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#FF4444',
    borderStyle: 'dashed',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  addAddressText: {
    fontSize: 14,
    color: '#FF4444',
    fontWeight: '500',
  },
});