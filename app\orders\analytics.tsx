import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';

const { width } = Dimensions.get('window');

interface SpendingData {
  month: string;
  amount: number;
}

interface CuisinePreference {
  cuisine: string;
  orders: number;
  percentage: number;
  color: string;
}

interface OrderInsight {
  title: string;
  value: string;
  subtitle: string;
  icon: string;
  trend?: 'up' | 'down' | 'stable';
}

export default function OrderAnalyticsScreen() {
  const router = useRouter();
  const [selectedPeriod, setSelectedPeriod] = useState<'month' | 'year'>('month');

  const spendingData: SpendingData[] = [
    { month: 'Jan', amount: 89.50 },
    { month: 'Feb', amount: 124.30 },
    { month: 'Mar', amount: 156.80 },
    { month: 'Apr', amount: 98.20 },
    { month: 'May', amount: 187.40 },
    { month: 'Jun', amount: 234.60 },
  ];

  const cuisinePreferences: CuisinePreference[] = [
    { cuisine: 'Italian', orders: 15, percentage: 32, color: '#FF6B6B' },
    { cuisine: 'Asian', orders: 12, percentage: 26, color: '#4ECDC4' },
    { cuisine: 'Mexican', orders: 8, percentage: 17, color: '#45B7D1' },
    { cuisine: 'American', orders: 7, percentage: 15, color: '#96CEB4' },
    { cuisine: 'Other', orders: 5, percentage: 10, color: '#FFEAA7' },
  ];

  const insights: OrderInsight[] = [
    {
      title: 'Average Order Value',
      value: '$26.54',
      subtitle: '+12% from last month',
      icon: '💰',
      trend: 'up',
    },
    {
      title: 'Orders This Month',
      value: '8',
      subtitle: 'Same as last month',
      icon: '📦',
      trend: 'stable',
    },
    {
      title: 'Favorite Day',
      value: 'Friday',
      subtitle: '35% of orders',
      icon: '📅',
    },
    {
      title: 'Peak Time',
      value: '7:30 PM',
      subtitle: 'Most common order time',
      icon: '🕰️',
    },
  ];

  const favoriteRestaurants = [
    { name: "Mario's Pizza", orders: 8, spent: 156.80, logo: '🍕' },
    { name: 'Dragon Palace', orders: 6, spent: 124.50, logo: '🥡' },
    { name: 'Taco Fiesta', orders: 5, spent: 89.30, logo: '🌮' },
    { name: 'Burger Junction', orders: 4, spent: 67.20, logo: '🍔' },
  ];

  const recommendations = [
    {
      title: 'Try Something New',
      subtitle: 'Mediterranean cuisine based on your preferences',
      restaurant: 'Olive Garden',
      discount: '15% off first order',
      icon: '🥗',
    },
    {
      title: 'Budget-Friendly Option',
      subtitle: 'Great value meals under $15',
      restaurant: 'Quick Bites',
      discount: 'Free delivery',
      icon: '💵',
    },
    {
      title: 'Seasonal Special',
      subtitle: 'Fresh summer menu items',
      restaurant: 'Fresh Kitchen',
      discount: '20% off',
      icon: '🌞',
    },
  ];

  const maxSpending = Math.max(...spendingData.map(d => d.amount));

  const renderSpendingChart = () => (
    <View style={styles.chartContainer}>
      <Text style={styles.chartTitle}>Monthly Spending</Text>
      <View style={styles.chart}>
        {spendingData.map((data, index) => (
          <View key={index} style={styles.chartBar}>
            <View
              style={[
                styles.bar,
                {
                  height: (data.amount / maxSpending) * 120,
                  backgroundColor: '#FF4444',
                },
              ]}
            />
            <Text style={styles.barValue}>${data.amount.toFixed(0)}</Text>
            <Text style={styles.barLabel}>{data.month}</Text>
          </View>
        ))}
      </View>
    </View>
  );

  const renderCuisineChart = () => (
    <View style={styles.cuisineContainer}>
      <Text style={styles.chartTitle}>Cuisine Preferences</Text>
      <View style={styles.cuisineChart}>
        {cuisinePreferences.map((cuisine, index) => (
          <View key={index} style={styles.cuisineItem}>
            <View style={styles.cuisineInfo}>
              <View
                style={[styles.cuisineColor, { backgroundColor: cuisine.color }]}
              />
              <Text style={styles.cuisineName}>{cuisine.cuisine}</Text>
            </View>
            <View style={styles.cuisineStats}>
              <Text style={styles.cuisineOrders}>{cuisine.orders} orders</Text>
              <Text style={styles.cuisinePercentage}>{cuisine.percentage}%</Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Order Analytics</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Period Selector */}
        <View style={styles.periodSelector}>
          <TouchableOpacity
            style={[
              styles.periodButton,
              selectedPeriod === 'month' && styles.periodButtonActive,
            ]}
            onPress={() => setSelectedPeriod('month')}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.periodButtonText,
              selectedPeriod === 'month' && styles.periodButtonTextActive,
            ]}>
              This Month
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.periodButton,
              selectedPeriod === 'year' && styles.periodButtonActive,
            ]}
            onPress={() => setSelectedPeriod('year')}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.periodButtonText,
              selectedPeriod === 'year' && styles.periodButtonTextActive,
            ]}>
              This Year
            </Text>
          </TouchableOpacity>
        </View>

        {/* Key Insights */}
        <View style={styles.insightsSection}>
          <Text style={styles.sectionTitle}>Key Insights</Text>
          <View style={styles.insightsGrid}>
            {insights.map((insight, index) => (
              <View key={index} style={styles.insightCard}>
                <Text style={styles.insightIcon}>{insight.icon}</Text>
                <Text style={styles.insightTitle}>{insight.title}</Text>
                <Text style={styles.insightValue}>{insight.value}</Text>
                <View style={styles.insightSubtitleContainer}>
                  {insight.trend && (
                    <Text style={[
                      styles.trendIcon,
                      insight.trend === 'up' && styles.trendUp,
                      insight.trend === 'down' && styles.trendDown,
                    ]}>
                      {insight.trend === 'up' ? '↗️' : insight.trend === 'down' ? '↘️' : '➡️'}
                    </Text>
                  )}
                  <Text style={styles.insightSubtitle}>{insight.subtitle}</Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Spending Chart */}
        <View style={styles.chartSection}>
          {renderSpendingChart()}
        </View>

        {/* Cuisine Preferences */}
        <View style={styles.chartSection}>
          {renderCuisineChart()}
        </View>

        {/* Favorite Restaurants */}
        <View style={styles.favoritesSection}>
          <Text style={styles.sectionTitle}>Favorite Restaurants</Text>
          <View style={styles.favoritesList}>
            {favoriteRestaurants.map((restaurant, index) => (
              <View key={index} style={styles.favoriteCard}>
                <Text style={styles.favoriteLogo}>{restaurant.logo}</Text>
                <View style={styles.favoriteInfo}>
                  <Text style={styles.favoriteName}>{restaurant.name}</Text>
                  <Text style={styles.favoriteStats}>
                    {restaurant.orders} orders • ${restaurant.spent.toFixed(2)} spent
                  </Text>
                </View>
                <TouchableOpacity
                  style={styles.favoriteAction}
                  onPress={() => router.push(`/restaurant/${restaurant.name}`)}
                  activeOpacity={0.7}
                >
                  <Text style={styles.favoriteActionText}>Order Again</Text>
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>

        {/* Recommendations */}
        <View style={styles.recommendationsSection}>
          <Text style={styles.sectionTitle}>Recommendations for You</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.recommendationsList}>
              {recommendations.map((rec, index) => (
                <View key={index} style={styles.recommendationCard}>
                  <Text style={styles.recommendationIcon}>{rec.icon}</Text>
                  <Text style={styles.recommendationTitle}>{rec.title}</Text>
                  <Text style={styles.recommendationSubtitle}>{rec.subtitle}</Text>
                  <Text style={styles.recommendationRestaurant}>{rec.restaurant}</Text>
                  <View style={styles.recommendationDiscount}>
                    <Text style={styles.recommendationDiscountText}>
                      {rec.discount}
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={styles.recommendationButton}
                    onPress={() => router.push(`/restaurant/${rec.restaurant}`)}
                    activeOpacity={0.8}
                  >
                    <Text style={styles.recommendationButtonText}>Try Now</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Export Options */}
        <View style={styles.exportSection}>
          <Text style={styles.sectionTitle}>Export Data</Text>
          <View style={styles.exportButtons}>
            <TouchableOpacity
              style={styles.exportButton}
              onPress={() => {/* Export CSV */}}
              activeOpacity={0.7}
            >
              <Text style={styles.exportIcon}>📊</Text>
              <Text style={styles.exportText}>Export CSV</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.exportButton}
              onPress={() => {/* Export PDF */}}
              activeOpacity={0.7}
            >
              <Text style={styles.exportIcon}>📄</Text>
              <Text style={styles.exportText}>Export PDF</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.exportButton}
              onPress={() => {/* Tax Summary */}}
              activeOpacity={0.7}
            >
              <Text style={styles.exportIcon}>🧾</Text>
              <Text style={styles.exportText}>Tax Summary</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  periodSelector: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 8,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: '#F8F8F8',
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: '#FF4444',
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  periodButtonTextActive: {
    color: '#FFFFFF',
  },
  insightsSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  insightsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  insightCard: {
    width: (width - 44) / 2,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  insightIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  insightTitle: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 4,
  },
  insightValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  insightSubtitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendIcon: {
    fontSize: 12,
    marginRight: 4,
  },
  trendUp: {
    color: '#34A853',
  },
  trendDown: {
    color: '#F44336',
  },
  insightSubtitle: {
    fontSize: 10,
    color: '#666666',
    textAlign: 'center',
  },
  chartSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  chartContainer: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
    textAlign: 'center',
  },
  chart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-around',
    height: 160,
  },
  chartBar: {
    alignItems: 'center',
    flex: 1,
  },
  bar: {
    width: 20,
    backgroundColor: '#FF4444',
    borderRadius: 2,
    marginBottom: 8,
  },
  barValue: {
    fontSize: 10,
    color: '#666666',
    marginBottom: 4,
  },
  barLabel: {
    fontSize: 10,
    color: '#666666',
  },
  cuisineContainer: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
  },
  cuisineChart: {
    gap: 12,
  },
  cuisineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  cuisineInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  cuisineColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  cuisineName: {
    fontSize: 14,
    color: '#2C2C2C',
  },
  cuisineStats: {
    alignItems: 'flex-end',
  },
  cuisineOrders: {
    fontSize: 12,
    color: '#666666',
  },
  cuisinePercentage: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  favoritesSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  favoritesList: {
    gap: 12,
  },
  favoriteCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
  },
  favoriteLogo: {
    fontSize: 24,
    marginRight: 12,
  },
  favoriteInfo: {
    flex: 1,
  },
  favoriteName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  favoriteStats: {
    fontSize: 12,
    color: '#666666',
  },
  favoriteAction: {
    backgroundColor: '#FF4444',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  favoriteActionText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  recommendationsSection: {
    paddingBottom: 20,
  },
  recommendationsList: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    gap: 12,
  },
  recommendationCard: {
    width: 200,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
  },
  recommendationIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  recommendationTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  recommendationSubtitle: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 8,
    lineHeight: 16,
  },
  recommendationRestaurant: {
    fontSize: 12,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  recommendationDiscount: {
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginBottom: 12,
  },
  recommendationDiscountText: {
    fontSize: 10,
    color: '#34A853',
    fontWeight: '500',
  },
  recommendationButton: {
    backgroundColor: '#FF4444',
    paddingVertical: 8,
    borderRadius: 6,
    alignItems: 'center',
  },
  recommendationButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  exportSection: {
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  exportButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  exportButton: {
    flex: 1,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  exportIcon: {
    fontSize: 20,
    marginBottom: 8,
  },
  exportText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666666',
  },
});