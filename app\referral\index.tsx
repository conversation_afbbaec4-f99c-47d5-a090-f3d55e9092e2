import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Share,
  Clipboard,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';

interface ReferralStats {
  totalReferrals: number;
  successfulReferrals: number;
  totalEarnings: number;
  pendingRewards: number;
  currentRank: number;
  totalUsers: number;
}

interface PendingReferral {
  id: string;
  name: string;
  phone: string;
  invitedDate: string;
  status: 'pending' | 'downloaded' | 'registered' | 'ordered' | 'expired';
  rewardAmount: number;
  daysLeft: number;
}

export default function ReferralDashboard() {
  const router = useRouter();
  const [animatedEarnings] = useState(new Animated.Value(0));
  const [animatedReferrals] = useState(new Animated.Value(0));
  const [showQRCode, setShowQRCode] = useState(false);
  const [copiedCode, setCopiedCode] = useState(false);

  const referralCode = "JOHN2024";
  const referralLink = `https://foodway.app/ref/${referralCode}`;

  const stats: ReferralStats = {
    totalReferrals: 12,
    successfulReferrals: 8,
    totalEarnings: 85.50,
    pendingRewards: 25.00,
    currentRank: 47,
    totalUsers: 1250,
  };

  const pendingReferrals: PendingReferral[] = [
    {
      id: '1',
      name: 'Sarah Johnson',
      phone: '+****************',
      invitedDate: '2024-01-15',
      status: 'registered',
      rewardAmount: 5.00,
      daysLeft: 12,
    },
    {
      id: '2',
      name: 'Mike Chen',
      phone: '+****************',
      invitedDate: '2024-01-18',
      status: 'downloaded',
      rewardAmount: 5.00,
      daysLeft: 9,
    },
    {
      id: '3',
      name: 'Emma Wilson',
      phone: '+****************',
      invitedDate: '2024-01-20',
      status: 'pending',
      rewardAmount: 5.00,
      daysLeft: 7,
    },
  ];

  useEffect(() => {
    // Animate earnings counter
    Animated.timing(animatedEarnings, {
      toValue: stats.totalEarnings,
      duration: 2000,
      useNativeDriver: false,
    }).start();

    // Animate referrals counter
    Animated.timing(animatedReferrals, {
      toValue: stats.successfulReferrals,
      duration: 1500,
      useNativeDriver: false,
    }).start();
  }, []);

  const copyReferralCode = async () => {
    await Clipboard.setStringAsync(referralCode);
    setCopiedCode(true);
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    
    setTimeout(() => setCopiedCode(false), 2000);
  };

  const shareReferralLink = async () => {
    try {
      await Share.share({
        message: `Hey! Try FoodWay - the best food delivery app! Use my code ${referralCode} and get $10 off your first order. Download here: ${referralLink}`,
        url: referralLink,
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#9E9E9E';
      case 'downloaded': return '#2196F3';
      case 'registered': return '#FF9800';
      case 'ordered': return '#4CAF50';
      case 'expired': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'Invitation Sent';
      case 'downloaded': return 'App Downloaded';
      case 'registered': return 'Account Created';
      case 'ordered': return 'First Order Complete';
      case 'expired': return 'Expired';
      default: return 'Unknown';
    }
  };

  const renderStatsCard = () => (
    <View style={styles.statsCard}>
      <Text style={styles.statsTitle}>Your Referral Stats</Text>
      
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <Animated.Text style={styles.statValue}>
            {animatedReferrals}
          </Animated.Text>
          <Text style={styles.statLabel}>Successful Referrals</Text>
        </View>
        
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{stats.totalReferrals}</Text>
          <Text style={styles.statLabel}>Total Invites</Text>
        </View>
        
        <View style={styles.statItem}>
          <Animated.Text style={styles.statValue}>
            ${animatedEarnings.interpolate({
              inputRange: [0, stats.totalEarnings],
              outputRange: ['0.00', stats.totalEarnings.toFixed(2)],
            })}
          </Animated.Text>
          <Text style={styles.statLabel}>Total Earnings</Text>
        </View>
        
        <View style={styles.statItem}>
          <Text style={styles.statValue}>${stats.pendingRewards.toFixed(2)}</Text>
          <Text style={styles.statLabel}>Pending Rewards</Text>
        </View>
      </View>
      
      <View style={styles.rankSection}>
        <Text style={styles.rankText}>
          🏆 Rank #{stats.currentRank} of {stats.totalUsers.toLocaleString()} users
        </Text>
        <TouchableOpacity
          style={styles.leaderboardButton}
          onPress={() => router.push('/referral/leaderboard')}
          activeOpacity={0.7}
        >
          <Text style={styles.leaderboardButtonText}>View Leaderboard</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderReferralCode = () => (
    <View style={styles.codeCard}>
      <Text style={styles.codeTitle}>Your Referral Code</Text>
      
      <View style={styles.codeContainer}>
        <Text style={styles.codeText}>{referralCode}</Text>
        <TouchableOpacity
          style={[styles.copyButton, copiedCode && styles.copyButtonSuccess]}
          onPress={copyReferralCode}
          activeOpacity={0.7}
        >
          <Text style={[styles.copyButtonText, copiedCode && styles.copyButtonTextSuccess]}>
            {copiedCode ? '✓ Copied!' : 'Copy'}
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.codeActions}>
        <TouchableOpacity
          style={styles.qrButton}
          onPress={() => setShowQRCode(!showQRCode)}
          activeOpacity={0.7}
        >
          <Text style={styles.qrButtonText}>📱 QR Code</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.shareButton}
          onPress={shareReferralLink}
          activeOpacity={0.7}
        >
          <Text style={styles.shareButtonText}>📤 Share Link</Text>
        </TouchableOpacity>
      </View>
      
      {showQRCode && (
        <View style={styles.qrCodeContainer}>
          <View style={styles.qrCodePlaceholder}>
            <Text style={styles.qrCodeText}>QR Code</Text>
            <Text style={styles.qrCodeSubtext}>{referralCode}</Text>
          </View>
          <Text style={styles.qrCodeInstructions}>
            Friends can scan this code to get your referral link
          </Text>
        </View>
      )}
    </View>
  );

  const renderQuickActions = () => (
    <View style={styles.actionsCard}>
      <Text style={styles.actionsTitle}>Quick Actions</Text>
      
      <View style={styles.actionsList}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => router.push('/referral/invite')}
          activeOpacity={0.7}
        >
          <Text style={styles.actionIcon}>👥</Text>
          <View style={styles.actionInfo}>
            <Text style={styles.actionTitle}>Invite Friends</Text>
            <Text style={styles.actionSubtitle}>Send personalized invitations</Text>
          </View>
          <Text style={styles.actionArrow}>→</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => router.push('/referral/tracking')}
          activeOpacity={0.7}
        >
          <Text style={styles.actionIcon}>📊</Text>
          <View style={styles.actionInfo}>
            <Text style={styles.actionTitle}>Track Referrals</Text>
            <Text style={styles.actionSubtitle}>Monitor invitation progress</Text>
          </View>
          <Text style={styles.actionArrow}>→</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => router.push('/referral/challenges')}
          activeOpacity={0.7}
        >
          <Text style={styles.actionIcon}>🎯</Text>
          <View style={styles.actionInfo}>
            <Text style={styles.actionTitle}>Referral Challenges</Text>
            <Text style={styles.actionSubtitle}>Earn bonus rewards</Text>
          </View>
          <Text style={styles.actionArrow}>→</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => router.push('/referral/rewards')}
          activeOpacity={0.7}
        >
          <Text style={styles.actionIcon}>💰</Text>
          <View style={styles.actionInfo}>
            <Text style={styles.actionTitle}>Reward History</Text>
            <Text style={styles.actionSubtitle}>View earnings and payments</Text>
          </View>
          <Text style={styles.actionArrow}>→</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderPendingReferrals = () => (
    <View style={styles.pendingCard}>
      <View style={styles.pendingHeader}>
        <Text style={styles.pendingTitle}>Pending Referrals</Text>
        <TouchableOpacity
          onPress={() => router.push('/referral/tracking')}
          activeOpacity={0.7}
        >
          <Text style={styles.viewAllText}>View All</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.pendingList}>
        {pendingReferrals.slice(0, 3).map((referral) => (
          <View key={referral.id} style={styles.pendingItem}>
            <View style={styles.pendingInfo}>
              <Text style={styles.pendingName}>{referral.name}</Text>
              <View style={styles.pendingStatus}>
                <View style={[
                  styles.statusDot,
                  { backgroundColor: getStatusColor(referral.status) }
                ]} />
                <Text style={styles.statusText}>
                  {getStatusText(referral.status)}
                </Text>
              </View>
            </View>
            
            <View style={styles.pendingMeta}>
              <Text style={styles.pendingReward}>
                +${referral.rewardAmount.toFixed(2)}
              </Text>
              <Text style={styles.pendingDays}>
                {referral.daysLeft} days left
              </Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );

  const renderRewardStructure = () => (
    <View style={styles.rewardCard}>
      <Text style={styles.rewardTitle}>How Referrals Work</Text>
      
      <View style={styles.rewardSteps}>
        <View style={styles.rewardStep}>
          <View style={styles.stepNumber}>
            <Text style={styles.stepNumberText}>1</Text>
          </View>
          <View style={styles.stepInfo}>
            <Text style={styles.stepTitle}>Invite Friends</Text>
            <Text style={styles.stepDescription}>
              Share your code or link with friends
            </Text>
          </View>
        </View>
        
        <View style={styles.rewardStep}>
          <View style={styles.stepNumber}>
            <Text style={styles.stepNumberText}>2</Text>
          </View>
          <View style={styles.stepInfo}>
            <Text style={styles.stepTitle}>Friend Orders</Text>
            <Text style={styles.stepDescription}>
              They place their first order ($20+ minimum)
            </Text>
          </View>
        </View>
        
        <View style={styles.rewardStep}>
          <View style={styles.stepNumber}>
            <Text style={styles.stepNumberText}>3</Text>
          </View>
          <View style={styles.stepInfo}>
            <Text style={styles.stepTitle}>Both Get Rewarded</Text>
            <Text style={styles.stepDescription}>
              You get $5, they get $10 off their order
            </Text>
          </View>
        </View>
      </View>
      
      <View style={styles.bonusInfo}>
        <Text style={styles.bonusTitle}>🎉 Bonus Rewards</Text>
        <Text style={styles.bonusText}>
          Refer 5 friends this month and get an extra $25 bonus!
        </Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Referral Program</Text>
        <TouchableOpacity
          style={styles.helpButton}
          onPress={() => router.push('/referral/help')}
          activeOpacity={0.7}
        >
          <Text style={styles.helpIcon}>?</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderStatsCard()}
        {renderReferralCode()}
        {renderQuickActions()}
        {renderPendingReferrals()}
        {renderRewardStructure()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  helpButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  helpIcon: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666666',
  },
  scrollView: {
    flex: 1,
  },
  statsCard: {
    backgroundColor: '#F8F8F8',
    margin: 16,
    borderRadius: 16,
    padding: 20,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 16,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF4444',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
  rankSection: {
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  rankText: {
    fontSize: 14,
    color: '#2C2C2C',
    marginBottom: 8,
  },
  leaderboardButton: {
    backgroundColor: '#FF4444',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  leaderboardButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  codeCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  codeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
    textAlign: 'center',
  },
  codeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  codeText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FF4444',
    letterSpacing: 2,
    marginRight: 16,
  },
  copyButton: {
    backgroundColor: '#F0F0F0',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  copyButtonSuccess: {
    backgroundColor: '#E8F5E8',
  },
  copyButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666666',
  },
  copyButtonTextSuccess: {
    color: '#4CAF50',
  },
  codeActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  qrButton: {
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 0.45,
    alignItems: 'center',
  },
  qrButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1976D2',
  },
  shareButton: {
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 0.45,
    alignItems: 'center',
  },
  shareButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4CAF50',
  },
  qrCodeContainer: {
    alignItems: 'center',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  qrCodePlaceholder: {
    width: 120,
    height: 120,
    backgroundColor: '#F0F0F0',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  qrCodeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666666',
  },
  qrCodeSubtext: {
    fontSize: 10,
    color: '#999999',
    marginTop: 4,
  },
  qrCodeInstructions: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
  actionsCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  actionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  actionsList: {
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
  },
  actionIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  actionInfo: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  actionSubtitle: {
    fontSize: 12,
    color: '#666666',
  },
  actionArrow: {
    fontSize: 16,
    color: '#666666',
  },
  pendingCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  pendingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  pendingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  viewAllText: {
    fontSize: 12,
    color: '#FF4444',
    fontWeight: '500',
  },
  pendingList: {
    gap: 12,
  },
  pendingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 12,
  },
  pendingInfo: {
    flex: 1,
  },
  pendingName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  pendingStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 11,
    color: '#666666',
  },
  pendingMeta: {
    alignItems: 'flex-end',
  },
  pendingReward: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 2,
  },
  pendingDays: {
    fontSize: 10,
    color: '#666666',
  },
  rewardCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginBottom: 40,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  rewardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
    textAlign: 'center',
  },
  rewardSteps: {
    marginBottom: 20,
  },
  rewardStep: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FF4444',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  stepNumberText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  stepInfo: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  stepDescription: {
    fontSize: 12,
    color: '#666666',
    lineHeight: 16,
  },
  bonusInfo: {
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  bonusTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#F57C00',
    marginBottom: 4,
  },
  bonusText: {
    fontSize: 12,
    color: '#F57C00',
    textAlign: 'center',
    lineHeight: 16,
  },
});