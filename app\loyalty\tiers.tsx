import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  Animated,
  Dimensions,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

const { width } = Dimensions.get('window');

interface TierLevel {
  name: 'Bronze' | 'Silver' | 'Gold';
  minPoints: number;
  maxPoints: number | null;
  color: string;
  icon: string;
  multiplier: number;
  benefits: string[];
  exclusiveOffers: string[];
}

interface TierProgress {
  currentTier: 'Bronze' | 'Silver' | 'Gold';
  currentPoints: number;
  nextTierPoints: number | null;
  progressPercentage: number;
}

export default function TierSystemScreen() {
  const router = useRouter();
  const [selectedTier, setSelectedTier] = useState<'Bronze' | 'Silver' | 'Gold'>('Silver');
  const [progressAnimation] = useState(new Animated.Value(0));
  const [glowAnimation] = useState(new Animated.Value(0));

  const tiers: TierLevel[] = [
    {
      name: 'Bronze',
      minPoints: 0,
      maxPoints: 499,
      color: '#CD7F32',
      icon: '🥉',
      multiplier: 1,
      benefits: [
        '1 point per $1 spent',
        'Basic customer support',
        'Standard delivery fees',
        'Order tracking',
        'Basic app features',
      ],
      exclusiveOffers: [
        'Welcome bonus: 50 points',
        'Birthday month: 10% off',
      ],
    },
    {
      name: 'Silver',
      minPoints: 500,
      maxPoints: 1499,
      color: '#C0C0C0',
      icon: '🥈',
      multiplier: 1.5,
      benefits: [
        '1.5 points per $1 spent',
        'Priority customer support',
        'Free delivery on orders $25+',
        'Exclusive Silver offers',
        'Early access to new features',
        'Extended order history',
      ],
      exclusiveOffers: [
        'Monthly: Free delivery voucher',
        'Quarterly: 15% off coupon',
        'Silver-only restaurant deals',
      ],
    },
    {
      name: 'Gold',
      minPoints: 1500,
      maxPoints: null,
      color: '#FFD700',
      icon: '🥇',
      multiplier: 2,
      benefits: [
        '2 points per $1 spent',
        'VIP customer support',
        'Free delivery on all orders',
        'Early access to new restaurants',
        'Birthday rewards',
        'Exclusive Gold offers',
        'Personal account manager',
        'Premium app features',
      ],
      exclusiveOffers: [
        'Monthly: $10 dining credit',
        'Quarterly: Chef\'s table experience',
        'Annual: VIP restaurant tour',
        'Gold-exclusive menu items',
      ],
    },
  ];

  const userProgress: TierProgress = {
    currentTier: 'Silver',
    currentPoints: 1250,
    nextTierPoints: 1500,
    progressPercentage: 83.3,
  };

  useEffect(() => {
    // Animate progress bar
    Animated.timing(progressAnimation, {
      toValue: userProgress.progressPercentage,
      duration: 2000,
      useNativeDriver: false,
    }).start();

    // Animate tier glow effect
    Animated.loop(
      Animated.sequence([
        Animated.timing(glowAnimation, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(glowAnimation, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const getCurrentTier = () => tiers.find(tier => tier.name === userProgress.currentTier)!;
  const getNextTier = () => {
    const currentIndex = tiers.findIndex(tier => tier.name === userProgress.currentTier);
    return currentIndex < tiers.length - 1 ? tiers[currentIndex + 1] : null;
  };

  const renderTierProgress = () => {
    const currentTier = getCurrentTier();
    const nextTier = getNextTier();

    return (
      <View style={styles.progressSection}>
        <Text style={styles.progressTitle}>Your Progress</Text>
        
        <View style={styles.currentTierCard}>
          <Animated.View style={[
            styles.tierGlow,
            {
              backgroundColor: currentTier.color,
              opacity: glowAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [0.2, 0.4],
              }),
            },
          ]} />
          
          <View style={styles.tierHeader}>
            <Text style={styles.tierIcon}>{currentTier.icon}</Text>
            <View style={styles.tierInfo}>
              <Text style={[styles.tierName, { color: currentTier.color }]}>
                {currentTier.name} Member
              </Text>
              <Text style={styles.currentPoints}>
                {userProgress.currentPoints.toLocaleString()} points
              </Text>
            </View>
          </View>
          
          {nextTier && (
            <>
              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <Animated.View
                    style={[
                      styles.progressFill,
                      {
                        width: progressAnimation.interpolate({
                          inputRange: [0, 100],
                          outputRange: ['0%', '100%'],
                        }),
                        backgroundColor: currentTier.color,
                      },
                    ]}
                  />
                </View>
                <Text style={styles.progressText}>
                  {userProgress.nextTierPoints! - userProgress.currentPoints} points to {nextTier.name}
                </Text>
              </View>
              
              <View style={styles.nextTierPreview}>
                <Text style={styles.nextTierText}>
                  Next: {nextTier.icon} {nextTier.name}
                </Text>
                <Text style={styles.nextTierBenefit}>
                  Unlock {nextTier.multiplier}x points & free delivery on all orders
                </Text>
              </View>
            </>
          )}
        </View>
      </View>
    );
  };

  const renderTierTabs = () => (
    <View style={styles.tierTabs}>
      {tiers.map((tier) => (
        <TouchableOpacity
          key={tier.name}
          style={[
            styles.tierTab,
            selectedTier === tier.name && styles.tierTabActive,
            { borderColor: tier.color },
          ]}
          onPress={() => setSelectedTier(tier.name)}
          activeOpacity={0.7}
        >
          <Text style={styles.tierTabIcon}>{tier.icon}</Text>
          <Text style={[
            styles.tierTabName,
            selectedTier === tier.name && { color: tier.color },
          ]}>
            {tier.name}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderTierDetails = () => {
    const tier = tiers.find(t => t.name === selectedTier)!;
    const isCurrentTier = tier.name === userProgress.currentTier;
    const isUnlocked = userProgress.currentPoints >= tier.minPoints;

    return (
      <View style={styles.tierDetailsSection}>
        <View style={[styles.tierDetailsCard, { borderColor: tier.color }]}>
          <View style={styles.tierDetailsHeader}>
            <Text style={styles.tierDetailsIcon}>{tier.icon}</Text>
            <View style={styles.tierDetailsInfo}>
              <Text style={[styles.tierDetailsName, { color: tier.color }]}>
                {tier.name} Tier
              </Text>
              <Text style={styles.tierPointsRange}>
                {tier.minPoints.toLocaleString()}
                {tier.maxPoints ? ` - ${tier.maxPoints.toLocaleString()}` : '+'} points
              </Text>
            </View>
            
            {isCurrentTier && (
              <View style={[styles.currentBadge, { backgroundColor: tier.color }]}>
                <Text style={styles.currentBadgeText}>Current</Text>
              </View>
            )}
            
            {!isUnlocked && (
              <View style={styles.lockedBadge}>
                <Text style={styles.lockedIcon}>🔒</Text>
              </View>
            )}
          </View>
          
          <View style={styles.multiplierSection}>
            <Text style={styles.multiplierLabel}>Earning Rate</Text>
            <Text style={[styles.multiplierValue, { color: tier.color }]}>
              {tier.multiplier}x points per $1
            </Text>
          </View>
          
          <View style={styles.benefitsSection}>
            <Text style={styles.benefitsTitle}>Benefits</Text>
            <View style={styles.benefitsList}>
              {tier.benefits.map((benefit, index) => (
                <View key={index} style={styles.benefitItem}>
                  <Text style={[styles.benefitIcon, { color: tier.color }]}>✓</Text>
                  <Text style={styles.benefitText}>{benefit}</Text>
                </View>
              ))}
            </View>
          </View>
          
          <View style={styles.offersSection}>
            <Text style={styles.offersTitle}>Exclusive Offers</Text>
            <View style={styles.offersList}>
              {tier.exclusiveOffers.map((offer, index) => (
                <View key={index} style={styles.offerItem}>
                  <Text style={styles.offerIcon}>🎁</Text>
                  <Text style={styles.offerText}>{offer}</Text>
                </View>
              ))}
            </View>
          </View>
        </View>
      </View>
    );
  };

  const renderUpgradeSection = () => {
    const nextTier = getNextTier();
    if (!nextTier) return null;

    const pointsNeeded = nextTier.minPoints - userProgress.currentPoints;
    const ordersNeeded = Math.ceil(pointsNeeded / 25); // Assuming $25 average order

    return (
      <View style={styles.upgradeSection}>
        <Text style={styles.upgradeTitle}>Upgrade to {nextTier.name}</Text>
        
        <View style={styles.upgradeCard}>
          <View style={styles.upgradeStats}>
            <View style={styles.upgradeStat}>
              <Text style={styles.upgradeStatValue}>{pointsNeeded}</Text>
              <Text style={styles.upgradeStatLabel}>points needed</Text>
            </View>
            <View style={styles.upgradeStat}>
              <Text style={styles.upgradeStatValue}>{ordersNeeded}</Text>
              <Text style={styles.upgradeStatLabel}>orders needed</Text>
            </View>
          </View>
          
          <Text style={styles.upgradeDescription}>
            Keep ordering to unlock {nextTier.multiplier}x points and exclusive {nextTier.name} benefits!
          </Text>
          
          <TouchableOpacity
            style={[styles.upgradeButton, { backgroundColor: nextTier.color }]}
            onPress={() => router.push('/(tabs)')}
            activeOpacity={0.8}
          >
            <Text style={styles.upgradeButtonText}>Start Ordering</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Tier System</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderTierProgress()}
        {renderTierTabs()}
        {renderTierDetails()}
        {renderUpgradeSection()}
        
        {/* FAQ Section */}
        <View style={styles.faqSection}>
          <Text style={styles.faqTitle}>Frequently Asked Questions</Text>
          
          <View style={styles.faqItem}>
            <Text style={styles.faqQuestion}>How do I earn points?</Text>
            <Text style={styles.faqAnswer}>
              Earn points by placing orders, writing reviews, referring friends, and completing challenges.
            </Text>
          </View>
          
          <View style={styles.faqItem}>
            <Text style={styles.faqQuestion}>Do points expire?</Text>
            <Text style={styles.faqAnswer}>
              Points expire after 12 months of inactivity. Stay active to keep your points!
            </Text>
          </View>
          
          <View style={styles.faqItem}>
            <Text style={styles.faqQuestion}>Can I lose my tier status?</Text>
            <Text style={styles.faqAnswer}>
              Tier status is reviewed annually. Maintain your point balance to keep your tier benefits.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  progressSection: {
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  progressTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  currentTierCard: {
    backgroundColor: '#F8F8F8',
    borderRadius: 16,
    padding: 20,
    position: 'relative',
    overflow: 'hidden',
  },
  tierGlow: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 16,
  },
  tierHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    zIndex: 1,
  },
  tierIcon: {
    fontSize: 32,
    marginRight: 16,
  },
  tierInfo: {
    flex: 1,
  },
  tierName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  currentPoints: {
    fontSize: 16,
    color: '#666666',
  },
  progressContainer: {
    marginBottom: 16,
    zIndex: 1,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
  nextTierPreview: {
    alignItems: 'center',
    zIndex: 1,
  },
  nextTierText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  nextTierBenefit: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
  tierTabs: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 20,
    gap: 8,
  },
  tierTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    backgroundColor: '#FFFFFF',
  },
  tierTabActive: {
    backgroundColor: '#F8F8F8',
  },
  tierTabIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  tierTabName: {
    fontSize: 16,
    marginLeft: 8,
  },
  tierDetailsSection: {
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  tierDetailsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    borderWidth: 2,
    borderColor: '#E0E0E0',
  },
  tierDetailsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  tierDetailsIcon: {
    fontSize: 32,
    marginRight: 16,
  },
  tierDetailsInfo: {
    flex: 1,
  },
  tierDetailsName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  tierPointsRange: {
    fontSize: 16,
    color: '#666666',
  },
  currentBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 2,
  },
  currentBadgeText: {
    fontSize: 12,
    color: '#2C2C2C',
  },
  lockedBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 2,
  },
  lockedIcon: {
    fontSize: 16,
    color: '#2C2C2C',
  },
  multiplierSection: {
    marginBottom: 16,
  },
  multiplierLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  multiplierValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  benefitsSection: {
    marginBottom: 16,
  },
  benefitsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  benefitsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  benefitIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  benefitText: {
    fontSize: 14,
    color: '#666666',
  },
  offersSection: {
    marginBottom: 16,
  },
  offersTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  offersList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  offerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  offerIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  offerText: {
    fontSize: 14,
    color: '#666666',
  },
  upgradeSection: {
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  upgradeTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  upgradeCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    borderWidth: 2,
    borderColor: '#E0E0E0',
  },
  upgradeStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  upgradeStat: {
    flex: 1,
    alignItems: 'center',
  },
  upgradeStatValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  upgradeStatLabel: {
    fontSize: 14,
    color: '#666666',
  },
  upgradeDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 16,
  },
  upgradeButton: {
    backgroundColor: '#FFD700',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  upgradeButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  faqSection: {
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  faqTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  faqItem: {
    marginBottom: 16,
  },
  faqQuestion: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  faqAnswer: {
    fontSize: 14,
    color: '#666666',
  },
});




