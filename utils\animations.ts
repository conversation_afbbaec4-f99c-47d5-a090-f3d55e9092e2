import { Animated, Easing } from 'react-native';

export const createStaggeredAnimation = (
  animations: Animated.CompositeAnimation[],
  staggerDelay: number = 200
): Animated.CompositeAnimation => {
  return Animated.stagger(staggerDelay, animations);
};

export const createFadeInAnimation = (
  animatedValue: Animated.Value,
  duration: number = 600,
  delay: number = 0
): Animated.CompositeAnimation => {
  return Animated.sequence([
    Animated.delay(delay),
    Animated.timing(animatedValue, {
      toValue: 1,
      duration,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    }),
  ]);
};

export const createSlideInAnimation = (
  animatedValue: Animated.Value,
  fromValue: number = 50,
  duration: number = 600,
  delay: number = 0
): Animated.CompositeAnimation => {
  return Animated.sequence([
    Animated.delay(delay),
    Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: Easing.out(Easing.back(1.2)),
      useNativeDriver: true,
    }),
  ]);
};

export const createScaleInAnimation = (
  animatedValue: Animated.Value,
  fromValue: number = 0.8,
  duration: number = 600,
  delay: number = 0
): Animated.CompositeAnimation => {
  return Animated.sequence([
    Animated.delay(delay),
    Animated.timing(animatedValue, {
      toValue: 1,
      duration,
      easing: Easing.elastic(1.2),
      useNativeDriver: true,
    }),
  ]);
};

export const createPulseAnimation = (
  animatedValue: Animated.Value,
  minScale: number = 1,
  maxScale: number = 1.1,
  duration: number = 1000
): Animated.CompositeAnimation => {
  return Animated.loop(
    Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: maxScale,
        duration: duration / 2,
        easing: Easing.inOut(Easing.ease),
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: minScale,
        duration: duration / 2,
        easing: Easing.inOut(Easing.ease),
        useNativeDriver: true,
      }),
    ])
  );
};