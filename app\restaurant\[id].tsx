import { useThemeColor } from '@/hooks/useThemeColor';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function RestaurantScreen() {
  const { id } = useLocalSearchParams();
  const backgroundColor = useThemeColor({}, 'background');
  const [restaurant, setRestaurant] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setRestaurant({
        id,
        name: 'Sample Restaurant',
        cuisine: 'Italian',
        rating: 4.5,
        deliveryTime: '30-45 min',
        deliveryFee: '$2.99'
      });
      setLoading(false);
    }, 1000);
  }, [id]);

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <View style={styles.loadingContainer}>
          <Text>Loading restaurant...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.name}>{restaurant?.name}</Text>
          <Text style={styles.cuisine}>{restaurant?.cuisine}</Text>
          <Text style={styles.info}>⭐ {restaurant?.rating} • {restaurant?.deliveryTime}</Text>
        </View>

        <TouchableOpacity 
          style={styles.menuButton}
          onPress={() => router.push(`/menu/${id}`)}
        >
          <Text style={styles.menuButtonText}>View Menu</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { padding: 20 },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  header: { marginBottom: 24 },
  name: { fontSize: 28, fontWeight: 'bold', marginBottom: 8 },
  cuisine: { fontSize: 18, color: '#666', marginBottom: 8 },
  info: { fontSize: 16, color: '#888' },
  menuButton: { backgroundColor: '#007AFF', padding: 16, borderRadius: 8 },
  menuButtonText: { color: 'white', textAlign: 'center', fontWeight: 'bold', fontSize: 16 },
});
