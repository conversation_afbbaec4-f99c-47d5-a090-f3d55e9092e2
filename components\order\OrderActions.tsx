import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

interface OrderActionsProps {
  onTrackOrder: () => void;
  onContactSupport: () => void;
  onReorder: () => void;
  onShareOrder: () => void;
  showTracking: boolean;
  orderStatus: string;
}

export default function OrderActions({
  onTrackOrder,
  onContactSupport,
  onReorder,
  onShareOrder,
  showTracking,
  orderStatus,
}: OrderActionsProps) {
  return (
    <View style={styles.container}>
      {/* Primary Actions */}
      <View style={styles.primaryActions}>
        {showTracking && (
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={onTrackOrder}
            activeOpacity={0.8}
          >
            <Text style={styles.primaryButtonText}>Track Order</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[styles.primaryButton, styles.supportButton]}
          onPress={onContactSupport}
          activeOpacity={0.8}
        >
          <Text style={[styles.primaryButtonText, styles.supportButtonText]}>
            Contact Support
          </Text>
        </TouchableOpacity>
      </View>

      {/* Secondary Actions */}
      <View style={styles.secondaryActions}>
        <TouchableOpacity
          style={styles.secondaryButton}
          onPress={onReorder}
          activeOpacity={0.7}
        >
          <Text style={styles.secondaryButtonIcon}>🔄</Text>
          <Text style={styles.secondaryButtonText}>Reorder</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.secondaryButton}
          onPress={onShareOrder}
          activeOpacity={0.7}
        >
          <Text style={styles.secondaryButtonIcon}>📤</Text>
          <Text style={styles.secondaryButtonText}>Share</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  primaryActions: {
    gap: 12,
    marginBottom: 16,
  },
  primaryButton: {
    backgroundColor: '#FF4444',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  supportButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#FF4444',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  supportButtonText: {
    color: '#FF4444',
  },
  secondaryActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  secondaryButton: {
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  secondaryButtonIcon: {
    fontSize: 24,
    marginBottom: 4,
  },
  secondaryButtonText: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
  },
});