import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface EmptyStateProps {
  query: string;
  onClearFilters: () => void;
  hasFilters: boolean;
}

export default function EmptyState({ query, onClearFilters, hasFilters }: EmptyStateProps) {
  const handleClearFilters = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    onClearFilters();
  };

  return (
    <View style={styles.container}>
      <View style={styles.illustration}>
        <Text style={styles.illustrationText}>🔍</Text>
      </View>
      
      <Text style={styles.title}>No restaurants found</Text>
      
      <Text style={styles.subtitle}>
        We couldn't find any restaurants matching "{query}"
        {hasFilters ? ' with your current filters' : ''}
      </Text>
      
      <View style={styles.suggestions}>
        <Text style={styles.suggestionsTitle}>Try searching for:</Text>
        <View style={styles.suggestionChips}>
          {['Pizza', 'Burgers', 'Chinese', 'Italian', 'Sushi'].map((suggestion, index) => (
            <View key={index} style={styles.suggestionChip}>
              <Text style={styles.suggestionText}>{suggestion}</Text>
            </View>
          ))}
        </View>
      </View>
      
      {hasFilters && (
        <TouchableOpacity 
          style={styles.clearButton}
          onPress={handleClearFilters}
          activeOpacity={0.7}
        >
          <Text style={styles.clearButtonText}>Clear all filters</Text>
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  illustration: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  illustrationText: {
    fontSize: 48,
    opacity: 0.5,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  suggestions: {
    alignItems: 'center',
    marginBottom: 32,
  },
  suggestionsTitle: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 16,
  },
  suggestionChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  suggestionChip: {
    backgroundColor: '#F5F5F5',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    margin: 4,
  },
  suggestionText: {
    fontSize: 14,
    color: '#666666',
  },
  clearButton: {
    backgroundColor: '#FF4444',
    borderRadius: 12,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  clearButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});