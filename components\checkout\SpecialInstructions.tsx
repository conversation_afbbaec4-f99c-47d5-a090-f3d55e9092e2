import React from 'react';
import {
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

interface SpecialInstructionsProps {
  instructions: {
    delivery: string;
    restaurant: string;
    contactPreference: 'call' | 'text';
  };
  onInstructionsChange: (instructions: {
    delivery: string;
    restaurant: string;
    contactPreference: 'call' | 'text';
  }) => void;
}

export default function SpecialInstructions({
  instructions,
  onInstructionsChange,
}: SpecialInstructionsProps) {
  const updateInstructions = (field: string, value: string | 'call' | 'text') => {
    onInstructionsChange({
      ...instructions,
      [field]: value,
    });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Special Instructions</Text>
      
      {/* Delivery Instructions */}
      <View style={styles.inputSection}>
        <Text style={styles.inputLabel}>Delivery Instructions</Text>
        <TextInput
          style={styles.textInput}
          placeholder="e.g., Ring doorbell, Leave at door"
          value={instructions.delivery}
          onChangeText={(text) => updateInstructions('delivery', text)}
          multiline
          numberOfLines={3}
        />
      </View>

      {/* Restaurant Instructions */}
      <View style={styles.inputSection}>
        <Text style={styles.inputLabel}>Restaurant Instructions</Text>
        <TextInput
          style={styles.textInput}
          placeholder="e.g., Extra spicy, No onions"
          value={instructions.restaurant}
          onChangeText={(text) => updateInstructions('restaurant', text)}
          multiline
          numberOfLines={3}
        />
      </View>

      {/* Contact Preference */}
      <View style={styles.inputSection}>
        <Text style={styles.inputLabel}>Contact Preference</Text>
        <View style={styles.contactOptions}>
          <TouchableOpacity
            style={[
              styles.contactOption,
              instructions.contactPreference === 'call' && styles.contactOptionSelected,
            ]}
            onPress={() => updateInstructions('contactPreference', 'call')}
          >
            <Text style={[
              styles.contactOptionText,
              instructions.contactPreference === 'call' && styles.contactOptionTextSelected,
            ]}>
              📞 Call
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.contactOption,
              instructions.contactPreference === 'text' && styles.contactOptionSelected,
            ]}
            onPress={() => updateInstructions('contactPreference', 'text')}
          >
            <Text style={[
              styles.contactOptionText,
              instructions.contactPreference === 'text' && styles.contactOptionTextSelected,
            ]}>
              💬 Text
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
    marginBottom: 16,
  },
  inputSection: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#1A1A1A',
    textAlignVertical: 'top',
    minHeight: 80,
  },
  contactOptions: {
    flexDirection: 'row',
    gap: 12,
  },
  contactOption: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  contactOptionSelected: {
    borderColor: '#FF4444',
    backgroundColor: '#FFF5F5',
  },
  contactOptionText: {
    fontSize: 16,
    color: '#666666',
  },
  contactOptionTextSelected: {
    color: '#FF4444',
    fontWeight: '600',
  },
});