import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Linking,
  Alert,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { OrderInfo } from '@/app/order-success';

interface OrderDetailsProps {
  orderInfo: OrderInfo;
}

export default function OrderDetails({ orderInfo }: OrderDetailsProps) {
  const [itemsExpanded, setItemsExpanded] = useState(false);

  const handleContactRestaurant = (type: 'phone' | 'chat') => {
    if (type === 'phone') {
      Linking.openURL(`tel:${orderInfo.restaurant.phone}`);
    } else {
      Alert.alert('Chat', 'Opening chat with restaurant...');
    }
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const toggleItemsExpanded = () => {
    setItemsExpanded(!itemsExpanded);
    Haptics.selectionAsync();
  };

  return (
    <View style={styles.container}>
      {/* Restaurant Info */}
      <View style={styles.section}>
        <View style={styles.restaurantHeader}>
          <View style={styles.restaurantInfo}>
            <Text style={styles.restaurantIcon}>{orderInfo.restaurant.image}</Text>
            <View style={styles.restaurantDetails}>
              <Text style={styles.restaurantName}>{orderInfo.restaurant.name}</Text>
              <Text style={styles.orderTime}>
                Ordered at {orderInfo.orderTime.toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </Text>
            </View>
          </View>
          
          <View style={styles.contactButtons}>
            <TouchableOpacity
              style={styles.contactButton}
              onPress={() => handleContactRestaurant('phone')}
              activeOpacity={0.7}
            >
              <Text style={styles.contactButtonIcon}>📞</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.contactButton}
              onPress={() => handleContactRestaurant('chat')}
              activeOpacity={0.7}
            >
              <Text style={styles.contactButtonIcon}>💬</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Items Summary */}
      <View style={styles.section}>
        <TouchableOpacity
          style={styles.itemsSummaryHeader}
          onPress={toggleItemsExpanded}
          activeOpacity={0.7}
        >
          <Text style={styles.itemsCount}>
            {orderInfo.items.length} item{orderInfo.items.length !== 1 ? 's' : ''}
          </Text>
          <Text style={styles.expandIcon}>
            {itemsExpanded ? '▲' : '▼'}
          </Text>
        </TouchableOpacity>

        {itemsExpanded && (
          <View style={styles.itemsList}>
            {orderInfo.items.map((item) => (
              <View key={item.id} style={styles.itemRow}>
                <View style={styles.itemInfo}>
                  <Text style={styles.itemName}>
                    {item.quantity}x {item.name}
                  </Text>
                  {item.customizations && item.customizations.length > 0 && (
                    <Text style={styles.itemCustomizations}>
                      {item.customizations.join(', ')}
                    </Text>
                  )}
                </View>
                <Text style={styles.itemPrice}>
                  ${(item.price * item.quantity).toFixed(2)}
                </Text>
              </View>
            ))}
            
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalAmount}>${orderInfo.total.toFixed(2)}</Text>
            </View>
          </View>
        )}
      </View>

      {/* Payment & Delivery Info */}
      <View style={styles.section}>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Payment Method</Text>
          <Text style={styles.infoValue}>{orderInfo.paymentMethod}</Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Delivery Address</Text>
          <Text style={styles.infoValue}>{orderInfo.deliveryAddress}</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
  },
  section: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  restaurantHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  restaurantInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  restaurantIcon: {
    fontSize: 40,
    marginRight: 12,
  },
  restaurantDetails: {
    flex: 1,
  },
  restaurantName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  orderTime: {
    fontSize: 14,
    color: '#666666',
  },
  contactButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  contactButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F8F8F8',
    justifyContent: 'center',
    alignItems: 'center',
  },
  contactButtonIcon: {
    fontSize: 18,
  },
  itemsSummaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  itemsCount: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
  },
  expandIcon: {
    fontSize: 14,
    color: '#666666',
  },
  itemsList: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  itemInfo: {
    flex: 1,
    marginRight: 12,
  },
  itemName: {
    fontSize: 14,
    color: '#2C2C2C',
    marginBottom: 2,
  },
  itemCustomizations: {
    fontSize: 12,
    color: '#666666',
    fontStyle: 'italic',
  },
  itemPrice: {
    fontSize: 14,
    fontWeight: '500',
    color: '#2C2C2C',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  totalAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666666',
    flex: 1,
    marginRight: 12,
  },
  infoValue: {
    fontSize: 14,
    color: '#2C2C2C',
    textAlign: 'right',
    flex: 1,
  },
});