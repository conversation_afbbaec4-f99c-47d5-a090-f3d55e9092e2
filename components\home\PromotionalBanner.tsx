import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';

const { width: screenWidth } = Dimensions.get('window');
const BANNER_WIDTH = screenWidth - 32;
const BANNER_HEIGHT = 160;

interface Promotion {
  id: string;
  title: string;
  subtitle: string;
  image: string;
  backgroundColor: string;
  textColor: string;
  ctaText: string;
  discount?: string;
}

const PROMOTIONS: Promotion[] = [
  {
    id: '1',
    title: 'Free Delivery',
    subtitle: 'On orders over $25',
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
    backgroundColor: '#FF4444',
    textColor: '#FFFFFF',
    ctaText: 'Order Now',
    discount: 'FREE',
  },
  {
    id: '2',
    title: '50% Off Pizza',
    subtitle: 'Limited time offer',
    image: 'https://images.unsplash.com/photo-1513104890138-7c749659a591?w=400',
    backgroundColor: '#34A853',
    textColor: '#FFFFFF',
    ctaText: 'Get Deal',
    discount: '50%',
  },
  {
    id: '3',
    title: 'New Restaurant',
    subtitle: 'Try authentic Asian cuisine',
    image: 'https://images.unsplash.com/photo-1559847844-d721426d6edc?w=400',
    backgroundColor: '#FBBC04',
    textColor: '#2C2C2C',
    ctaText: 'Explore',
  },
];

export default function PromotionalBanner() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);
  const autoScrollTimer = useRef<NodeJS.Timeout>();

  useEffect(() => {
    startAutoScroll();
    return () => {
      if (autoScrollTimer.current) {
        clearInterval(autoScrollTimer.current);
      }
    };
  }, []);

  const startAutoScroll = () => {
    autoScrollTimer.current = setInterval(() => {
      const nextIndex = (currentIndex + 1) % PROMOTIONS.length;
      scrollToIndex(nextIndex);
    }, 4000);
  };

  const stopAutoScroll = () => {
    if (autoScrollTimer.current) {
      clearInterval(autoScrollTimer.current);
    }
  };

  const scrollToIndex = (index: number) => {
    setCurrentIndex(index);
    scrollViewRef.current?.scrollTo({
      x: index * BANNER_WIDTH,
      animated: true,
    });
  };

  const handleScroll = (event: any) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(offsetX / BANNER_WIDTH);
    if (index !== currentIndex) {
      setCurrentIndex(index);
    }
  };

  const handleBannerPress = (promotion: Promotion) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    console.log('Banner pressed:', promotion.title);
    // Navigate to promotion details or restaurant
  };

  const handleScrollBegin = () => {
    stopAutoScroll();
  };

  const handleScrollEnd = () => {
    startAutoScroll();
  };

  return (
    <View style={styles.container}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        onScrollBeginDrag={handleScrollBegin}
        onScrollEndDrag={handleScrollEnd}
        scrollEventThrottle={16}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
      >
        {PROMOTIONS.map((promotion, index) => (
          <PromotionCard
            key={promotion.id}
            promotion={promotion}
            onPress={() => handleBannerPress(promotion)}
            index={index}
            currentIndex={currentIndex}
          />
        ))}
      </ScrollView>

      {/* Page Indicators */}
      <View style={styles.indicators}>
        {PROMOTIONS.map((_, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.indicator,
              index === currentIndex && styles.indicatorActive,
            ]}
            onPress={() => scrollToIndex(index)}
          />
        ))}
      </View>
    </View>
  );
}

interface PromotionCardProps {
  promotion: Promotion;
  onPress: () => void;
  index: number;
  currentIndex: number;
}

function PromotionCard({ promotion, onPress, index, currentIndex }: PromotionCardProps) {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    const isActive = index === currentIndex;
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: isActive ? 1 : 0.95,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: isActive ? 1 : 0.8,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, [currentIndex, index]);

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: index === currentIndex ? 1 : 0.95,
      useNativeDriver: true,
    }).start();
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={1}
    >
      <Animated.View
        style={[
          styles.bannerCard,
          {
            transform: [{ scale: scaleAnim }],
            opacity: opacityAnim,
          },
        ]}
      >
        <LinearGradient
          colors={[promotion.backgroundColor, `${promotion.backgroundColor}CC`]}
          style={styles.bannerGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {/* Background Image */}
          <Image
            source={{ uri: promotion.image }}
            style={styles.bannerImage}
            resizeMode="cover"
          />

          {/* Overlay Content */}
          <View style={styles.bannerOverlay}>
            <View style={styles.bannerContent}>
              {promotion.discount && (
                <View style={styles.discountBadge}>
                  <Text style={styles.discountText}>{promotion.discount}</Text>
                </View>
              )}
              
              <Text style={[styles.bannerTitle, { color: promotion.textColor }]}>
                {promotion.title}
              </Text>
              <Text style={[styles.bannerSubtitle, { color: promotion.textColor }]}>
                {promotion.subtitle}
              </Text>
              
              <TouchableOpacity style={styles.ctaButton}>
                <Text style={styles.ctaButtonText}>{promotion.ctaText}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </LinearGradient>
      </Animated.View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  scrollView: {
    marginHorizontal: 16,
  },
  scrollContent: {
    paddingRight: 16,
  },
  bannerCard: {
    width: BANNER_WIDTH,
    height: BANNER_HEIGHT,
    borderRadius: 12,
    overflow: 'hidden',
    marginRight: 16,
  },
  bannerGradient: {
    flex: 1,
    position: 'relative',
  },
  bannerImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.3,
  },
  bannerOverlay: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  bannerContent: {
    flex: 1,
    justifyContent: 'center',
  },
  discountBadge: {
    alignSelf: 'flex-start',
    backgroundColor: '#FFD700',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
  },
  discountText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  bannerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  bannerSubtitle: {
    fontSize: 14,
    opacity: 0.9,
    marginBottom: 16,
  },
  ctaButton: {
    alignSelf: 'flex-start',
    backgroundColor: '#FFD700',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  ctaButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  indicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 12,
    gap: 6,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#E0E0E0',
  },
  indicatorActive: {
    backgroundColor: '#FF4444',
    width: 20,
  },
});