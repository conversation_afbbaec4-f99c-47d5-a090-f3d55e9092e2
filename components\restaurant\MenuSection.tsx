import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Image } from 'expo-image';
import * as Haptics from 'expo-haptics';

import AddToCartButton from '@/components/restaurant/AddToCartButton';

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  isVeg: boolean;
  spiceLevel: number;
  isBestseller: boolean;
  category: string;
}

interface CartItem {
  id: string;
  quantity: number;
}

interface MenuSectionProps {
  category: string;
  items: MenuItem[];
  cartItems: CartItem[];
  onAddToCart: (item: MenuItem) => void;
  onUpdateQuantity: (itemId: string, quantity: number) => void;
  onLayout: (y: number) => void;
}

export default function MenuSection({
  category,
  items,
  cartItems,
  onAddToCart,
  onUpdateQuantity,
  onLayout,
}: MenuSectionProps) {
  const getItemQuantity = (itemId: string) => {
    const cartItem = cartItems.find(item => item.id === itemId);
    return cartItem ? cartItem.quantity : 0;
  };

  const renderSpiceLevel = (level: number) => {
    const peppers = [];
    for (let i = 0; i < 3; i++) {
      peppers.push(
        <Text
          key={i}
          style={[
            styles.pepper,
            { opacity: i < level ? 1 : 0.3 }
          ]}
        >
          🌶️
        </Text>
      );
    }
    return peppers;
  };

  const handleItemPress = (item: MenuItem) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    console.log('Item pressed:', item.name);
    // Navigate to item detail or show modal
  };

  return (
    <View
      style={styles.container}
      onLayout={(event) => {
        const { y } = event.nativeEvent.layout;
        onLayout(y);
      }}
    >
      <Text style={styles.sectionTitle}>{category}</Text>
      
      {items.map((item) => {
        const quantity = getItemQuantity(item.id);
        
        return (
          <TouchableOpacity
            key={item.id}
            style={styles.menuItem}
            onPress={() => handleItemPress(item)}
            activeOpacity={0.9}
          >
            <View style={styles.itemContent}>
              <View style={styles.itemInfo}>
                {/* Veg/Non-veg indicator */}
                <View style={styles.indicators}>
                  <View style={[
                    styles.vegIndicator,
                    { borderColor: item.isVeg ? '#34A853' : '#FF4444' }
                  ]}>
                    <View style={[
                      styles.vegDot,
                      { backgroundColor: item.isVeg ? '#34A853' : '#FF4444' }
                    ]} />
                  </View>
                  
                  {item.isBestseller && (
                    <View style={styles.bestsellerBadge}>
                      <Text style={styles.bestsellerText}>BESTSELLER</Text>
                    </View>
                  )}
                </View>
                
                <Text style={styles.itemName}>{item.name}</Text>
                
                <View style={styles.priceSpiceContainer}>
                  <Text style={styles.itemPrice}>${item.price.toFixed(2)}</Text>
                  {item.spiceLevel > 0 && (
                    <View style={styles.spiceLevel}>
                      {renderSpiceLevel(item.spiceLevel)}
                    </View>
                  )}
                </View>
                
                <Text style={styles.itemDescription} numberOfLines={2}>
                  {item.description}
                </Text>
              </View>
              
              <View style={styles.itemImageContainer}>
                <Image
                  source={{ uri: item.image }}
                  style={styles.itemImage}
                  contentFit="cover"
                />
                
                <View style={styles.addButtonContainer}>
                  <AddToCartButton
                    item={item}
                    quantity={quantity}
                    onAdd={() => onAddToCart(item)}
                    onUpdateQuantity={(newQuantity) => onUpdateQuantity(item.id, newQuantity)}
                  />
                </View>
              </View>
            </View>
          </TouchableOpacity>
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  menuItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  itemContent: {
    flexDirection: 'row',
    padding: 12,
  },
  itemInfo: {
    flex: 1,
    marginRight: 12,
  },
  indicators: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  vegIndicator: {
    width: 16,
    height: 16,
    borderWidth: 1.5,
    borderRadius: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  vegDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  bestsellerBadge: {
    backgroundColor: '#FF4444',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  bestsellerText: {
    fontSize: 8,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  priceSpiceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF4444',
    marginRight: 8,
  },
  spiceLevel: {
    flexDirection: 'row',
  },
  pepper: {
    fontSize: 10,
    marginRight: 1,
  },
  itemDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  itemImageContainer: {
    position: 'relative',
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  addButtonContainer: {
    position: 'absolute',
    bottom: -8,
    right: -8,
  },
});