import { useThemeColor } from '@/hooks/useThemeColor';
import React from 'react';
import { ScrollView, StyleSheet, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function TrackingHelpScreen() {
  const backgroundColor = useThemeColor({}, 'background');

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Delivery Tracking Help</Text>
        <Text style={styles.subtitle}>Learn how to track your orders</Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>How to Track Your Order</Text>
          <Text style={styles.text}>
            1. Go to "My Orders" from the main menu{'\n'}
            2. Select your current order{'\n'}
            3. View real-time tracking updates{'\n'}
            4. Contact driver if needed
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { padding: 20 },
  title: { fontSize: 24, fontWeight: 'bold', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#666', marginBottom: 20 },
  section: { marginBottom: 20 },
  sectionTitle: { fontSize: 18, fontWeight: 'bold', marginBottom: 8 },
  text: { fontSize: 16, lineHeight: 24 },
});