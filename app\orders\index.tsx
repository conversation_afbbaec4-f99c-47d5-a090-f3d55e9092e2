import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { useThemeColor } from '@/hooks/useThemeColor';

export default function OrdersScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const [orders, setOrders] = useState([]);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setOrders([
        { 
          id: '1', 
          restaurant: 'Pizza Palace', 
          total: '$24.99', 
          status: 'delivered',
          date: '2024-01-15'
        },
        { 
          id: '2', 
          restaurant: 'Burger House', 
          total: '$18.50', 
          status: 'preparing',
          date: '2024-01-16'
        },
      ]);
    }, 1000);
  }, []);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Order History</Text>
        
        {orders.map((order) => (
          <TouchableOpacity
            key={order.id}
            style={styles.orderItem}
            onPress={() => router.push(`/orders/${order.id}`)}
          >
            <View style={styles.orderInfo}>
              <Text style={styles.restaurantName}>{order.restaurant}</Text>
              <Text style={styles.orderDate}>{order.date}</Text>
              <Text style={styles.orderStatus}>Status: {order.status}</Text>
            </View>
            <Text style={styles.orderTotal}>{order.total}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { padding: 20 },
  title: { fontSize: 28, fontWeight: 'bold', marginBottom: 20 },
  orderItem: { 
    flexDirection: 'row', 
    justifyContent: 'space-between',
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  orderInfo: { flex: 1 },
  restaurantName: { fontSize: 18, fontWeight: 'bold', marginBottom: 4 },
  orderDate: { fontSize: 14, color: '#666', marginBottom: 4 },
  orderStatus: { fontSize: 14, color: '#007AFF' },
  orderTotal: { fontSize: 16, fontWeight: 'bold', color: '#007AFF' },
});