import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

interface SupportOption {
  id: string;
  title: string;
  description: string;
  icon: string;
  estimatedTime: string;
  route: string;
}

export default function SupportHubScreen() {
  const [searchQuery, setSearchQuery] = useState('');

  const supportOptions: SupportOption[] = [
    {
      id: 'order_issues',
      title: 'Order Issues',
      description: 'Problem with current order, missing items, wrong order',
      icon: '🍽️',
      estimatedTime: '< 2 min',
      route: '/support/chat/order',
    },
    {
      id: 'payment_help',
      title: 'Payment Help',
      description: 'Payment or billing questions, refunds, charges',
      icon: '💳',
      estimatedTime: '< 5 min',
      route: '/support/chat/payment',
    },
    {
      id: 'account_support',
      title: 'Account Support',
      description: 'Profile and account help, login issues, settings',
      icon: '👤',
      estimatedTime: '< 3 min',
      route: '/support/chat/account',
    },
    {
      id: 'delivery_tracking',
      title: 'Delivery Tracking',
      description: 'Track your order, delivery updates, driver contact',
      icon: '🚚',
      estimatedTime: '< 1 min',
      route: '/support/tracking',
    },
    {
      id: 'restaurant_issues',
      title: 'Restaurant Issues',
      description: 'Restaurant closed, menu problems, availability',
      icon: '🏪',
      estimatedTime: '< 3 min',
      route: '/support/chat/restaurant',
    },
    {
      id: 'general_inquiry',
      title: 'General Inquiry',
      description: 'Other questions, feedback, suggestions',
      icon: '💬',
      estimatedTime: '< 10 min',
      route: '/support/chat/general',
    },
  ];

  const quickActions = [
    {
      title: 'Track Order',
      icon: '📍',
      action: () => router.push('/orders/track'),
    },
    {
      title: 'FAQ',
      icon: '❓',
      action: () => router.push('/support/faq'),
    },
    {
      title: 'Call Support',
      icon: '📞',
      action: () => router.push('/support/call'),
    },
    {
      title: 'My Tickets',
      icon: '🎫',
      action: () => router.push('/support/tickets'),
    },
  ];

  const filteredOptions = supportOptions.filter(option =>
    option.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    option.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleOptionPress = (option: SupportOption) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push(option.route);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Help & Support</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>How can we help you?</Text>
          <Text style={styles.welcomeSubtitle}>
            Choose a category below or search for specific help
          </Text>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Text style={styles.searchIcon}>🔍</Text>
          <TextInput
            style={styles.searchInput}
            placeholder="Search for help..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#999999"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => setSearchQuery('')}
              activeOpacity={0.7}
            >
              <Text style={styles.clearIcon}>✕</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActions}>
            {quickActions.map((action, index) => (
              <TouchableOpacity
                key={index}
                style={styles.quickActionButton}
                onPress={action.action}
                activeOpacity={0.8}
              >
                <Text style={styles.quickActionIcon}>{action.icon}</Text>
                <Text style={styles.quickActionText}>{action.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Support Options */}
        <View style={styles.supportOptionsContainer}>
          <Text style={styles.sectionTitle}>Get Help With</Text>
          
          {filteredOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={styles.supportOption}
              onPress={() => handleOptionPress(option)}
              activeOpacity={0.8}
            >
              <View style={styles.optionIconContainer}>
                <Text style={styles.optionIcon}>{option.icon}</Text>
              </View>
              
              <View style={styles.optionContent}>
                <View style={styles.optionHeader}>
                  <Text style={styles.optionTitle}>{option.title}</Text>
                  <View style={styles.estimatedTimeContainer}>
                    <Text style={styles.estimatedTime}>{option.estimatedTime}</Text>
                  </View>
                </View>
                <Text style={styles.optionDescription}>{option.description}</Text>
              </View>
              
              <Text style={styles.optionArrow}>→</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Contact Information */}
        <View style={styles.contactSection}>
          <Text style={styles.sectionTitle}>Other Ways to Reach Us</Text>
          
          <View style={styles.contactOptions}>
            <TouchableOpacity
              style={styles.contactOption}
              onPress={() => router.push('/support/call')}
              activeOpacity={0.8}
            >
              <Text style={styles.contactIcon}>📞</Text>
              <View style={styles.contactContent}>
                <Text style={styles.contactTitle}>Phone Support</Text>
                <Text style={styles.contactSubtitle}>Available 24/7</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.contactOption}
              onPress={() => router.push('/support/email')}
              activeOpacity={0.8}
            >
              <Text style={styles.contactIcon}>📧</Text>
              <View style={styles.contactContent}>
                <Text style={styles.contactTitle}>Email Support</Text>
                <Text style={styles.contactSubtitle}>Response within 24h</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  headerRight: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  welcomeSection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    alignItems: 'center',
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 8,
    textAlign: 'center',
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 24,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchIcon: {
    fontSize: 16,
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#2C2C2C',
  },
  clearButton: {
    padding: 4,
  },
  clearIcon: {
    fontSize: 14,
    color: '#999999',
  },
  quickActionsContainer: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    paddingVertical: 16,
    paddingHorizontal: 8,
    borderRadius: 12,
    marginHorizontal: 4,
  },
  quickActionIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  quickActionText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#2C2C2C',
    textAlign: 'center',
  },
  supportOptionsContainer: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  supportOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
  },
  optionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionIcon: {
    fontSize: 24,
  },
  optionContent: {
    flex: 1,
  },
  optionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  estimatedTimeContainer: {
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  estimatedTime: {
    fontSize: 12,
    color: '#34A853',
    fontWeight: '500',
  },
  optionDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  optionArrow: {
    fontSize: 18,
    color: '#CCCCCC',
    marginLeft: 12,
  },
  contactSection: {
    paddingHorizontal: 20,
  },
  contactOptions: {
    gap: 12,
  },
  contactOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
  },
  contactIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  contactContent: {
    flex: 1,
  },
  contactTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  contactSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
});