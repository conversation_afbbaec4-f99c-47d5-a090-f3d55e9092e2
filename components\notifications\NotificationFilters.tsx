import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { NotificationFilter, Notification } from '@/app/notifications';

interface NotificationFiltersProps {
  currentFilter: NotificationFilter;
  onFilterChange: (filter: NotificationFilter) => void;
  notifications: Notification[];
}

export default function NotificationFilters({
  currentFilter,
  onFilterChange,
  notifications,
}: NotificationFiltersProps) {
  const getFilterCount = (filter: NotificationFilter) => {
    if (filter === 'all') {
      return notifications.filter(n => !n.isRead).length;
    }
    const type = filter.slice(0, -1); // Remove 's' from filter
    return notifications.filter(n => n.type === type && !n.isRead).length;
  };

  const filters: Array<{
    key: NotificationFilter;
    label: string;
    icon: string;
  }> = [
    { key: 'all', label: 'All', icon: '🔔' },
    { key: 'orders', label: 'Orders', icon: '🍽️' },
    { key: 'offers', label: 'Offers', icon: '🎁' },
    { key: 'updates', label: 'Updates', icon: '⭐' },
  ];

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {filters.map((filter) => {
          const count = getFilterCount(filter.key);
          const isActive = currentFilter === filter.key;

          return (
            <TouchableOpacity
              key={filter.key}
              style={[
                styles.filterTab,
                isActive && styles.filterTabActive,
              ]}
              onPress={() => onFilterChange(filter.key)}
              activeOpacity={0.7}
            >
              <View style={styles.filterContent}>
                <Text style={styles.filterIcon}>{filter.icon}</Text>
                <Text style={[
                  styles.filterLabel,
                  isActive && styles.filterLabelActive,
                ]}>
                  {filter.label}
                </Text>
                {count > 0 && (
                  <View style={[
                    styles.badge,
                    isActive && styles.badgeActive,
                  ]}>
                    <Text style={[
                      styles.badgeText,
                      isActive && styles.badgeTextActive,
                    ]}>
                      {count > 99 ? '99+' : count}
                    </Text>
                  </View>
                )}
              </View>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F8F8F8',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  filterTab: {
    marginRight: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  filterTabActive: {
    backgroundColor: '#FF4444',
    borderColor: '#FF4444',
  },
  filterContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666666',
  },
  filterLabelActive: {
    color: '#FFFFFF',
  },
  badge: {
    marginLeft: 6,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeActive: {
    backgroundColor: '#FFFFFF',
  },
  badgeText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  badgeTextActive: {
    color: '#FF4444',
  },
});