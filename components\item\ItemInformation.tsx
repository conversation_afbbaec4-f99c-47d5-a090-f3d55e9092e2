import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  images: string[];
  isVeg: boolean;
  spiceLevel: number;
  isBestseller: boolean;
  category: string;
  restaurant: string;
  ingredients?: string[];
  allergens?: string[];
  nutrition?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  prepTime?: number;
  originalPrice?: number;
  discount?: number;
}

interface ItemInformationProps {
  item: MenuItem;
}

export default function ItemInformation({ item }: ItemInformationProps) {
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [showIngredients, setShowIngredients] = useState(false);
  const [showNutrition, setShowNutrition] = useState(false);

  const toggleDescription = () => {
    Haptics.selectionAsync();
    setShowFullDescription(!showFullDescription);
  };

  const toggleIngredients = () => {
    Haptics.selectionAsync();
    setShowIngredients(!showIngredients);
  };

  const toggleNutrition = () => {
    Haptics.selectionAsync();
    setShowNutrition(!showNutrition);
  };

  const renderSpiceLevel = () => {
    const peppers = [];
    for (let i = 0; i < 4; i++) {
      peppers.push(
        <Text
          key={i}
          style={[
            styles.pepper,
            { opacity: i < item.spiceLevel ? 1 : 0.3 }
          ]}
        >
          🌶️
        </Text>
      );
    }
    return peppers;
  };

  return (
    <View style={styles.container}>
      {/* Item Name */}
      <Text style={styles.itemName}>{item.name}</Text>

      {/* Restaurant Name */}
      <TouchableOpacity activeOpacity={0.7}>
        <Text style={styles.restaurantName}>from {item.restaurant}</Text>
      </TouchableOpacity>

      {/* Price Section */}
      <View style={styles.priceContainer}>
        <Text style={styles.currentPrice}>${item.price.toFixed(2)}</Text>
        {item.originalPrice && (
          <Text style={styles.originalPrice}>${item.originalPrice.toFixed(2)}</Text>
        )}
        {item.discount && (
          <View style={styles.discountBadge}>
            <Text style={styles.discountText}>{item.discount}% OFF</Text>
          </View>
        )}
      </View>

      {/* Preparation Time */}
      {item.prepTime && (
        <View style={styles.prepTimeContainer}>
          <Text style={styles.prepTimeIcon}>🕐</Text>
          <Text style={styles.prepTimeText}>{item.prepTime} mins prep time</Text>
        </View>
      )}

      {/* Spice Level */}
      {item.spiceLevel > 0 && (
        <View style={styles.spiceLevelContainer}>
          <Text style={styles.spiceLevelLabel}>Spice Level: </Text>
          <View style={styles.spiceLevel}>
            {renderSpiceLevel()}
          </View>
        </View>
      )}

      {/* Description */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Description</Text>
        <Text
          style={styles.description}
          numberOfLines={showFullDescription ? undefined : 3}
        >
          {item.description}
        </Text>
        {item.description.length > 150 && (
          <TouchableOpacity onPress={toggleDescription}>
            <Text style={styles.expandButton}>
              {showFullDescription ? 'Show Less' : 'Read More'}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Ingredients */}
      {item.ingredients && (
        <View style={styles.section}>
          <TouchableOpacity
            style={styles.sectionHeader}
            onPress={toggleIngredients}
            activeOpacity={0.7}
          >
            <Text style={styles.sectionTitle}>Ingredients</Text>
            <Text style={[
              styles.expandIcon,
              showIngredients && styles.expandIconRotated
            ]}>
              ▼
            </Text>
          </TouchableOpacity>
          
          {showIngredients && (
            <View style={styles.ingredientsList}>
              {item.ingredients.map((ingredient, index) => (
                <View key={index} style={styles.ingredientItem}>
                  <Text style={styles.ingredientBullet}>•</Text>
                  <Text style={styles.ingredientText}>{ingredient}</Text>
                </View>
              ))}
              
              {item.allergens && item.allergens.length > 0 && (
                <View style={styles.allergensContainer}>
                  <Text style={styles.allergensTitle}>Allergens:</Text>
                  <Text style={styles.allergensText}>
                    {item.allergens.join(', ')}
                  </Text>
                </View>
              )}
            </View>
          )}
        </View>
      )}

      {/* Nutritional Information */}
      {item.nutrition && (
        <View style={styles.section}>
          <TouchableOpacity
            style={styles.sectionHeader}
            onPress={toggleNutrition}
            activeOpacity={0.7}
          >
            <Text style={styles.sectionTitle}>Nutritional Information</Text>
            <Text style={[
              styles.expandIcon,
              showNutrition && styles.expandIconRotated
            ]}>
              ▼
            </Text>
          </TouchableOpacity>
          
          {showNutrition && (
            <View style={styles.nutritionGrid}>
              <View style={styles.nutritionItem}>
                <Text style={styles.nutritionValue}>{item.nutrition.calories}</Text>
                <Text style={styles.nutritionLabel}>Calories</Text>
              </View>
              <View style={styles.nutritionItem}>
                <Text style={styles.nutritionValue}>{item.nutrition.protein}g</Text>
                <Text style={styles.nutritionLabel}>Protein</Text>
              </View>
              <View style={styles.nutritionItem}>
                <Text style={styles.nutritionValue}>{item.nutrition.carbs}g</Text>
                <Text style={styles.nutritionLabel}>Carbs</Text>
              </View>
              <View style={styles.nutritionItem}>
                <Text style={styles.nutritionValue}>{item.nutrition.fat}g</Text>
                <Text style={styles.nutritionLabel}>Fat</Text>
              </View>
            </View>
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  itemName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  restaurantName: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 16,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  currentPrice: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FF4444',
    marginRight: 8,
  },
  originalPrice: {
    fontSize: 16,
    color: '#999999',
    textDecorationLine: 'line-through',
    marginRight: 8,
  },
  discountBadge: {
    backgroundColor: '#34A853',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  discountText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  prepTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  prepTimeIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  prepTimeText: {
    fontSize: 14,
    color: '#666666',
  },
  spiceLevelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  spiceLevelLabel: {
    fontSize: 14,
    color: '#666666',
    marginRight: 8,
  },
  spiceLevel: {
    flexDirection: 'row',
  },
  pepper: {
    fontSize: 14,
    marginRight: 2,
  },
  section: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  expandIcon: {
    fontSize: 12,
    color: '#666666',
    transform: [{ rotate: '0deg' }],
  },
  expandIconRotated: {
    transform: [{ rotate: '180deg' }],
  },
  description: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 24,
    marginBottom: 8,
  },
  expandButton: {
    fontSize: 14,
    color: '#FF4444',
    fontWeight: '600',
  },
  ingredientsList: {
    marginTop: 8,
  },
  ingredientItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  ingredientBullet: {
    fontSize: 16,
    color: '#666666',
    marginRight: 8,
    marginTop: 2,
  },
  ingredientText: {
    fontSize: 14,
    color: '#666666',
    flex: 1,
    lineHeight: 20,
  },
  allergensContainer: {
    marginTop: 12,
    padding: 12,
    backgroundColor: '#FFF3CD',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#FBBC04',
  },
  allergensTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#856404',
    marginBottom: 4,
  },
  allergensText: {
    fontSize: 14,
    color: '#856404',
  },
  nutritionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  nutritionItem: {
    alignItems: 'center',
    flex: 1,
  },
  nutritionValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  nutritionLabel: {
    fontSize: 12,
    color: '#666666',
  },
});