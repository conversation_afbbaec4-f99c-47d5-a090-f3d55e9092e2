import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Animated,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import { OfferCard } from '@/components/OfferCard';
import { LoyaltyHeader } from '@/components/LoyaltyHeader';
import { ScratchCardModal } from '@/components/ScratchCardModal';
import { SpinWheelModal } from '@/components/SpinWheelModal';

interface Offer {
  id: string;
  title: string;
  description: string;
  restaurant: {
    name: string;
    cuisine: string;
    logo: string;
  };
  discount: {
    type: 'percentage' | 'fixed' | 'bogo' | 'free_delivery';
    value: number;
    display: string;
  };
  code?: string;
  minOrder?: number;
  validUntil: string;
  category: 'restaurant' | 'delivery' | 'cashback' | 'first_time' | 'loyalty';
  status: 'available' | 'used' | 'expired' | 'coming_soon';
  badges: ('new' | 'expiring' | 'popular' | 'exclusive')[];
  image: string;
  terms: string[];
}

interface LoyaltyData {
  points: number;
  tier: 'bronze' | 'silver' | 'gold';
  tierProgress: number;
  nextTierPoints: number;
  expiringPoints: number;
  expiringDate: string;
}

export default function OffersScreen() {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [offers, setOffers] = useState<Offer[]>([]);
  const [loyaltyData, setLoyaltyData] = useState<LoyaltyData | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [showScratchCard, setShowScratchCard] = useState(false);
  const [showSpinWheel, setShowSpinWheel] = useState(false);
  const [pointsAnimation] = useState(new Animated.Value(0));

  const categories = [
    { id: 'all', name: 'All Offers', icon: '🎁' },
    { id: 'restaurant', name: 'Restaurant', icon: '🍽️' },
    { id: 'delivery', name: 'Delivery', icon: '🚚' },
    { id: 'cashback', name: 'Cashback', icon: '💰' },
    { id: 'first_time', name: 'First Time', icon: '⭐' },
    { id: 'loyalty', name: 'Loyalty', icon: '👑' },
  ];

  useEffect(() => {
    loadOffers();
    loadLoyaltyData();
  }, []);

  const loadOffers = async () => {
    // Simulate API call
    const mockOffers: Offer[] = [
      {
        id: '1',
        title: '50% Off Your First Order',
        description: 'Welcome bonus for new customers',
        restaurant: {
          name: 'Pizza Palace',
          cuisine: 'Italian',
          logo: '🍕',
        },
        discount: {
          type: 'percentage',
          value: 50,
          display: '50% OFF',
        },
        code: 'WELCOME50',
        minOrder: 25,
        validUntil: '2024-02-15',
        category: 'first_time',
        status: 'available',
        badges: ['new', 'exclusive'],
        image: 'pizza_offer.jpg',
        terms: ['Valid for first-time users only', 'Minimum order $25', 'Cannot be combined with other offers'],
      },
      {
        id: '2',
        title: 'Free Delivery Weekend',
        description: 'No delivery fees on all orders',
        restaurant: {
          name: 'All Restaurants',
          cuisine: 'Various',
          logo: '🚚',
        },
        discount: {
          type: 'free_delivery',
          value: 0,
          display: 'FREE DELIVERY',
        },
        validUntil: '2024-01-28',
        category: 'delivery',
        status: 'available',
        badges: ['popular', 'expiring'],
        image: 'delivery_offer.jpg',
        terms: ['Valid on weekends only', 'All restaurants included', 'No minimum order'],
      },
      // Add more mock offers...
    ];
    setOffers(mockOffers);
  };

  const loadLoyaltyData = async () => {
    // Simulate API call
    const mockLoyalty: LoyaltyData = {
      points: 1250,
      tier: 'silver',
      tierProgress: 75,
      nextTierPoints: 250,
      expiringPoints: 150,
      expiringDate: '2024-02-01',
    };
    setLoyaltyData(mockLoyalty);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([loadOffers(), loadLoyaltyData()]);
    setRefreshing(false);
  };

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const filteredOffers = selectedCategory === 'all' 
    ? offers 
    : offers.filter(offer => offer.category === selectedCategory);

  const getQuickStats = () => {
    const activeOffers = offers.filter(o => o.status === 'available').length;
    const expiringToday = offers.filter(o => 
      o.status === 'available' && 
      new Date(o.validUntil).toDateString() === new Date().toDateString()
    ).length;
    
    return {
      activeOffers,
      usedThisMonth: 8,
      pointsEarned: 450,
      expiringToday,
    };
  };

  const stats = getQuickStats();

  const animatePoints = () => {
    Animated.sequence([
      Animated.timing(pointsAnimation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(pointsAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <View style={styles.titleContainer}>
              <Text style={styles.titleIcon}>🎁</Text>
              <Text style={styles.title}>Offers & Deals</Text>
            </View>
            <TouchableOpacity
              style={styles.notificationButton}
              onPress={() => router.push('/offers/notifications')}
            >
              <Text style={styles.notificationIcon}>🔔</Text>
              <View style={styles.notificationBadge}>
                <Text style={styles.notificationBadgeText}>3</Text>
              </View>
            </TouchableOpacity>
          </View>

          {loyaltyData && (
            <LoyaltyHeader 
              loyaltyData={loyaltyData}
              onPointsPress={animatePoints}
              pointsAnimation={pointsAnimation}
            />
          )}
        </View>

        {/* Quick Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{stats.activeOffers}</Text>
              <Text style={styles.statLabel}>Active Offers</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>$45</Text>
              <Text style={styles.statLabel}>Saved This Month</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{stats.pointsEarned}</Text>
              <Text style={styles.statLabel}>Points Earned</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={[
                styles.statValue,
                stats.expiringToday > 0 && styles.statValueWarning
              ]}>
                {stats.expiringToday}
              </Text>
              <Text style={styles.statLabel}>Expiring Today</Text>
            </View>
          </View>
        </View>

        {/* Games Section */}
        <View style={styles.gamesSection}>
          <Text style={styles.sectionTitle}>Daily Rewards</Text>
          <View style={styles.gamesContainer}>
            <TouchableOpacity
              style={styles.gameCard}
              onPress={() => setShowScratchCard(true)}
              activeOpacity={0.8}
            >
              <Text style={styles.gameIcon}>🎫</Text>
              <Text style={styles.gameTitle}>Scratch Card</Text>
              <Text style={styles.gameSubtitle}>Free daily attempt</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.gameCard}
              onPress={() => setShowSpinWheel(true)}
              activeOpacity={0.8}
            >
              <Text style={styles.gameIcon}>🎡</Text>
              <Text style={styles.gameTitle}>Spin Wheel</Text>
              <Text style={styles.gameSubtitle}>50 points to spin</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.gameCard}
              onPress={() => router.push('/offers/quiz')}
              activeOpacity={0.8}
            >
              <Text style={styles.gameIcon}>🧠</Text>
              <Text style={styles.gameTitle}>Food Quiz</Text>
              <Text style={styles.gameSubtitle}>Test your knowledge</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Category Tabs */}
        <View style={styles.categoriesSection}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContainer}
          >
            {categories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryTab,
                  selectedCategory === category.id && styles.categoryTabActive,
                ]}
                onPress={() => handleCategorySelect(category.id)}
                activeOpacity={0.8}
              >
                <Text style={styles.categoryIcon}>{category.icon}</Text>
                <Text style={[
                  styles.categoryText,
                  selectedCategory === category.id && styles.categoryTextActive,
                ]}>
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Offers List */}
        <View style={styles.offersSection}>
          <View style={styles.offersHeader}>
            <Text style={styles.sectionTitle}>
              {selectedCategory === 'all' ? 'All Offers' : categories.find(c => c.id === selectedCategory)?.name}
            </Text>
            <Text style={styles.offersCount}>
              {filteredOffers.length} offers
            </Text>
          </View>

          <View style={styles.offersList}>
            {filteredOffers.map((offer) => (
              <OfferCard
                key={offer.id}
                offer={offer}
                onPress={() => router.push(`/offers/${offer.id}`)}
                onUse={() => {/* Handle offer usage */}}
              />
            ))}
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => router.push('/offers/referral')}
            activeOpacity={0.8}
          >
            <Text style={styles.actionIcon}>👥</Text>
            <Text style={styles.actionText}>Refer Friends</Text>
            <Text style={styles.actionSubtext}>Earn $5 per referral</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => router.push('/offers/loyalty')}
            activeOpacity={0.8}
          >
            <Text style={styles.actionIcon}>🏆</Text>
            <Text style={styles.actionText}>Loyalty Program</Text>
            <Text style={styles.actionSubtext}>View rewards catalog</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Modals */}
      <ScratchCardModal
        visible={showScratchCard}
        onClose={() => setShowScratchCard(false)}
        onReward={(reward) => {
          // Handle scratch card reward
          console.log('Scratch card reward:', reward);
        }}
      />

      <SpinWheelModal
        visible={showSpinWheel}
        onClose={() => setShowSpinWheel(false)}
        userPoints={loyaltyData?.points || 0}
        onReward={(reward) => {
          // Handle spin wheel reward
          console.log('Spin wheel reward:', reward);
        }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 20,
    backgroundColor: '#FFFFFF',
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  notificationButton: {
    position: 'relative',
    padding: 8,
  },
  notificationIcon: {
    fontSize: 24,
  },
  notificationBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: '#FF4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  statsContainer: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    flex: 1,
    minWidth: '22%',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  statValueWarning: {
    color: '#FF4444',
  },
  statLabel: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
  gamesSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  gamesContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  gameCard: {
    flex: 1,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  gameIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  gameTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  gameSubtitle: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
  categoriesSection: {
    marginBottom: 24,
  },
  categoriesContainer: {
    paddingHorizontal: 16,
    gap: 12,
  },
  categoryTab: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 25,
  },
  categoryTabActive: {
    backgroundColor: '#FF4444',
  },
  categoryIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  categoryTextActive: {
    color: '#FFFFFF',
  },
  offersSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  offersHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  offersCount: {
    fontSize: 14,
    color: '#666666',
  },
  offersList: {
    gap: 16,
  },
  quickActions: {
    paddingHorizontal: 16,
    paddingBottom: 24,
    gap: 12,
  },
  actionButton: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  actionText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  actionSubtext: {
    fontSize: 12,
    color: '#666666',
  },
});