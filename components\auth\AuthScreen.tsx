import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

import PhoneInput from './PhoneInput';
import PasswordInput from './PasswordInput';
import SocialLoginButtons from './SocialLoginButtons';
import LoadingButton from '../ui/LoadingButton';
import { useAuth } from '@/hooks/useAuth';

type AuthMode = 'login' | 'register';

interface AuthScreenProps {
  initialMode?: AuthMode;
}

export default function AuthScreen({ initialMode = 'login' }: AuthScreenProps) {
  const [mode, setMode] = useState<AuthMode>(initialMode);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const { login, register } = useAuth();

  // Animation values
  const slideAnim = useRef(new Animated.Value(300)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const logoScale = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    // Initial animations
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(logoScale, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Phone validation
    if (!phoneNumber.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (phoneNumber.length < 10) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    // Password validation
    if (!password.trim()) {
      newErrors.password = 'Password is required';
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    // Register-specific validations
    if (mode === 'register') {
      if (!fullName.trim()) {
        newErrors.fullName = 'Full name is required';
      }

      if (password !== confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }

      if (!agreeToTerms) {
        newErrors.terms = 'Please agree to terms and conditions';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      // Shake animation for errors
      shakeAnimation();
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    setIsLoading(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    try {
      if (mode === 'login') {
        await login(phoneNumber, password);
      } else {
        await register(phoneNumber, password, fullName);
      }
      
      // Success - navigate to main app
      router.replace('/(tabs)');
    } catch (error) {
      Alert.alert(
        'Authentication Error',
        error instanceof Error ? error.message : 'Something went wrong'
      );
      shakeAnimation();
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setIsLoading(false);
    }
  };

  const shakeAnimation = () => {
    const shakeValue = new Animated.Value(0);
    Animated.sequence([
      Animated.timing(shakeValue, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeValue, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeValue, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeValue, { toValue: 0, duration: 50, useNativeDriver: true }),
    ]).start();
  };

  const switchMode = () => {
    setMode(mode === 'login' ? 'register' : 'login');
    setErrors({});
    Haptics.selectionAsync();
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Background Pattern */}
      <View style={styles.backgroundPattern}>
        <Text style={styles.patternEmoji}>🍕</Text>
        <Text style={styles.patternEmoji}>🍔</Text>
        <Text style={styles.patternEmoji}>🍜</Text>
        <Text style={styles.patternEmoji}>🌮</Text>
        <Text style={styles.patternEmoji}>🍱</Text>
      </View>

      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Header Area */}
        <Animated.View 
          style={[
            styles.headerArea,
            {
              opacity: fadeAnim,
              transform: [{ scale: logoScale }],
            },
          ]}
        >
          <View style={styles.logoContainer}>
            <Text style={styles.logoEmoji}>🍕</Text>
            <Text style={styles.logoText}>FoodWay</Text>
          </View>
          <Text style={styles.welcomeText}>
            {mode === 'login' ? 'Welcome Back!' : 'Join FoodWay'}
          </Text>
          <Text style={styles.subtitleText}>
            {mode === 'login' 
              ? 'Sign in to continue ordering' 
              : 'Create account to get started'
            }
          </Text>
        </Animated.View>

        {/* Form Area */}
        <Animated.View 
          style={[
            styles.formArea,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {/* Full Name Input (Register only) */}
          {mode === 'register' && (
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Full Name</Text>
              <NameInput
                value={fullName}
                onChangeText={setFullName}
                error={errors.fullName}
              />
            </View>
          )}

          {/* Phone Number Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Phone Number</Text>
            <PhoneInput
              value={phoneNumber}
              onChangeText={setPhoneNumber}
              error={errors.phone}
            />
          </View>

          {/* Password Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Password</Text>
            <PasswordInput
              value={password}
              onChangeText={setPassword}
              error={errors.password}
              showStrengthIndicator={mode === 'register'}
            />
          </View>

          {/* Confirm Password (Register only) */}
          {mode === 'register' && (
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Confirm Password</Text>
              <PasswordInput
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                error={errors.confirmPassword}
                placeholder="Confirm your password"
              />
            </View>
          )}

          {/* Forgot Password (Login only) */}
          {mode === 'login' && (
            <TouchableOpacity style={styles.forgotPassword}>
              <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
            </TouchableOpacity>
          )}

          {/* Terms Agreement (Register only) */}
          {mode === 'register' && (
            <TermsAgreement
              agreed={agreeToTerms}
              onToggle={setAgreeToTerms}
              error={errors.terms}
            />
          )}

          {/* Submit Button */}
          <LoadingButton
            title={mode === 'login' ? 'Sign In' : 'Create Account'}
            onPress={handleSubmit}
            loading={isLoading}
            style={styles.submitButton}
          />

          {/* Mode Switch */}
          <TouchableOpacity style={styles.switchMode} onPress={switchMode}>
            <Text style={styles.switchModeText}>
              {mode === 'login' 
                ? "Don't have an account? " 
                : "Already have an account? "
              }
              <Text style={styles.switchModeLink}>
                {mode === 'login' ? 'Sign Up' : 'Sign In'}
              </Text>
            </Text>
          </TouchableOpacity>
        </Animated.View>

        {/* Footer Area - Social Login */}
        <Animated.View 
          style={[
            styles.footerArea,
            {
              opacity: fadeAnim,
            },
          ]}
        >
          <View style={styles.divider}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>or continue with</Text>
            <View style={styles.dividerLine} />
          </View>

          <SocialLoginButtons onSocialLogin={handleSocialLogin} />
        </Animated.View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

// Handle social login
const handleSocialLogin = async (provider: 'google' | 'facebook' | 'apple') => {
  try {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    // Implement social login logic
    console.log(`Login with ${provider}`);
  } catch (error) {
    Alert.alert('Login Error', 'Social login failed. Please try again.');
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  backgroundPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.05,
    zIndex: 0,
  },
  patternEmoji: {
    position: 'absolute',
    fontSize: 40,
    color: '#FF4444',
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  headerArea: {
    flex: 0.3,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  logoEmoji: {
    fontSize: 32,
    marginRight: 8,
  },
  logoText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FF4444',
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  subtitleText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  formArea: {
    flex: 0.5,
    paddingVertical: 20,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 24,
  },
  forgotPasswordText: {
    fontSize: 14,
    color: '#666666',
  },
  submitButton: {
    marginTop: 8,
    marginBottom: 24,
  },
  switchMode: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  switchModeText: {
    fontSize: 14,
    color: '#666666',
  },
  switchModeLink: {
    color: '#FF4444',
    fontWeight: '600',
  },
  footerArea: {
    flex: 0.2,
    paddingTop: 20,
    paddingBottom: 40,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#E0E0E0',
  },
  dividerText: {
    fontSize: 12,
    color: '#666666',
    paddingHorizontal: 16,
  },
});