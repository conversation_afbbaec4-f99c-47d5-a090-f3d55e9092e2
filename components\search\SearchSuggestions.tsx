import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface SearchSuggestionsProps {
  onSuggestionPress: (query: string) => void;
}

interface Suggestion {
  id: string;
  text: string;
  type: 'recent' | 'popular';
  icon: string;
}

const RECENT_SEARCHES: Suggestion[] = [
  { id: '1', text: 'Pizza Hut', type: 'recent', icon: '🕐' },
  { id: '2', text: 'Chinese food', type: 'recent', icon: '🕐' },
  { id: '3', text: 'Burger King', type: 'recent', icon: '🕐' },
  { id: '4', text: 'Sushi', type: 'recent', icon: '🕐' },
];

const POPULAR_SEARCHES: Suggestion[] = [
  { id: '5', text: 'Pizza', type: 'popular', icon: '📈' },
  { id: '6', text: 'Burgers', type: 'popular', icon: '📈' },
  { id: '7', text: 'Indian food', type: 'popular', icon: '📈' },
  { id: '8', text: 'Thai cuisine', type: 'popular', icon: '📈' },
  { id: '9', text: 'Desserts', type: 'popular', icon: '📈' },
  { id: '10', text: 'Coffee', type: 'popular', icon: '📈' },
];

export default function SearchSuggestions({ onSuggestionPress }: SearchSuggestionsProps) {
  const handleSuggestionPress = (suggestion: Suggestion) => {
    Haptics.selectionAsync();
    onSuggestionPress(suggestion.text);
  };

  const renderSuggestion = ({ item }: { item: Suggestion }) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleSuggestionPress(item)}
      activeOpacity={0.7}
    >
      <Text style={styles.suggestionIcon}>{item.icon}</Text>
      <Text style={styles.suggestionText}>{item.text}</Text>
      <Text style={styles.arrow}>↗</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Recent Searches */}
      {RECENT_SEARCHES.length > 0 && (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Searches</Text>
            <TouchableOpacity style={styles.clearButton}>
              <Text style={styles.clearText}>Clear</Text>
            </TouchableOpacity>
          </View>
          <FlatList
            data={RECENT_SEARCHES}
            renderItem={renderSuggestion}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
          />
        </View>
      )}

      {/* Popular Searches */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Popular Searches</Text>
        <FlatList
          data={POPULAR_SEARCHES}
          renderItem={renderSuggestion}
          keyExtractor={(item) => item.id}
          scrollEnabled={false}
        />
      </View>

      {/* Quick Suggestions */}
      <View style={styles.quickSuggestions}>
        <Text style={styles.quickTitle}>Try searching for:</Text>
        <View style={styles.quickChips}>
          {['Pizza', 'Burgers', 'Chinese', 'Italian', 'Desserts'].map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.quickChip}
              onPress={() => onSuggestionPress(item)}
              activeOpacity={0.7}
            >
              <Text style={styles.quickChipText}>{item}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  clearButton: {
    padding: 4,
  },
  clearText: {
    fontSize: 14,
    color: '#FF4444',
    fontWeight: '500',
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  suggestionIcon: {
    fontSize: 16,
    marginRight: 12,
    width: 20,
  },
  suggestionText: {
    flex: 1,
    fontSize: 16,
    color: '#2C2C2C',
  },
  arrow: {
    fontSize: 14,
    color: '#CCCCCC',
  },
  quickSuggestions: {
    paddingHorizontal: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  quickTitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 12,
  },
  quickChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  quickChip: {
    backgroundColor: '#F5F5F5',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  quickChipText: {
    fontSize: 14,
    color: '#666666',
  },
});