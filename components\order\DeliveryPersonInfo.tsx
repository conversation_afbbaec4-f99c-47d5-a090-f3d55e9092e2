import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Linking,
  Alert,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { DeliveryPerson } from '@/app/order-success';

interface DeliveryPersonInfoProps {
  deliveryPerson: DeliveryPerson;
  estimatedArrival: string;
}

export default function DeliveryPersonInfo({
  deliveryPerson,
  estimatedArrival,
}: DeliveryPersonInfoProps) {
  const handleContact = (type: 'call' | 'message') => {
    if (type === 'call') {
      Linking.openURL(`tel:${deliveryPerson.phone}`);
    } else {
      Alert.alert('Message', 'Opening message to delivery person...');
    }
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const getVehicleIcon = (type: string) => {
    switch (type) {
      case 'bike':
        return '🚴‍♂️';
      case 'car':
        return '🚗';
      case 'scooter':
        return '🛵';
      default:
        return '🚚';
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push('⭐');
    }
    if (hasHalfStar) {
      stars.push('⭐');
    }
    
    return stars.join('');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Your Delivery Person</Text>
      
      <View style={styles.driverCard}>
        <View style={styles.driverInfo}>
          <View style={styles.driverPhoto}>
            <Text style={styles.driverPhotoText}>{deliveryPerson.photo}</Text>
          </View>
          
          <View style={styles.driverDetails}>
            <Text style={styles.driverName}>{deliveryPerson.name}</Text>
            
            <View style={styles.ratingContainer}>
              <Text style={styles.ratingStars}>
                {renderStars(deliveryPerson.rating)}
              </Text>
              <Text style={styles.ratingText}>
                {deliveryPerson.rating.toFixed(1)}
              </Text>
            </View>
            
            <View style={styles.vehicleInfo}>
              <Text style={styles.vehicleIcon}>
                {getVehicleIcon(deliveryPerson.vehicle.type)}
              </Text>
              <Text style={styles.vehicleText}>
                {deliveryPerson.vehicle.licensePlate}
              </Text>
            </View>
          </View>
        </View>
        
        <View style={styles.arrivalInfo}>
          <Text style={styles.arrivalLabel}>ETA</Text>
          <Text style={styles.arrivalTime}>{estimatedArrival}</Text>
        </View>
      </View>
      
      <View style={styles.contactButtons}>
        <TouchableOpacity
          style={styles.contactButton}
          onPress={() => handleContact('call')}
          activeOpacity={0.8}
        >
          <Text style={styles.contactButtonIcon}>📞</Text>
          <Text style={styles.contactButtonText}>Call</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.contactButton}
          onPress={() => handleContact('message')}
          activeOpacity={0.8}
        >
          <Text style={styles.contactButtonIcon}>💬</Text>
          <Text style={styles.contactButtonText}>Message</Text>
        </TouchableOpacity>
      </View>
      
      {/* Live Updates */}
      <View style={styles.liveUpdates}>
        <View style={styles.updateIndicator}>
          <View style={styles.pulseDot} />
          <Text style={styles.liveText}>Live tracking</Text>
        </View>
        <Text style={styles.lastUpdate}>
          Last updated: {new Date().toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
          })}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  driverCard: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  driverPhoto: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  driverPhotoText: {
    fontSize: 24,
  },
  driverDetails: {
    flex: 1,
  },
  driverName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  ratingStars: {
    fontSize: 12,
    marginRight: 4,
  },
  ratingText: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
  },
  vehicleInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  vehicleIcon: {
    fontSize: 14,
    marginRight: 4,
  },
  vehicleText: {
    fontSize: 12,
    color: '#666666',
  },
  arrivalInfo: {
    alignItems: 'center',
  },
  arrivalLabel: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 2,
  },
  arrivalTime: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF4444',
  },
  contactButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  contactButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF4444',
    paddingVertical: 12,
    borderRadius: 8,
  },
  contactButtonIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  contactButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  liveUpdates: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  updateIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pulseDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#34A853',
    marginRight: 6,
  },
  liveText: {
    fontSize: 12,
    color: '#34A853',
    fontWeight: '500',
  },
  lastUpdate: {
    fontSize: 12,
    color: '#666666',
  },
});