import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  PanGestureHandler,
  Animated,
  Dimensions,
} from 'react-native';
import { PanGestureHandlerGestureEvent } from 'react-native-gesture-handler';
import * as Haptics from 'expo-haptics';
import Svg, { Path } from 'react-native-svg';

interface ScratchCardModalProps {
  visible: boolean;
  onClose: () => void;
  onReward: (reward: { type: string; value: number; display: string }) => void;
}

const { width: screenWidth } = Dimensions.get('window');
const CARD_WIDTH = screenWidth - 64;
const CARD_HEIGHT = 200;

export function ScratchCardModal({
  visible,
  onClose,
  onReward,
}: ScratchCardModalProps) {
  const [isScratching, setIsScratching] = useState(false);
  const [scratchedArea, setScratchedArea] = useState(0);
  const [isRevealed, setIsRevealed] = useState(false);
  const [reward, setReward] = useState<any>(null);
  const pathRef = useRef('');
  const animatedValue = useRef(new Animated.Value(0)).current;

  const rewards = [
    { type: 'points', value: 50, display: '50 Points', icon: '⭐' },
    { type: 'discount', value: 10, display: '10% Off', icon: '🎫' },
    { type: 'free_delivery', value: 0, display: 'Free Delivery', icon: '🚚' },
    { type: 'cashback', value: 5, display: '$5 Cashback', icon: '💰' },
    { type: 'points', value: 100, display: '100 Points', icon: '⭐' },
  ];

  const generateReward = () => {
    const randomReward = rewards[Math.floor(Math.random() * rewards.length)];
    setReward(randomReward);
    return randomReward;
  };

  const handleGesture = (event: PanGestureHandlerGestureEvent) => {
    const { x, y } = event.nativeEvent;
    
    if (!isScratching) {
      setIsScratching(true);
      if (!reward) {
        generateReward();
      }
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    // Add to scratch path
    if (pathRef.current === '') {
      pathRef.current = `M${x},${y}`;
    } else {
      pathRef.current += ` L${x},${y}`;
    }

    // Calculate scratched area (simplified)
    const newArea = Math.min(scratchedArea + 2, 100);
    setScratchedArea(newArea);

    if (newArea >= 60 && !isRevealed) {
      setIsRevealed(true);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      
      // Animate reveal
      Animated.spring(animatedValue, {
        toValue: 1,
        useNativeDriver: true,
        tension: 50,
        friction: 8,
      }).start();
    }
  };

  const handleClose = () => {
    if (isRevealed && reward) {
      onReward(reward);
    }
    
    // Reset state
    setIsScratching(false);
    setScratchedArea(0);
    setIsRevealed(false);
    setReward(null);
    pathRef.current = '';
    animatedValue.setValue(0);
    
    onClose();
  };

  const resetCard = () => {
    setIsScratching(false);
    setScratchedArea(0);
    setIsRevealed(false);
    setReward(null);
    pathRef.current = '';
    animatedValue.setValue(0);
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>🎫 Daily Scratch Card</Text>
            <Text style={styles.subtitle}>
              Scratch to reveal your reward!
            </Text>
          </View>

          {/* Scratch Card */}
          <View style={styles.cardContainer}>
            <View style={styles.card}>
              {/* Background/Reward Layer */}
              <View style={styles.rewardLayer}>
                {reward && (
                  <Animated.View 
                    style={[
                      styles.rewardContent,
                      {
                        opacity: animatedValue,
                        transform: [
                          {
                            scale: animatedValue.interpolate({
                              inputRange: [0, 1],
                              outputRange: [0.8, 1],
                            }),
                          },
                        ],
                      },
                    ]}
                  >
                    <Text style={styles.rewardIcon}>{reward.icon}</Text>
                    <Text style={styles.rewardText}>{reward.display}</Text>
                    <Text style={styles.congratsText}>Congratulations!</Text>
                  </Animated.View>
                )}
                
                {!reward && (
                  <View style={styles.placeholderContent}>
                    <Text style={styles.placeholderIcon}>🎁</Text>
                    <Text style={styles.placeholderText}>Your Reward</Text>
                  </View>
                )}
              </View>

              {/* Scratch Layer */}
              <PanGestureHandler onGestureEvent={handleGesture}>
                <Animated.View style={[
                  styles.scratchLayer,
                  { opacity: Math.max(0, 1 - scratchedArea / 100) }
                ]}>
                  <View style={styles.scratchSurface}>
                    <Text style={styles.scratchText}>SCRATCH HERE</Text>
                    <Text style={styles.scratchSubtext}>👆 Use your finger</Text>
                  </View>
                  
                  {/* Scratch Path Visualization */}
                  {pathRef.current && (
                    <Svg 
                      style={StyleSheet.absoluteFill}
                      width={CARD_WIDTH}
                      height={CARD_HEIGHT}
                    >
                      <Path
                        d={pathRef.current}
                        stroke="transparent"
                        strokeWidth={20}
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        fill="none"
                      />
                    </Svg>
                  )}
                </Animated.View>
              </PanGestureHandler>
            </View>

            {/* Progress Indicator */}
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill,
                    { width: `${scratchedArea}%` }
                  ]} 
                />
              </View>
              <Text style={styles.progressText}>
                {Math.round(scratchedArea)}% scratched
              </Text>
            </View>
          </View>

          {/* Instructions */}
          {!isScratching && (
            <View style={styles.instructions}>
              <Text style={styles.instructionText}>
                💡 Scratch off the silver area to reveal your daily reward
              </Text>
            </View>
          )}

          {/* Revealed Message */}
          {isRevealed && reward && (
            <View style={styles.revealedMessage}>
              <Text style={styles.revealedText}>
                🎉 You won {reward.display}!
              </Text>
              <Text style={styles.revealedSubtext}>
                Your reward has been added to your account
              </Text>
            </View>
          )}

          {/* Action Buttons */}
          <View style={styles.actions}>
            {!isRevealed && scratchedArea > 0 && (
              <TouchableOpacity
                style={styles.resetButton}
                onPress={resetCard}
                activeOpacity={0.8}
              >
                <Text style={styles.resetButtonText}>Reset Card</Text>
              </TouchableOpacity>
            )}
            
            <TouchableOpacity
              style={[
                styles.actionButton,
                isRevealed && styles.actionButtonSuccess,
              ]}
              onPress={handleClose}
              activeOpacity={0.8}
            >
              <Text style={[
                styles.actionButtonText,
                isRevealed && styles.actionButtonTextSuccess,
              ]}>
                {isRevealed ? 'Collect Reward' : 'Close'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Daily Limit Info */}
          <View style={styles.limitInfo}>
            <Text style={styles.limitText}>
              ⏰ Next free scratch card in 18h 32m
            </Text>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 400,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  cardContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  card: {
    width: CARD_WIDTH,
    height: CARD_HEIGHT,
    borderRadius: 16,
    overflow: 'hidden',
    position: 'relative',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  rewardLayer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#FFD700',
    justifyContent: 'center',
    alignItems: 'center',
  },
  rewardContent: {
    alignItems: 'center',
  },
  rewardIcon: {
    fontSize: 48,
    marginBottom: 12,
  },
  rewardText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  congratsText: {
    fontSize: 16,
    color: '#666666',
  },
  placeholderContent: {
    alignItems: 'center',
  },
  placeholderIcon: {
    fontSize: 48,
    marginBottom: 12,
  },
  placeholderText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  scratchLayer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#C0C0C0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scratchSurface: {
    alignItems: 'center',
  },
  scratchText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#666666',
    marginBottom: 8,
  },
  scratchSubtext: {
    fontSize: 14,
    color: '#999999',
  },
  progressContainer: {
    width: CARD_WIDTH,
    marginTop: 16,
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    color: '#666666',
  },
  instructions: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  instructionText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
  },
  revealedMessage: {
    backgroundColor: '#E8F5E8',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    alignItems: 'center',
  },
  revealedText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#34A853',
    marginBottom: 4,
  },
  revealedSubtext: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
  },
  resetButton: {
    flex: 1,
    backgroundColor: '#F8F8F8',
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  resetButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666666',
  },
  actionButton: {
    flex: 1,
    backgroundColor: '#FF4444',
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  actionButtonSuccess: {
    backgroundColor: '#34A853',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  actionButtonTextSuccess: {
    color: '#FFFFFF',
  },
  limitInfo: {
    marginTop: 16,
    alignItems: 'center',
  },
  limitText: {
    fontSize: 12,
    color: '#999999',
  },
});