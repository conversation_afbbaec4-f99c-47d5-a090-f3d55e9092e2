import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as Haptics from 'expo-haptics';
import { UserProfile } from '@/app/profile';

interface ProfileHeaderProps {
  userProfile: UserProfile;
  onEditProfile: () => void;
}

export default function ProfileHeader({ 
  userProfile, 
  onEditProfile 
}: ProfileHeaderProps) {
  const handleProfilePhotoPress = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant camera roll permissions to change your profile photo.');
      return;
    }

    Alert.alert(
      'Change Profile Photo',
      'Choose an option',
      [
        { text: 'Camera', onPress: openCamera },
        { text: 'Photo Library', onPress: openImagePicker },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const openCamera = async () => {
    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      // Handle image upload
      console.log('New profile photo:', result.assets[0].uri);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const openImagePicker = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      // Handle image upload
      console.log('New profile photo:', result.assets[0].uri);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const formatMemberSince = (date: Date) => {
    return `Member since ${date.toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric'
    })}`;
  };

  return (
    <View style={styles.container}>
      {/* Profile Photo Section */}
      <View style={styles.photoSection}>
        <TouchableOpacity
          style={styles.photoContainer}
          onPress={handleProfilePhotoPress}
          activeOpacity={0.8}
        >
          <View style={styles.photoCircle}>
            <Text style={styles.photoText}>{userProfile.profilePhoto}</Text>
          </View>
          <View style={styles.editOverlay}>
            <Text style={styles.editIcon}>✏️</Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* User Info */}
      <View style={styles.userInfo}>
        <View style={styles.nameRow}>
          <Text style={styles.userName}>{userProfile.name}</Text>
          <TouchableOpacity
            style={styles.editButton}
            onPress={onEditProfile}
            activeOpacity={0.7}
          >
            <Text style={styles.editButtonIcon}>✏️</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.contactInfo}>
          <Text style={styles.phoneNumber}>
            {userProfile.phone}
            {userProfile.isPhoneVerified && (
              <Text style={styles.verifiedBadge}> ✓</Text>
            )}
          </Text>
          <Text style={styles.memberSince}>
            {formatMemberSince(userProfile.memberSince)}
          </Text>
        </View>
      </View>

      {/* Stats Row */}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statIcon}>🍽️</Text>
          <Text style={styles.statNumber}>{userProfile.stats.ordersCompleted}</Text>
          <Text style={styles.statLabel}>Orders</Text>
        </View>

        <View style={styles.statItem}>
          <Text style={styles.statIcon}>❤️</Text>
          <Text style={styles.statNumber}>{userProfile.stats.favoriteRestaurants}</Text>
          <Text style={styles.statLabel}>Favorites</Text>
        </View>

        <View style={styles.statItem}>
          <Text style={styles.statIcon}>⭐</Text>
          <Text style={styles.statNumber}>{userProfile.stats.reviewsWritten}</Text>
          <Text style={styles.statLabel}>Reviews</Text>
        </View>

        <View style={styles.statItem}>
          <Text style={styles.statIcon}>🎯</Text>
          <Text style={styles.statNumber}>{userProfile.stats.pointsEarned}</Text>
          <Text style={styles.statLabel}>Points</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  photoSection: {
    alignItems: 'center',
    marginBottom: 16,
  },
  photoContainer: {
    position: 'relative',
  },
  photoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F8F8F8',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  photoText: {
    fontSize: 32,
  },
  editOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
  },
  editIcon: {
    fontSize: 12,
  },
  userInfo: {
    alignItems: 'center',
    marginBottom: 20,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginRight: 8,
  },
  editButton: {
    padding: 4,
  },
  editButtonIcon: {
    fontSize: 16,
    color: '#666666',
  },
  contactInfo: {
    alignItems: 'center',
  },
  phoneNumber: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 4,
  },
  verifiedBadge: {
    color: '#34A853',
    fontWeight: 'bold',
  },
  memberSince: {
    fontSize: 14,
    color: '#666666',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  statItem: {
    alignItems: 'center',
  },
  statIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  statNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
    color: '#666666',
  },
});