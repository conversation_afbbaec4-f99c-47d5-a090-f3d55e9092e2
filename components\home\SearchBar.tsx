import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Animated,
  FlatList,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface SearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
}

interface RecentSearch {
  id: string;
  query: string;
  timestamp: number;
}

export default function SearchBar({ value, onChangeText, placeholder }: SearchBarProps) {
  const [isFocused, setIsFocused] = useState(false);
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>([]);
  const [showRecent, setShowRecent] = useState(false);

  const inputRef = useRef<TextInput>(null);
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const dropdownAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadRecentSearches();
  }, []);

  useEffect(() => {
    if (isFocused && recentSearches.length > 0) {
      setShowRecent(true);
      Animated.timing(dropdownAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(dropdownAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start(() => {
        setShowRecent(false);
      });
    }
  }, [isFocused, recentSearches]);

  const loadRecentSearches = () => {
    // Mock recent searches - in real app, load from AsyncStorage
    const mockRecent: RecentSearch[] = [
      { id: '1', query: 'Pizza', timestamp: Date.now() - 3600000 },
      { id: '2', query: 'Burger King', timestamp: Date.now() - 7200000 },
      { id: '3', query: 'Sushi', timestamp: Date.now() - 10800000 },
    ];
    setRecentSearches(mockRecent);
  };

  const handleFocus = () => {
    setIsFocused(true);
    Animated.spring(scaleAnim, {
      toValue: 1.02,
      useNativeDriver: true,
    }).start();
  };

  const handleBlur = () => {
    setIsFocused(false);
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handleVoiceSearch = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    // Implement voice search functionality
    console.log('Voice search pressed');
  };

  const handleRecentSearchPress = (query: string) => {
    onChangeText(query);
    inputRef.current?.blur();
    Haptics.selectionAsync();
  };

  const clearRecentSearch = (id: string) => {
    setRecentSearches(prev => prev.filter(item => item.id !== id));
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const renderRecentSearchItem = ({ item }: { item: RecentSearch }) => (
    <TouchableOpacity
      style={styles.recentItem}
      onPress={() => handleRecentSearchPress(item.query)}
      activeOpacity={0.7}
    >
      <View style={styles.recentIcon}>
        <Text style={styles.recentIconText}>🕒</Text>
      </View>
      <Text style={styles.recentText}>{item.query}</Text>
      <TouchableOpacity
        style={styles.clearButton}
        onPress={() => clearRecentSearch(item.id)}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Text style={styles.clearButtonText}>✕</Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.searchContainer,
          isFocused && styles.searchContainerFocused,
          { transform: [{ scale: scaleAnim }] },
        ]}
      >
        {/* Search Icon */}
        <View style={styles.searchIcon}>
          <Text style={styles.searchIconText}>🔍</Text>
        </View>

        {/* Text Input */}
        <TextInput
          ref={inputRef}
          style={styles.textInput}
          value={value}
          onChangeText={onChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          placeholderTextColor="#999999"
          returnKeyType="search"
          autoCorrect={false}
          autoCapitalize="none"
        />

        {/* Voice Search Icon */}
        <TouchableOpacity
          style={styles.voiceButton}
          onPress={handleVoiceSearch}
          activeOpacity={0.7}
        >
          <Text style={styles.voiceIcon}>🎤</Text>
        </TouchableOpacity>
      </Animated.View>

      {/* Recent Searches Dropdown */}
      {showRecent && (
        <Animated.View
          style={[
            styles.dropdown,
            {
              opacity: dropdownAnim,
              transform: [
                {
                  translateY: dropdownAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-10, 0],
                  }),
                },
              ],
            },
          ]}
        >
          <View style={styles.dropdownHeader}>
            <Text style={styles.dropdownTitle}>Recent Searches</Text>
            <TouchableOpacity
              onPress={() => setRecentSearches([])}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Text style={styles.clearAllText}>Clear All</Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={recentSearches}
            renderItem={renderRecentSearchItem}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            style={styles.recentList}
          />
        </Animated.View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1000,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 44,
  },
  searchContainerFocused: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#FF4444',
    shadowColor: '#FF4444',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchIconText: {
    fontSize: 16,
    color: '#666666',
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#2C2C2C',
    paddingVertical: 0,
  },
  voiceButton: {
    marginLeft: 8,
    padding: 4,
  },
  voiceIcon: {
    fontSize: 16,
    color: '#FF4444',
  },
  dropdown: {
    position: 'absolute',
    top: 52,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    maxHeight: 200,
  },
  dropdownHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  dropdownTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  clearAllText: {
    fontSize: 12,
    color: '#FF4444',
    fontWeight: '500',
  },
  recentList: {
    maxHeight: 120,
  },
  recentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  recentIcon: {
    marginRight: 12,
  },
  recentIconText: {
    fontSize: 14,
    opacity: 0.6,
  },
  recentText: {
    flex: 1,
    fontSize: 14,
    color: '#2C2C2C',
  },
  clearButton: {
    padding: 4,
  },
  clearButtonText: {
    fontSize: 12,
    color: '#999999',
  },
});