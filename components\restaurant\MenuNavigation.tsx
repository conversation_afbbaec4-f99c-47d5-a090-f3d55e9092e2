import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface MenuNavigationProps {
  categories: string[];
  activeCategory: string;
  onCategoryPress: (category: string) => void;
  scrollY: Animated.Value;
}

export default function MenuNavigation({
  categories,
  activeCategory,
  onCategoryPress,
  scrollY,
}: MenuNavigationProps) {
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    // Auto-scroll to active category
    const activeIndex = categories.indexOf(activeCategory);
    if (activeIndex !== -1 && scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: activeIndex * 100 - 50,
        animated: true,
      });
    }
  }, [activeCategory, categories]);

  const handleCategoryPress = (category: string) => {
    Haptics.selectionAsync();
    onCategoryPress(category);
  };

  // Sticky header effect
  const headerTranslateY = scrollY.interpolate({
    inputRange: [0, 200],
    outputRange: [0, 0],
    extrapolate: 'clamp',
  });

  const headerOpacity = scrollY.interpolate({
    inputRange: [150, 200],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateY: headerTranslateY }],
        },
      ]}
    >
      {/* Background with opacity animation */}
      <Animated.View
        style={[
          styles.background,
          { opacity: headerOpacity },
        ]}
      />
      
      <ScrollView
        ref={scrollViewRef}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {categories.map((category) => {
          const isActive = category === activeCategory;
          
          return (
            <TouchableOpacity
              key={category}
              style={[
                styles.categoryTab,
                isActive && styles.categoryTabActive,
              ]}
              onPress={() => handleCategoryPress(category)}
              activeOpacity={0.7}
            >
              <Text
                style={[
                  styles.categoryText,
                  isActive && styles.categoryTextActive,
                ]}
              >
                {category}
              </Text>
              
              {isActive && <View style={styles.activeIndicator} />}
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 200, // Below hero section
    left: 0,
    right: 0,
    zIndex: 5,
    height: 50,
  },
  background: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  scrollContent: {
    paddingHorizontal: 16,
    alignItems: 'center',
    height: 50,
  },
  categoryTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  categoryTabActive: {
    backgroundColor: '#FF4444',
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  categoryTextActive: {
    color: '#FFFFFF',
  },
  activeIndicator: {
    position: 'absolute',
    bottom: -2,
    left: '50%',
    marginLeft: -2,
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#FF4444',
  },
});