import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { useThemeColor } from '@/hooks/useThemeColor';

export default function HelpCenterScreen() {
  const backgroundColor = useThemeColor({}, 'background');

  const helpTopics = [
    { id: '1', title: 'Getting Started', description: 'Learn the basics' },
    { id: '2', title: 'Placing Orders', description: 'How to order food' },
    { id: '3', title: 'Payment Issues', description: 'Billing and payments' },
    { id: '4', title: 'Delivery Problems', description: 'Tracking and delivery' },
  ];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Help Center</Text>
        <Text style={styles.subtitle}>Find answers to common questions</Text>
        
        {helpTopics.map((topic) => (
          <TouchableOpacity
            key={topic.id}
            style={styles.helpItem}
            onPress={() => router.push('/support/faq')}
          >
            <Text style={styles.helpTitle}>{topic.title}</Text>
            <Text style={styles.helpDescription}>{topic.description}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { padding: 20 },
  title: { fontSize: 28, fontWeight: 'bold', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#666', marginBottom: 20 },
  helpItem: { 
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  helpTitle: { fontSize: 18, fontWeight: 'bold', marginBottom: 4 },
  helpDescription: { fontSize: 14, color: '#666' },
});