import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

import NotificationCard from '@/components/notifications/NotificationCard';
import NotificationFilters from '@/components/notifications/NotificationFilters';
import EmptyNotifications from '@/components/notifications/EmptyNotifications';
import { useThemeColor } from '@/hooks/useThemeColor';

export interface Notification {
  id: string;
  type: 'order' | 'offer' | 'update';
  category: 'confirmed' | 'preparing' | 'delivery' | 'delivered' | 'cancelled' | 
           'new_offer' | 'promotion' | 'cashback' | 'referral' | 
           'feature' | 'maintenance' | 'policy';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  isImportant?: boolean;
  isExpired?: boolean;
  actionButton?: {
    text: string;
    action: () => void;
  };
  orderId?: string;
  offerId?: string;
}

export type NotificationFilter = 'all' | 'orders' | 'offers' | 'updates';

export default function NotificationsScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'order',
      category: 'delivered',
      title: 'Order Delivered!',
      message: 'Your order from Burger Palace has been delivered. Enjoy your meal!',
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      isRead: false,
      actionButton: {
        text: 'Rate Order',
        action: () => router.push('/orders/rate/1'),
      },
      orderId: '1',
    },
    {
      id: '2',
      type: 'offer',
      category: 'new_offer',
      title: '20% Off Your Next Order!',
      message: 'Use code SAVE20 and get 20% off on orders above $25. Valid until midnight.',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      isRead: false,
      isImportant: true,
      actionButton: {
        text: 'Use Offer',
        action: () => router.push('/offers/SAVE20'),
      },
      offerId: 'SAVE20',
    },
    {
      id: '3',
      type: 'order',
      category: 'preparing',
      title: 'Order is Being Prepared',
      message: 'Pizza Corner is preparing your order. Estimated time: 25 minutes.',
      timestamp: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
      isRead: true,
      orderId: '2',
    },
    {
      id: '4',
      type: 'update',
      category: 'feature',
      title: 'New Feature: Live Tracking',
      message: 'Track your delivery driver in real-time with our new live tracking feature!',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
      isRead: true,
    },
    {
      id: '5',
      type: 'offer',
      category: 'cashback',
      title: 'Cashback Earned!',
      message: 'You earned $2.50 cashback on your last order. Total balance: $12.75',
      timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      isRead: true,
    },
  ]);

  const [filter, setFilter] = useState<NotificationFilter>('all');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'all') return true;
    return notification.type === filter.slice(0, -1); // Remove 's' from filter
  });

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRefreshing(false);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleMarkAllRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleNotificationPress = (notification: Notification) => {
    if (isSelectionMode) {
      toggleSelection(notification.id);
      return;
    }

    // Mark as read
    setNotifications(prev => 
      prev.map(n => n.id === notification.id ? { ...n, isRead: true } : n)
    );

    // Navigate based on notification type
    if (notification.orderId) {
      router.push(`/orders/${notification.orderId}`);
    } else if (notification.offerId) {
      router.push(`/offers/${notification.offerId}`);
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleNotificationLongPress = (notification: Notification) => {
    setIsSelectionMode(true);
    setSelectedNotifications([notification.id]);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  const toggleSelection = (id: string) => {
    setSelectedNotifications(prev => 
      prev.includes(id) 
        ? prev.filter(nId => nId !== id)
        : [...prev, id]
    );
  };

  const handleMarkSelectedRead = () => {
    setNotifications(prev => 
      prev.map(n => 
        selectedNotifications.includes(n.id) ? { ...n, isRead: true } : n
      )
    );
    exitSelectionMode();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleDeleteSelected = () => {
    Alert.alert(
      'Delete Notifications',
      `Delete ${selectedNotifications.length} notification${selectedNotifications.length !== 1 ? 's' : ''}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setNotifications(prev => 
              prev.filter(n => !selectedNotifications.includes(n.id))
            );
            exitSelectionMode();
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          },
        },
      ]
    );
  };

  const exitSelectionMode = () => {
    setIsSelectionMode(false);
    setSelectedNotifications([]);
  };

  const handleSelectAll = () => {
    const allIds = filteredNotifications.map(n => n.id);
    setSelectedNotifications(
      selectedNotifications.length === allIds.length ? [] : allIds
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => isSelectionMode ? exitSelectionMode() : router.back()}
            activeOpacity={0.7}
          >
            <Text style={styles.backIcon}>
              {isSelectionMode ? '✕' : '←'}
            </Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {isSelectionMode ? `${selectedNotifications.length} Selected` : 'Notifications'}
          </Text>
        </View>

        <View style={styles.headerRight}>
          {isSelectionMode ? (
            <>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={handleSelectAll}
                activeOpacity={0.7}
              >
                <Text style={styles.headerButtonText}>
                  {selectedNotifications.length === filteredNotifications.length ? 'None' : 'All'}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={handleMarkSelectedRead}
                activeOpacity={0.7}
              >
                <Text style={styles.headerButtonIcon}>👁️</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={handleDeleteSelected}
                activeOpacity={0.7}
              >
                <Text style={styles.headerButtonIcon}>🗑️</Text>
              </TouchableOpacity>
            </>
          ) : (
            <>
              {unreadCount > 0 && (
                <TouchableOpacity
                  style={styles.headerButton}
                  onPress={handleMarkAllRead}
                  activeOpacity={0.7}
                >
                  <Text style={styles.markAllReadText}>Mark all read</Text>
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => router.push('/notifications/settings')}
                activeOpacity={0.7}
              >
                <Text style={styles.headerButtonIcon}>⚙️</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>

      {/* Filter Tabs */}
      {!isSelectionMode && (
        <NotificationFilters
          currentFilter={filter}
          onFilterChange={setFilter}
          notifications={notifications}
        />
      )}

      {/* Notifications List */}
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            tintColor="#FF4444"
            colors={['#FF4444']}
          />
        }
        contentContainerStyle={styles.scrollContent}
      >
        {filteredNotifications.length === 0 ? (
          <EmptyNotifications
            filter={filter}
            onClearFilter={() => setFilter('all')}
          />
        ) : (
          filteredNotifications.map((notification) => (
            <NotificationCard
              key={notification.id}
              notification={notification}
              isSelected={selectedNotifications.includes(notification.id)}
              isSelectionMode={isSelectionMode}
              onPress={() => handleNotificationPress(notification)}
              onLongPress={() => handleNotificationLongPress(notification)}
              onToggleRead={(id) => {
                setNotifications(prev => 
                  prev.map(n => n.id === id ? { ...n, isRead: !n.isRead } : n)
                );
              }}
              onDelete={(id) => {
                setNotifications(prev => prev.filter(n => n.id !== id));
              }}
            />
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
  headerButtonText: {
    fontSize: 14,
    color: '#FF4444',
    fontWeight: '600',
  },
  headerButtonIcon: {
    fontSize: 18,
  },
  markAllReadText: {
    fontSize: 14,
    color: '#FF4444',
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
});