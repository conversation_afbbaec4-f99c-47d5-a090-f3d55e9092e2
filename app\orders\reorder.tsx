import * as Haptics from 'expo-haptics';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface ReorderItem {
  id: string;
  name: string;
  originalPrice: number;
  currentPrice: number;
  quantity: number;
  customizations?: string[];
  available: boolean;
  priceChanged: boolean;
  selected: boolean;
}

export default function ReorderScreen() {
  const router = useRouter();
  const [items, setItems] = useState<ReorderItem[]>([
    {
      id: '1',
      name: 'Margherita Pizza',
      originalPrice: 18.99,
      currentPrice: 19.99,
      quantity: 1,
      customizations: ['Extra cheese', 'Thin crust'],
      available: true,
      priceChanged: true,
      selected: true,
    },
    {
      id: '2',
      name: 'Caesar Salad',
      originalPrice: 12.99,
      currentPrice: 12.99,
      quantity: 1,
      customizations: ['No croutons', 'Extra parmesan'],
      available: true,
      priceChanged: false,
      selected: true,
    },
    {
      id: '3',
      name: 'Garlic Bread',
      originalPrice: 6.99,
      currentPrice: 0,
      quantity: 2,
      available: false,
      priceChanged: false,
      selected: false,
    },
  ]);

  const [scheduleOrder, setScheduleOrder] = useState(false);
  const [selectedTime, setSelectedTime] = useState('ASAP');

  const toggleItemSelection = (itemId: string) => {
    setItems(items.map(item => 
      item.id === itemId 
        ? { ...item, selected: !item.selected }
        : item
    ));
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const updateQuantity = (itemId: string, change: number) => {
    setItems(items.map(item => 
      item.id === itemId 
        ? { ...item, quantity: Math.max(0, item.quantity + change) }
        : item
    ));
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const getSelectedItems = () => items.filter(item => item.selected && item.available);
  
  const getTotalPrice = () => {
    return getSelectedItems().reduce((total, item) => 
      total + (item.currentPrice * item.quantity), 0
    );
  };

  const handleAddToCart = () => {
    const selectedItems = getSelectedItems();
    if (selectedItems.length === 0) {
      Alert.alert('No Items Selected', 'Please select at least one available item.');
      return;
    }

    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    Alert.alert(
      'Added to Cart!',
      `${selectedItems.length} item${selectedItems.length !== 1 ? 's' : ''} added to your cart.`,
      [
        { text: 'Continue Shopping', style: 'cancel' },
        { text: 'View Cart', onPress: () => router.push('/cart') },
      ]
    );
  };

  const renderItem = (item: ReorderItem) => (
    <View key={item.id} style={[
      styles.itemCard,
      !item.available && styles.itemCardUnavailable,
    ]}>
      <View style={styles.itemHeader}>
        <TouchableOpacity
          style={styles.checkbox}
          onPress={() => toggleItemSelection(item.id)}
          disabled={!item.available}
        >
          <Text style={styles.checkboxText}>
            {item.selected ? '✓' : '○'}
          </Text>
        </TouchableOpacity>
        
        <View style={styles.itemInfo}>
          <Text style={styles.itemName}>{item.name}</Text>
          {item.customizations && (
            <Text style={styles.customizations}>
              {item.customizations.join(', ')}
            </Text>
          )}
        </View>
        
        <View style={styles.priceSection}>
          {item.priceChanged && (
            <Text style={styles.originalPrice}>
              ${item.originalPrice.toFixed(2)}
            </Text>
          )}
          <Text style={styles.currentPrice}>
            ${item.currentPrice.toFixed(2)}
          </Text>
        </View>
      </View>
      
      {item.available && (
        <View style={styles.quantityControls}>
          <TouchableOpacity
            style={styles.quantityButton}
            onPress={() => updateQuantity(item.id, -1)}
          >
            <Text style={styles.quantityButtonText}>-</Text>
          </TouchableOpacity>
          <Text style={styles.quantity}>{item.quantity}</Text>
          <TouchableOpacity
            style={styles.quantityButton}
            onPress={() => updateQuantity(item.id, 1)}
          >
            <Text style={styles.quantityButtonText}>+</Text>
          </TouchableOpacity>
        </View>
      )}
      
      {!item.available && (
        <Text style={styles.unavailableText}>Currently unavailable</Text>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <Text style={styles.title}>Reorder Items</Text>
          
          <View style={styles.itemsList}>
            {items.map(renderItem)}
          </View>
          
          <TouchableOpacity
            style={styles.addToCartButton}
            onPress={handleAddToCart}
            activeOpacity={0.8}
          >
            <Text style={styles.addToCartText}>
              Add to Cart • ${getTotalPrice().toFixed(2)}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1A1A1A',
    marginBottom: 20,
  },
  itemsList: {
    marginBottom: 20,
  },
  addToCartButton: {
    backgroundColor: '#FF4444',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  addToCartText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  itemCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  itemCardUnavailable: {
    opacity: 0.6,
    backgroundColor: '#F5F5F5',
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#FF4444',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  checkboxText: {
    color: '#FF4444',
    fontSize: 16,
    fontWeight: 'bold',
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
    marginBottom: 4,
  },
  customizations: {
    fontSize: 14,
    color: '#666666',
  },
  priceSection: {
    alignItems: 'flex-end',
  },
  originalPrice: {
    fontSize: 14,
    color: '#999999',
    textDecorationLine: 'line-through',
  },
  currentPrice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF4444',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#FF4444',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  quantity: {
    fontSize: 16,
    fontWeight: '600',
    marginHorizontal: 16,
    minWidth: 24,
    textAlign: 'center',
  },
  unavailableText: {
    fontSize: 14,
    color: '#F44336',
    fontStyle: 'italic',
    textAlign: 'center',
  },
});

