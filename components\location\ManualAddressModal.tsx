import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Animated,
  Dimensions,
  Alert,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import MapView, { Marker } from 'react-native-maps';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface Address {
  id: string;
  title: string;
  subtitle: string;
  latitude: number;
  longitude: number;
  fullAddress: string;
}

interface ManualAddressModalProps {
  visible: boolean;
  onClose: () => void;
  onAddressSelected: (address: Address) => void;
}

export default function ManualAddressModal({
  visible,
  onClose,
  onAddressSelected,
}: ManualAddressModalProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Address[]>([]);
  const [recentAddresses, setRecentAddresses] = useState<Address[]>([]);
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const slideAnim = useRef(new Animated.Value(screenHeight)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      // Load recent addresses
      loadRecentAddresses();
      
      // Animate modal in
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Reset state when modal closes
      setSearchQuery('');
      setSearchResults([]);
      setSelectedAddress(null);
    }
  }, [visible]);

  const loadRecentAddresses = () => {
    // Mock recent addresses
    const mockRecent: Address[] = [
      {
        id: '1',
        title: 'Home',
        subtitle: '123 Main St, New York, NY',
        latitude: 40.7128,
        longitude: -74.0060,
        fullAddress: '123 Main St, New York, NY 10001',
      },
      {
        id: '2',
        title: 'Work',
        subtitle: '456 Business Ave, New York, NY',
        latitude: 40.7589,
        longitude: -73.9851,
        fullAddress: '456 Business Ave, New York, NY 10019',
      },
    ];
    setRecentAddresses(mockRecent);
  };

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    
    if (query.length < 3) {
      setSearchResults([]);
      return;
    }

    setIsLoading(true);
    
    try {
      // Simulate Google Places API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock search results
      const mockResults: Address[] = [
        {
          id: `search-1-${Date.now()}`,
          title: query,
          subtitle: 'New York, NY, USA',
          latitude: 40.7128 + Math.random() * 0.01,
          longitude: -74.0060 + Math.random() * 0.01,
          fullAddress: `${query}, New York, NY 10001`,
        },
        {
          id: `search-2-${Date.now()}`,
          title: `${query} Restaurant`,
          subtitle: 'Manhattan, NY, USA',
          latitude: 40.7589 + Math.random() * 0.01,
          longitude: -73.9851 + Math.random() * 0.01,
          fullAddress: `${query} Restaurant, Manhattan, NY 10019`,
        },
      ];
      
      setSearchResults(mockResults);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddressSelect = (address: Address) => {
    setSelectedAddress(address);
    setSearchQuery(address.title);
    setSearchResults([]);
    Haptics.selectionAsync();
  };

  const handleConfirm = () => {
    if (!selectedAddress) {
      Alert.alert('No Address Selected', 'Please select an address to continue.');
      return;
    }

    onAddressSelected(selectedAddress);
    handleClose();
  };

  const handleClose = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: screenHeight,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onClose();
    });
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={handleClose}
    >
      <Animated.View style={[styles.overlay, { opacity: fadeAnim }]}>
        <TouchableOpacity style={styles.backdrop} onPress={handleClose} />
        
        <Animated.View
          style={[
            styles.modalContainer,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Enter Your Address</Text>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          {/* Search Input */}
          <View style={styles.searchContainer}>
            <View style={styles.searchInputContainer}>
              <Text style={styles.searchIcon}>🔍</Text>
              <TextInput
                style={styles.searchInput}
                value={searchQuery}
                onChangeText={handleSearch}
                placeholder="Search for your address..."
                placeholderTextColor="#999999"
                autoFocus
              />
              {isLoading && (
                <View style={styles.loadingIndicator}>
                  <Text style={styles.loadingText}>⏳</Text>
                </View>
              )}
            </View>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Search Results */}
            {searchResults.length > 0 && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Search Results</Text>
                {searchResults.map((address) => (
                  <AddressItem
                    key={address.id}
                    address={address}
                    onSelect={handleAddressSelect}
                    isSelected={selectedAddress?.id === address.id}
                  />
                ))}
              </View>
            )}

            {/* Recent Addresses */}
            {recentAddresses.length > 0 && searchResults.length === 0 && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Recent Addresses</Text>
                {recentAddresses.map((address) => (
                  <AddressItem
                    key={address.id}
                    address={address}
                    onSelect={handleAddressSelect}
                    isSelected={selectedAddress?.id === address.id}
                    showIcon
                  />
                ))}
              </View>
            )}

            {/* Map Preview */}
            {selectedAddress && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Location Preview</Text>
                <View style={styles.mapContainer}>
                  <MapView
                    style={styles.map}
                    region={{
                      latitude: selectedAddress.latitude,
                      longitude: selectedAddress.longitude,
                      latitudeDelta: 0.01,
                      longitudeDelta: 0.01,
                    }}
                  >
                    <Marker
                      coordinate={{
                        latitude: selectedAddress.latitude,
                        longitude: selectedAddress.longitude,
                      }}
                      title={selectedAddress.title}
                      description={selectedAddress.subtitle}
                    />
                  </MapView>
                </View>
              </View>
            )}
          </ScrollView>

          {/* Confirm Button */}
          <View style={styles.footer}>
            <TouchableOpacity
              style={[
                styles.confirmButton,
                !selectedAddress && styles.confirmButtonDisabled,
              ]}
              onPress={handleConfirm}
              disabled={!selectedAddress}
            >
              <Text style={[
                styles.confirmButtonText,
                !selectedAddress && styles.confirmButtonTextDisabled,
              ]}>
                Use This Location
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
}

// Address item component
interface AddressItemProps {
  address: Address;
  onSelect: (address: Address) => void;
  isSelected: boolean;
  showIcon?: boolean;
}

function AddressItem({ address, onSelect, isSelected, showIcon }: AddressItemProps) {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePress = () => {
    onSelect(address);
    
    // Press animation
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const getAddressIcon = (title: string) => {
    if (title.toLowerCase().includes('home')) return '🏠';
    if (title.toLowerCase().includes('work')) return '🏢';
    return '📍';
  };

  return (
    <TouchableOpacity onPress={handlePress}>
      <Animated.View
        style={[
          styles.addressItem,
          isSelected && styles.addressItemSelected,
          { transform: [{ scale: scaleAnim }] },
        ]}
      >
        <View style={styles.addressIcon}>
          <Text style={styles.addressIconText}>
            {showIcon ? getAddressIcon(address.title) : '📍'}
          </Text>
        </View>
        
        <View style={styles.addressInfo}>
          <Text style={styles.addressTitle}>{address.title}</Text>
          <Text style={styles.addressSubtitle}>{address.subtitle}</Text>
        </View>

        {isSelected && (
          <View style={styles.selectedIndicator}>
            <Text style={styles.selectedIcon}>✓</Text>
          </View>
        )}
      </Animated.View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  backdrop: {
    flex: 1,
  },
  modalContainer: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: screenHeight * 0.9,
    paddingBottom: 40,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#666666',
  },
  searchContainer: {
    padding: 20,
    paddingBottom: 10,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 48,
  },
  searchIcon: {
    fontSize: 16,
    marginRight: 12,
    opacity: 0.6,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#2C2C2C',
  },
  loadingIndicator: {
    marginLeft: 8,
  },
  loadingText: {
    fontSize: 16,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 12,
  },
  addressItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#F0F0F0',
  },
  addressItemSelected: {
    borderColor: '#FF4444',
    backgroundColor: '#FFF5F5',
  },
  addressIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  addressIconText: {
    fontSize: 18,
  },
  addressInfo: {
    flex: 1,
  },
  addressTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 4,
  },
  addressSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  selectedIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedIcon: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  mapContainer: {
    height: 200,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  map: {
    flex: 1,
  },
  footer: {
    padding: 20,
    paddingTop: 16,
  },
  confirmButton: {
    height: 48,
    backgroundColor: '#FF4444',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  confirmButtonTextDisabled: {
    color: '#999999',
  },
});