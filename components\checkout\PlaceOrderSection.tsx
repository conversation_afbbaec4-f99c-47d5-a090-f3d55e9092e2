import React from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

interface PlaceOrderSectionProps {
  total: number;
  estimatedTime: string;
  termsAccepted: boolean;
  onTermsChange: (accepted: boolean) => void;
  onPlaceOrder: () => void;
  isProcessing: boolean;
}

export default function PlaceOrderSection({
  total,
  estimatedTime,
  termsAccepted,
  onTermsChange,
  onPlaceOrder,
  isProcessing,
}: PlaceOrderSectionProps) {
  return (
    <View style={styles.container}>
      {/* Terms and Conditions */}
      <TouchableOpacity
        style={styles.termsContainer}
        onPress={() => onTermsChange(!termsAccepted)}
        activeOpacity={0.7}
      >
        <View style={[
          styles.checkbox,
          termsAccepted && styles.checkboxChecked,
        ]}>
          {termsAccepted && (
            <Text style={styles.checkmark}>✓</Text>
          )}
        </View>
        <Text style={styles.termsText}>
          I agree to the{' '}
          <Text style={styles.termsLink}>Terms & Conditions</Text>
          {' '}and{' '}
          <Text style={styles.termsLink}>Privacy Policy</Text>
        </Text>
      </TouchableOpacity>

      {/* Order Summary */}
      <View style={styles.orderSummary}>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Total</Text>
          <Text style={styles.summaryValue}>${total.toFixed(2)}</Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={styles.estimatedTime}>
            🕒 Estimated delivery: {estimatedTime}
          </Text>
        </View>
      </View>

      {/* Place Order Button */}
      <TouchableOpacity
        style={[
          styles.placeOrderButton,
          (!termsAccepted || isProcessing) && styles.placeOrderButtonDisabled,
        ]}
        onPress={onPlaceOrder}
        disabled={!termsAccepted || isProcessing}
        activeOpacity={0.8}
      >
        {isProcessing ? (
          <View style={styles.processingContainer}>
            <ActivityIndicator color="#FFFFFF" size="small" />
            <Text style={styles.processingText}>Processing...</Text>
          </View>
        ) : (
          <Text style={styles.placeOrderText}>
            Place Order • ${total.toFixed(2)}
          </Text>
        )}
      </TouchableOpacity>

      {/* Security Notice */}
      <Text style={styles.securityNotice}>
        🔒 Your payment information is secure and encrypted
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    padding: 16,
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  checkboxChecked: {
    backgroundColor: '#FF4444',
    borderColor: '#FF4444',
  },
  checkmark: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  termsText: {
    flex: 1,
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  termsLink: {
    color: '#FF4444',
    textDecorationLine: 'underline',
  },
  orderSummary: {
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF4444',
  },
  estimatedTime: {
    fontSize: 14,
    color: '#666666',
  },
  placeOrderButton: {
    backgroundColor: '#FF4444',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  placeOrderButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  placeOrderText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  processingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  processingText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  securityNotice: {
    fontSize: 12,
    color: '#999999',
    textAlign: 'center',
  },
});