import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';

interface Restaurant {
  id: string;
  name: string;
  cuisine: string;
  rating: number;
  deliveryTime: string;
  deliveryFee: number;
  image: string;
  isOpen: boolean;
}

interface QuickInfoBarProps {
  restaurant: Restaurant;
}

interface InfoCard {
  icon: string;
  title: string;
  subtitle: string;
  iconColor: string;
}

export default function QuickInfoBar({ restaurant }: QuickInfoBarProps) {
  const infoCards: InfoCard[] = [
    {
      icon: '🕐',
      title: restaurant.deliveryTime,
      subtitle: 'Delivery Time',
      iconColor: '#FF4444',
    },
    {
      icon: '💰',
      title: '$15 minimum',
      subtitle: 'Minimum Order',
      iconColor: '#34A853',
    },
    {
      icon: '🚚',
      title: `$${restaurant.deliveryFee} delivery`,
      subtitle: 'Delivery Fee',
      iconColor: '#4285F4',
    },
    {
      icon: '⭐',
      title: `${restaurant.rating} (1.2k reviews)`,
      subtitle: 'Rating',
      iconColor: '#FBBC04',
    },
  ];

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {infoCards.map((card, index) => (
          <View key={index} style={styles.infoCard}>
            <View style={[styles.iconContainer, { backgroundColor: `${card.iconColor}20` }]}>
              <Text style={styles.icon}>{card.icon}</Text>
            </View>
            <View style={styles.cardContent}>
              <Text style={styles.cardTitle}>{card.title}</Text>
              <Text style={styles.cardSubtitle}>{card.subtitle}</Text>
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  scrollContent: {
    paddingHorizontal: 16,
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 12,
    marginRight: 12,
    minWidth: 140,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  icon: {
    fontSize: 16,
  },
  cardContent: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  cardSubtitle: {
    fontSize: 10,
    color: '#666666',
  },
});