import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';

interface SettingsItem {
  id: string;
  title: string;
  icon: string;
  route?: string;
  action?: () => void;
  showChevron?: boolean;
  textColor?: string;
}

interface SettingsSection {
  title: string;
  items: SettingsItem[];
}

interface SettingsMenuProps {
  onSettingsPress: (route: string) => void;
  onLogout: () => void;
}

export default function SettingsMenu({ onSettingsPress, onLogout }: SettingsMenuProps) {
  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'This action cannot be undone. All your data will be permanently deleted.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // Handle account deletion
            console.log('Account deletion requested');
          },
        },
      ]
    );
  };

  const settingsSections: SettingsSection[] = [
    {
      title: 'Account Settings',
      items: [
        {
          id: 'personal-info',
          title: 'Personal Information',
          icon: '👤',
          route: '/profile/personal-info',
          showChevron: true,
        },
        {
          id: 'security',
          title: 'Password & Security',
          icon: '🔒',
          route: '/profile/security',
          showChevron: true,
        },
        {
          id: 'privacy',
          title: 'Privacy Settings',
          icon: '🛡️',
          route: '/profile/privacy',
          showChevron: true,
        },
        {
          id: 'delete-account',
          title: 'Delete Account',
          icon: '🗑️',
          action: handleDeleteAccount,
          showChevron: false,
          textColor: '#FF4444',
        },
      ],
    },
    {
      title: 'App Preferences',
      items: [
        {
          id: 'notifications',
          title: 'Notifications',
          icon: '🔔',
          route: '/profile/notifications',
          showChevron: true,
        },
        {
          id: 'language',
          title: 'Language',
          icon: '🌐',
          route: '/profile/language',
          showChevron: true,
        },
        {
          id: 'theme',
          title: 'Theme',
          icon: '🎨',
          route: '/profile/theme',
          showChevron: true,
        },
        {
          id: 'units',
          title: 'Units',
          icon: '📏',
          route: '/profile/units',
          showChevron: true,
        },
      ],
    },
    {
      title: 'Delivery Preferences',
      items: [
        {
          id: 'default-address',
          title: 'Default Address',
          icon: '🏠',
          route: '/profile/default-address',
          showChevron: true,
        },
        {
          id: 'delivery-instructions',
          title: 'Delivery Instructions',
          icon: '📝',
          route: '/profile/delivery-instructions',
          showChevron: true,
        },
        {
          id: 'dietary-restrictions',
          title: 'Dietary Restrictions',
          icon: '🥗',
          route: '/profile/dietary-restrictions',
          showChevron: true,
        },
      ],
    },
    {
      title: 'Payment & Billing',
      items: [
        {
          id: 'payment-methods',
          title: 'Payment Methods',
          icon: '💳',
          route: '/profile/payment-methods',
          showChevron: true,
        },
        {
          id: 'billing-history',
          title: 'Billing History',
          icon: '📊',
          route: '/profile/billing-history',
          showChevron: true,
        },
        {
          id: 'auto-pay',
          title: 'Auto-pay Settings',
          icon: '⚡',
          route: '/profile/auto-pay',
          showChevron: true,
        },
      ],
    },
    {
      title: 'Support & Legal',
      items: [
        {
          id: 'help-center',
          title: 'Help Center',
          icon: '❓',
          route: '/profile/help-center',
          showChevron: true,
        },
        {
          id: 'contact-support',
          title: 'Contact Support',
          icon: '💬',
          route: '/profile/contact-support',
          showChevron: true,
        },
        {
          id: 'terms',
          title: 'Terms of Service',
          icon: '📄',
          route: '/profile/terms',
          showChevron: true,
        },
        {
          id: 'privacy-policy',
          title: 'Privacy Policy',
          icon: '🔐',
          route: '/profile/privacy-policy',
          showChevron: true,
        },
        {
          id: 'rate-app',
          title: 'Rate App',
          icon: '⭐',
          route: '/profile/rate-app',
          showChevron: true,
        },
      ],
    },
  ];

  const handleItemPress = (item: SettingsItem) => {
    if (item.action) {
      item.action();
    } else if (item.route) {
      onSettingsPress(item.route);
    }
  };

  return (
    <View style={styles.container}>
      {settingsSections.map((section) => (
        <View key={section.title} style={styles.section}>
          <Text style={styles.sectionTitle}>{section.title}</Text>
          
          <View style={styles.itemsContainer}>
            {section.items.map((item, index) => (
              <TouchableOpacity
                key={item.id}
                style={[
                  styles.settingsItem,
                  index === section.items.length - 1 && styles.lastItem,
                ]}
                onPress={() => handleItemPress(item)}
                activeOpacity={0.7}
              >
                <View style={styles.itemLeft}>
                  <Text style={styles.itemIcon}>{item.icon}</Text>
                  <Text style={[
                    styles.itemTitle,
                    item.textColor && { color: item.textColor },
                  ]}>
                    {item.title}
                  </Text>
                </View>
                
                {item.showChevron && (
                  <Text style={styles.chevron}>›</Text>
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      ))}

      {/* Logout Button */}
      <View style={styles.logoutSection}>
        <TouchableOpacity
          style={styles.logoutButton}
          onPress={onLogout}
          activeOpacity={0.8}
        >
          <Text style={styles.logoutIcon}>🚪</Text>
          <Text style={styles.logoutText}>Sign Out</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666666',
    marginBottom: 12,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  itemsContainer: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    overflow: 'hidden',
  },
  settingsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  lastItem: {
    borderBottomWidth: 0,
  },
  itemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  itemIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  itemTitle: {
    fontSize: 16,
    color: '#2C2C2C',
  },
  chevron: {
    fontSize: 20,
    color: '#CCCCCC',
    fontWeight: 'bold',
  },
  logoutSection: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF4444',
    paddingVertical: 16,
    borderRadius: 12,
  },
  logoutIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});