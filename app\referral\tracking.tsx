import * as Haptics from 'expo-haptics';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Al<PERSON>,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface ReferralTracking {
  id: string;
  name: string;
  phone: string;
  email?: string;
  invitedDate: string;
  status: 'pending' | 'downloaded' | 'registered' | 'ordered' | 'expired';
  rewardAmount: number;
  daysLeft: number;
  lastActivity?: string;
  orderValue?: number;
  shareMethod: 'whatsapp' | 'sms' | 'email' | 'link' | 'qr';
}

interface ConversionStats {
  totalInvited: number;
  downloaded: number;
  registered: number;
  ordered: number;
  expired: number;
  conversionRate: number;
  averageTime: number;
}

export default function ReferralTrackingScreen() {
  const router = useRouter();
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [expandedReferral, setExpandedReferral] = useState<string | null>(null);

  const referrals: ReferralTracking[] = [
    {
      id: '1',
      name: '<PERSON>',
      phone: '+****************',
      email: '<EMAIL>',
      invitedDate: '2024-01-15',
      status: 'ordered',
      rewardAmount: 5.00,
      daysLeft: 0,
      lastActivity: '2024-01-18',
      orderValue: 32.50,
      shareMethod: 'whatsapp',
    },
    {
      id: '2',
      name: 'Mike Chen',
      phone: '+****************',
      invitedDate: '2024-01-18',
      status: 'registered',
      rewardAmount: 5.00,
      daysLeft: 9,
      lastActivity: '2024-01-19',
      shareMethod: 'sms',
    },
    {
      id: '3',
      name: 'Emma Wilson',
      phone: '+****************',
      invitedDate: '2024-01-20',
      status: 'downloaded',
      rewardAmount: 5.00,
      daysLeft: 7,
      lastActivity: '2024-01-20',
      shareMethod: 'email',
    },
    {
      id: '4',
      name: 'David Brown',
      phone: '+****************',
      invitedDate: '2024-01-22',
      status: 'pending',
      rewardAmount: 5.00,
      daysLeft: 5,
      shareMethod: 'link',
    },
    {
      id: '5',
      name: 'Lisa Garcia',
      phone: '+****************',
      invitedDate: '2023-12-15',
      status: 'expired',
      rewardAmount: 0,
      daysLeft: 0,
      shareMethod: 'whatsapp',
    },
    {
      id: '6',
      name: 'Tom Wilson',
      phone: '+****************',
      invitedDate: '2024-01-10',
      status: 'ordered',
      rewardAmount: 5.00,
      daysLeft: 0,
      lastActivity: '2024-01-12',
      orderValue: 28.75,
      shareMethod: 'qr',
    },
  ];

  const stats: ConversionStats = {
    totalInvited: referrals.length,
    downloaded: referrals.filter(r => ['downloaded', 'registered', 'ordered'].includes(r.status)).length,
    registered: referrals.filter(r => ['registered', 'ordered'].includes(r.status)).length,
    ordered: referrals.filter(r => r.status === 'ordered').length,
    expired: referrals.filter(r => r.status === 'expired').length,
    conversionRate: (referrals.filter(r => r.status === 'ordered').length / referrals.length) * 100,
    averageTime: 3.5,
  };

  const filters = [
    { id: 'all', name: 'All', count: referrals.length },
    { id: 'pending', name: 'Pending', count: referrals.filter(r => r.status === 'pending').length },
    { id: 'downloaded', name: 'Downloaded', count: referrals.filter(r => r.status === 'downloaded').length },
    { id: 'registered', name: 'Registered', count: referrals.filter(r => r.status === 'registered').length },
    { id: 'ordered', name: 'Ordered', count: referrals.filter(r => r.status === 'ordered').length },
    { id: 'expired', name: 'Expired', count: referrals.filter(r => r.status === 'expired').length },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#9E9E9E';
      case 'downloaded': return '#2196F3';
      case 'registered': return '#FF9800';
      case 'ordered': return '#4CAF50';
      case 'expired': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return '⏳';
      case 'downloaded': return '📱';
      case 'registered': return '✍️';
      case 'ordered': return '✅';
      case 'expired': return '❌';
      default: return '⏳';
    }
  };

  const getShareMethodIcon = (method: string) => {
    switch (method) {
      case 'whatsapp': return '💬';
      case 'sms': return '📱';
      case 'email': return '📧';
      case 'link': return '🔗';
      case 'qr': return '📱';
      default: return '📤';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'Invitation Sent';
      case 'downloaded': return 'App Downloaded';
      case 'registered': return 'Account Created';
      case 'ordered': return 'First Order Complete';
      case 'expired': return 'Invitation Expired';
      default: return 'Unknown';
    }
  };

  const sendReminder = (referral: ReferralTracking) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert(
      'Send Reminder',
      `Send a follow-up invitation to ${referral.name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Send', 
          onPress: () => {
            Alert.alert('Reminder Sent!', `Follow-up invitation sent to ${referral.name}`);
          }
        },
      ]
    );
  };

  const filteredReferrals = selectedFilter === 'all' 
    ? referrals 
    : referrals.filter(r => r.status === selectedFilter);

  const renderConversionFunnel = () => (
    <View style={styles.funnelCard}>
      <Text style={styles.funnelTitle}>Conversion Funnel</Text>
      
      <View style={styles.funnelSteps}>
        <View style={styles.funnelStep}>
          <View style={[styles.funnelBar, { width: '100%', backgroundColor: '#9E9E9E' }]} />
          <Text style={styles.funnelLabel}>Invited: {stats.totalInvited}</Text>
        </View>
        
        <View style={styles.funnelStep}>
          <View style={[
            styles.funnelBar, 
            { 
              width: `${(stats.downloaded / stats.totalInvited) * 100}%`, 
              backgroundColor: '#2196F3' 
            }
          ]} />
          <Text style={styles.funnelLabel}>Downloaded: {stats.downloaded}</Text>
        </View>
        
        <View style={styles.funnelStep}>
          <View style={[
            styles.funnelBar, 
            { 
              width: `${(stats.registered / stats.totalInvited) * 100}%`, 
              backgroundColor: '#FF9800' 
            }
          ]} />
          <Text style={styles.funnelLabel}>Registered: {stats.registered}</Text>
        </View>
        
        <View style={styles.funnelStep}>
          <View style={[
            styles.funnelBar, 
            { 
              width: `${(stats.ordered / stats.totalInvited) * 100}%`, 
              backgroundColor: '#4CAF50' 
            }
          ]} />
          <Text style={styles.funnelLabel}>Ordered: {stats.ordered}</Text>
        </View>
        
        <View style={styles.funnelStep}>
          <View style={[
            styles.funnelBar, 
            { 
              width: `${(stats.expired / stats.totalInvited) * 100}%`, 
              backgroundColor: '#F44336' 
            }
          ]} />
          <Text style={styles.funnelLabel}>Expired: {stats.expired}</Text>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Referral Tracking</Text>
        <TouchableOpacity onPress={() => router.push('/referral')}>
          <Text style={styles.backButton}>Back</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.filters}>
        {filters.map(filter => (
          <TouchableOpacity 
            key={filter.id}
            style={[
              styles.filterButton,
              { backgroundColor: selectedFilter === filter.id ? '#FF9800' : '#F5F5F5' }
            ]}
            onPress={() => setSelectedFilter(filter.id!)}
          >
            <Text style={styles.filterText}>{filter.name}</Text>
            <Text style={styles.filterCount}>{filter.count}</Text>
          </TouchableOpacity>
        ))}
      </View>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {renderConversionFunnel()}
        {filteredReferrals.map(referral => (
          <View key={referral.id} style={styles.referralCard}>
            <View style={styles.referralHeader}>
              <Text style={styles.referralName}>{referral.name}</Text>
              <Text style={styles.referralPhone}>{referral.phone}</Text>
            </View>
            <View style={styles.referralStatus}>
              <Text style={[styles.statusText, { color: getStatusColor(referral.status) }]}>
                {getStatusIcon(referral.status)} {getStatusText(referral.status)}
              </Text>
              <Text style={styles.shareMethod}>
                {getShareMethodIcon(referral.shareMethod)} {referral.shareMethod}
              </Text>
            </View>
            <View style={styles.referralDetails}>
              <Text style={styles.detailText}>Invited on: {referral.invitedDate}</Text>
              {referral.lastActivity && (
                <Text style={styles.detailText}>Last Activity: {referral.lastActivity}</Text>
              )}
              {referral.orderValue && (
                <Text style={styles.detailText}>Order Value: ${referral.orderValue.toFixed(2)}</Text>
              )}
              {referral.daysLeft! > 0 && (
                <Text style={styles.detailText}>Days Left: {referral.daysLeft!}</Text>
              )}
              {referral.rewardAmount! > 0 && (
                <Text style={styles.detailText}>Reward: ${referral.rewardAmount!}</Text>
              )}
            </View>
            <TouchableOpacity 
              style={styles.reminderButton}
              onPress={() => sendReminder(referral)}
            >
              <Text style={styles.reminderButtonText}>Send Reminder</Text>
            </TouchableOpacity>
          </View>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FF9800',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  backButton: {
    fontSize: 16,
    color: '#fff',
  },
  filters: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 10,
    backgroundColor: '#F5F5F5',
  },
  filterButton: {
    flex: 1,
    alignItems: 'center',
    padding: 10,
    borderRadius: 5,
    margin: 5,
  },
  filterText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  filterCount: {
    fontSize: 14,
    color: '#9E9E9E',
  },
  scrollContainer: {
    padding: 16,
  },
  funnelCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  funnelTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  funnelSteps: {
    flexDirection: 'column',
    gap: 10,
  },
  funnelStep: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  funnelBar: {
    height: 10,
    borderRadius: 5,
    backgroundColor: '#9E9E9E',
  },
  funnelLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  referralCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  referralHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  referralName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  referralPhone: {
    fontSize: 16,
    color: '#9E9E9E',
  },
  referralStatus: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  statusText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  shareMethod: {
    fontSize: 16,
    color: '#9E9E9E',
  },
  referralDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
    marginBottom: 10,
  },
  detailText: {
    fontSize: 14,
    color: '#9E9E9E',
  },
  reminderButton: {
    backgroundColor: '#FF9800',
    borderRadius: 5,
    padding: 10,
    alignItems: 'center',
  },
  reminderButtonText: {
    fontSize: 16,
    color: '#fff',
  },
});

