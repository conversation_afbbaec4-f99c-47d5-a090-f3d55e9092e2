import React, { useRef, useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  ScrollView,
  TouchableOpacity,
  Animated,
  Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface OnboardingSlide {
  id: number;
  title: string;
  subtitle: string;
  illustration: string;
  backgroundColor: string;
  iconColor: string;
}

const slides: OnboardingSlide[] = [
  {
    id: 1,
    title: 'Discover Great Food',
    subtitle: 'Browse thousands of restaurants and cuisines',
    illustration: '🍽️',
    backgroundColor: '#FFFFFF',
    iconColor: '#FF4444',
  },
  {
    id: 2,
    title: 'Lightning Fast Delivery',
    subtitle: 'Get your food delivered in 30 minutes or less',
    illustration: '🚴‍♂️',
    backgroundColor: '#FFFFFF',
    iconColor: '#34A853',
  },
  {
    id: 3,
    title: 'Track Every Step',
    subtitle: 'Real-time updates from kitchen to your door',
    illustration: '📍',
    backgroundColor: '#FFFFFF',
    iconColor: '#4285F4',
  },
];

interface OnboardingCarouselProps {
  onComplete: () => void;
}

export default function OnboardingCarousel({ onComplete }: OnboardingCarouselProps) {
  const scrollViewRef = useRef<ScrollView>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    // Initial animation
    animateSlideIn();
  }, [currentIndex]);

  const animateSlideIn = () => {
    // Reset animations
    fadeAnim.setValue(0);
    slideAnim.setValue(50);
    scaleAnim.setValue(0.8);

    // Staggered animation sequence
    Animated.sequence([
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleScroll = (event: any) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffset / screenWidth);
    if (index !== currentIndex) {
      setCurrentIndex(index);
    }
  };

  const goToNext = () => {
    if (currentIndex < slides.length - 1) {
      const nextIndex = currentIndex + 1;
      scrollViewRef.current?.scrollTo({
        x: nextIndex * screenWidth,
        animated: true,
      });
      setCurrentIndex(nextIndex);
    } else {
      onComplete();
    }
  };

  const skipOnboarding = () => {
    onComplete();
  };

  return (
    <View style={styles.container}>
      {/* Skip Button */}
      <TouchableOpacity style={styles.skipButton} onPress={skipOnboarding}>
        <Text style={styles.skipText}>Skip</Text>
      </TouchableOpacity>

      {/* Slides */}
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={handleScroll}
        style={styles.scrollView}
      >
        {slides.map((slide, index) => (
          <OnboardingSlide
            key={slide.id}
            slide={slide}
            isActive={index === currentIndex}
            fadeAnim={fadeAnim}
            slideAnim={slideAnim}
            scaleAnim={scaleAnim}
          />
        ))}
      </ScrollView>

      {/* Bottom Navigation */}
      <View style={styles.bottomContainer}>
        {/* Dots Indicator */}
        <View style={styles.dotsContainer}>
          {slides.map((_, index) => (
            <View
              key={index}
              style={[
                styles.dot,
                {
                  backgroundColor: index === currentIndex ? '#FF4444' : '#CCCCCC',
                  width: index === currentIndex ? 24 : 8,
                },
              ]}
            />
          ))}
        </View>

        {/* Next/Get Started Button */}
        <TouchableOpacity style={styles.nextButton} onPress={goToNext}>
          <LinearGradient
            colors={['#FF4444', '#FF6B35']}
            style={styles.buttonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Text style={styles.buttonText}>
              {currentIndex === slides.length - 1 ? 'Get Started' : 'Next'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );
}

interface OnboardingSlideProps {
  slide: OnboardingSlide;
  isActive: boolean;
  fadeAnim: Animated.Value;
  slideAnim: Animated.Value;
  scaleAnim: Animated.Value;
}

function OnboardingSlide({ slide, isActive, fadeAnim, slideAnim, scaleAnim }: OnboardingSlideProps) {
  return (
    <View style={[styles.slide, { backgroundColor: slide.backgroundColor }]}>
      {/* Hero Section with Parallax Effect */}
      <View style={styles.heroSection}>
        <Animated.View
          style={[
            styles.illustrationContainer,
            {
              transform: [{ scale: scaleAnim }],
              opacity: fadeAnim,
            },
          ]}
        >
          <View style={[styles.iconBackground, { backgroundColor: `${slide.iconColor}15` }]}>
            <Text style={styles.illustration}>{slide.illustration}</Text>
            <PulseAnimation color={slide.iconColor} />
          </View>
        </Animated.View>

        {/* Decorative Elements */}
        <View style={styles.decorativeElements}>
          <DecorativeCircle color={slide.iconColor} size={60} top={100} left={50} />
          <DecorativeCircle color={slide.iconColor} size={40} top={200} right={80} />
          <DecorativeCircle color={slide.iconColor} size={80} bottom={150} left={30} />
        </View>
      </View>

      {/* Content Section */}
      <View style={styles.contentSection}>
        <Animated.View
          style={[
            styles.textContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <Text style={styles.title}>{slide.title}</Text>
          <Text style={styles.subtitle}>{slide.subtitle}</Text>
        </Animated.View>

        {/* Feature Highlights */}
        <FeatureHighlights slideId={slide.id} iconColor={slide.iconColor} />
      </View>
    </View>
  );
}

// Pulse animation for icons
function PulseAnimation({ color }: { color: string }) {
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]).start(() => pulse());
    };
    pulse();
  }, []);

  return (
    <Animated.View
      style={[
        styles.pulseRing,
        {
          borderColor: color,
          transform: [{ scale: pulseAnim }],
        },
      ]}
    />
  );
}

// Decorative background circles
function DecorativeCircle({ color, size, top, bottom, left, right }: any) {
  const floatAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const float = () => {
      Animated.sequence([
        Animated.timing(floatAnim, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: true,
        }),
        Animated.timing(floatAnim, {
          toValue: 0,
          duration: 3000,
          useNativeDriver: true,
        }),
      ]).start(() => float());
    };
    float();
  }, []);

  const translateY = floatAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -10],
  });

  return (
    <Animated.View
      style={[
        styles.decorativeCircle,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: `${color}10`,
          borderColor: `${color}20`,
          top,
          bottom,
          left,
          right,
          transform: [{ translateY }],
        },
      ]}
    />
  );
}

// Feature highlights for each slide
function FeatureHighlights({ slideId, iconColor }: { slideId: number; iconColor: string }) {
  const features = {
    1: ['🔍 Smart Search', '⭐ Top Rated', '🏷️ Best Deals'],
    2: ['⚡ 30 Min Delivery', '🛵 Live Tracking', '📞 24/7 Support'],
    3: ['📱 Real-time Updates', '🗺️ GPS Tracking', '🔔 Push Notifications'],
  };

  return (
    <View style={styles.featuresContainer}>
      {features[slideId as keyof typeof features]?.map((feature, index) => (
        <View key={index} style={styles.featureItem}>
          <View style={[styles.featureDot, { backgroundColor: iconColor }]} />
          <Text style={styles.featureText}>{feature}</Text>
        </View>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  skipButton: {
    position: 'absolute',
    top: 60,
    right: 20,
    zIndex: 10,
    padding: 10,
  },
  skipText: {
    color: '#666666',
    fontSize: 16,
    fontWeight: '500',
  },
  scrollView: {
    flex: 1,
  },
  slide: {
    width: screenWidth,
    flex: 1,
    paddingHorizontal: 20,
  },
  heroSection: {
    flex: 0.6,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  illustrationContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBackground: {
    width: 160,
    height: 160,
    borderRadius: 80,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  illustration: {
    fontSize: 80,
  },
  pulseRing: {
    position: 'absolute',
    width: 180,
    height: 180,
    borderRadius: 90,
    borderWidth: 2,
    opacity: 0.3,
  },
  decorativeElements: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  decorativeCircle: {
    position: 'absolute',
    borderWidth: 1,
  },
  contentSection: {
    flex: 0.4,
    paddingTop: 20,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2C2C2C',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  featuresContainer: {
    alignItems: 'flex-start',
    paddingHorizontal: 40,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 12,
  },
  featureText: {
    fontSize: 14,
    color: '#666666',
  },
  bottomContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
    alignItems: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 30,
    gap: 8,
  },
  dot: {
    height: 8,
    borderRadius: 4,
    transition: 'all 0.3s ease',
  },
  nextButton: {
    width: '100%',
    height: 56,
    borderRadius: 28,
    overflow: 'hidden',
  },
  buttonGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
});