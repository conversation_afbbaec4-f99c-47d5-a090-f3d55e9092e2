import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
  PanGestureHandler,
  State,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Image } from 'expo-image';
import * as Haptics from 'expo-haptics';

import { useCart } from '@/hooks/useCart';
import CartItem from '@/components/cart/CartItem';
import OrderSummary from '@/components/cart/OrderSummary';
import PromoCodeSection from '@/components/cart/PromoCodeSection';
import DeliveryOptions from '@/components/cart/DeliveryOptions';
import EmptyCart from '@/components/cart/EmptyCart';

export default function CartScreen() {
  const { cartItems, getTotalItems, getTotalPrice, clearCart } = useCart();
  const [appliedPromo, setAppliedPromo] = useState<string | null>(null);
  const [selectedDelivery, setSelectedDelivery] = useState<'standard' | 'express' | 'scheduled'>('standard');
  const [selectedAddress, setSelectedAddress] = useState('123 Main St, City, State 12345');

  const fadeAnim = useRef(new Animated.Value(1)).current;

  const handleClearCart = () => {
    Alert.alert(
      'Clear Cart',
      'Are you sure you want to remove all items from your cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: () => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            clearCart();
          },
        },
      ]
    );
  };

  const handleCheckout = () => {
    if (cartItems.length === 0) return;
    
    const subtotal = getTotalPrice();
    const minimumOrder = 15.00;
    
    if (subtotal < minimumOrder) {
      Alert.alert(
        'Minimum Order Not Met',
        `Please add $${(minimumOrder - subtotal).toFixed(2)} more to meet the minimum order requirement.`,
        [{ text: 'OK' }]
      );
      return;
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push('/checkout');
  };

  const calculateDeliveryFee = () => {
    switch (selectedDelivery) {
      case 'express':
        return 4.99;
      case 'scheduled':
        return 1.99;
      default:
        return 2.99;
    }
  };

  const calculateServiceFee = () => {
    return getTotalPrice() * 0.05; // 5% service fee
  };

  const calculateTax = () => {
    return (getTotalPrice() + calculateDeliveryFee() + calculateServiceFee()) * 0.08; // 8% tax
  };

  const calculateDiscount = () => {
    if (appliedPromo === 'SAVE20') return getTotalPrice() * 0.2;
    if (appliedPromo === 'FIRST10') return Math.min(getTotalPrice() * 0.1, 10);
    return 0;
  };

  const calculateTotal = () => {
    const subtotal = getTotalPrice();
    const deliveryFee = calculateDeliveryFee();
    const serviceFee = calculateServiceFee();
    const tax = calculateTax();
    const discount = calculateDiscount();
    
    return subtotal + deliveryFee + serviceFee + tax - discount;
  };

  const getDeliveryTime = () => {
    switch (selectedDelivery) {
      case 'express':
        return '15-25 min';
      case 'scheduled':
        return 'Scheduled time';
      default:
        return '30-45 min';
    }
  };

  if (cartItems.length === 0) {
    return <EmptyCart />;
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.title}>Your Order</Text>
          <Text style={styles.itemCount}>({getTotalItems()} items)</Text>
        </View>
        <TouchableOpacity onPress={handleClearCart} activeOpacity={0.7}>
          <Text style={styles.clearButton}>Clear cart</Text>
        </TouchableOpacity>
      </View>

      {/* Restaurant Info */}
      <View style={styles.restaurantInfo}>
        <Text style={styles.restaurantName}>Mario's Italian Kitchen</Text>
        <Text style={styles.deliveryEstimate}>Delivery in {getDeliveryTime()}</Text>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Cart Items */}
        <View style={styles.cartItems}>
          {cartItems.map((item, index) => (
            <CartItem
              key={`${item.id}-${JSON.stringify(item.customization)}`}
              item={item}
              index={index}
            />
          ))}
        </View>

        {/* Delivery Options */}
        <DeliveryOptions
          selectedDelivery={selectedDelivery}
          onDeliveryChange={setSelectedDelivery}
          selectedAddress={selectedAddress}
          onAddressChange={setSelectedAddress}
        />

        {/* Promo Code Section */}
        <PromoCodeSection
          appliedPromo={appliedPromo}
          onPromoApplied={setAppliedPromo}
        />

        {/* Order Summary */}
        <OrderSummary
          subtotal={getTotalPrice()}
          deliveryFee={calculateDeliveryFee()}
          serviceFee={calculateServiceFee()}
          tax={calculateTax()}
          discount={calculateDiscount()}
          total={calculateTotal()}
          appliedPromo={appliedPromo}
        />

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <View style={styles.deliveryInfo}>
          <Text style={styles.deliveryTime}>Estimated delivery: {getDeliveryTime()}</Text>
          {getTotalPrice() < 15 && (
            <Text style={styles.minimumOrder}>
              Add ${(15 - getTotalPrice()).toFixed(2)} more for minimum order
            </Text>
          )}
        </View>
        
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.continueButton}
            onPress={() => router.back()}
            activeOpacity={0.7}
          >
            <Text style={styles.continueButtonText}>Continue Shopping</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.checkoutButton,
              getTotalPrice() < 15 && styles.checkoutButtonDisabled,
            ]}
            onPress={handleCheckout}
            disabled={getTotalPrice() < 15}
            activeOpacity={0.8}
          >
            <Text style={styles.checkoutButtonText}>
              Proceed to Checkout • ${calculateTotal().toFixed(2)}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginRight: 8,
  },
  itemCount: {
    fontSize: 16,
    color: '#666666',
  },
  clearButton: {
    fontSize: 14,
    color: '#FF4444',
    fontWeight: '600',
  },
  restaurantInfo: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#F8F8F8',
  },
  restaurantName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  deliveryEstimate: {
    fontSize: 14,
    color: '#666666',
  },
  scrollView: {
    flex: 1,
  },
  cartItems: {
    paddingTop: 8,
  },
  bottomSpacing: {
    height: 20,
  },
  bottomActions: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 16,
  },
  deliveryInfo: {
    marginBottom: 12,
  },
  deliveryTime: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 4,
  },
  minimumOrder: {
    fontSize: 12,
    color: '#FF4444',
    textAlign: 'center',
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  continueButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FF4444',
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: 16,
    color: '#FF4444',
    fontWeight: '600',
  },
  checkoutButton: {
    flex: 2,
    paddingVertical: 14,
    borderRadius: 8,
    backgroundColor: '#FF4444',
    alignItems: 'center',
  },
  checkoutButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  checkoutButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});