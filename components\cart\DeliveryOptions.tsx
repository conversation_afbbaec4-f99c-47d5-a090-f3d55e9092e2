import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface DeliveryOptionsProps {
  selectedDelivery: 'standard' | 'express' | 'scheduled';
  onDeliveryChange: (delivery: 'standard' | 'express' | 'scheduled') => void;
  selectedAddress: string;
  onAddressChange: (address: string) => void;
}

const savedAddresses = [
  '123 Main St, City, State 12345',
  '456 Oak Ave, City, State 12345',
  '789 Pine Rd, City, State 12345',
];

export default function DeliveryOptions({
  selectedDelivery,
  onDeliveryChange,
  selectedAddress,
  onAddressChange,
}: DeliveryOptionsProps) {
  const [showAddressModal, setShowAddressModal] = useState(false);

  const deliveryOptions = [
    {
      id: 'standard' as const,
      title: 'Standard Delivery',
      time: '30-45 min',
      price: 2.99,
      description: 'Regular delivery',
    },
    {
      id: 'express' as const,
      title: 'Express Delivery',
      time: '15-25 min',
      price: 4.99,
      description: 'Faster delivery',
    },
    {
      id: 'scheduled' as const,
      title: 'Scheduled Delivery',
      time: 'Choose time',
      price: 1.99,
      description: 'Deliver at your preferred time',
    },
  ];

  const handleDeliveryChange = (delivery: 'standard' | 'express' | 'scheduled') => {
    Haptics.selectionAsync();
    onDeliveryChange(delivery);
  };

  const handleAddressSelect = (address: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onAddressChange(address);
    setShowAddressModal(false);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Delivery Options</Text>
      
      {/* Delivery Methods */}
      <View style={styles.deliveryMethods}>
        {deliveryOptions.map((option) => (
          <TouchableOpacity
            key={option.id}
            style={[
              styles.deliveryOption,
              selectedDelivery === option.id && styles.selectedDeliveryOption,
            ]}
            onPress={() => handleDeliveryChange(option.id)}
            activeOpacity={0.7}
          >
            <View style={styles.deliveryOptionContent}>
              <View style={styles.deliveryInfo}>
                <Text style={[
                  styles.deliveryTitle,
                  selectedDelivery === option.id && styles.selectedDeliveryTitle,
                ]}>
                  {option.title}
                </Text>
                <Text style={styles.deliveryDescription}>
                  {option.description}
                </Text>
                <Text style={styles.deliveryTime}>{option.time}</Text>
              </View>
              
              <View style={styles.deliveryPrice}>
                <Text style={[
                  styles.priceText,
                  selectedDelivery === option.id && styles.selectedPriceText,
                ]}>
                  ${option.price.toFixed(2)}
                </Text>
              </View>
            </View>
            
            <View style={[
              styles.radioButton,
              selectedDelivery === option.id && styles.selectedRadioButton,
            ]}>
              {selectedDelivery === option.id && (
                <View style={styles.radioButtonInner} />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {/* Address Selection */}
      <View style={styles.addressSection}>
        <Text style={styles.addressLabel}>Deliver to:</Text>
        <TouchableOpacity
          style={styles.addressSelector}
          onPress={() => setShowAddressModal(true)}
          activeOpacity={0.7}
        >
          <Text style={styles.selectedAddress} numberOfLines={1}>
            {selectedAddress}
          </Text>
          <Text style={styles.changeAddressText}>Change</Text>
        </TouchableOpacity>
      </View>

      {/* Address Modal */}
      <Modal
        visible={showAddressModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Address</Text>
            <TouchableOpacity
              onPress={() => setShowAddressModal(false)}
              style={styles.closeButton}
              activeOpacity={0.7}
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            {savedAddresses.map((address, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.addressOption,
                  selectedAddress === address && styles.selectedAddressOption,
                ]}
                onPress={() => handleAddressSelect(address)}
                activeOpacity={0.7}
              >
                <Text style={[
                  styles.addressText,
                  selectedAddress === address && styles.selectedAddressText,
                ]}>
                  {address}
                </Text>
                {selectedAddress === address && (
                  <Text style={styles.checkmark}>✓</Text>
                )}
              </TouchableOpacity>
            ))}
            
            <TouchableOpacity
              style={styles.addAddressButton}
              activeOpacity={0.7}
            >
              <Text style={styles.addAddressText}>+ Add New Address</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#F0F0F0',
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
  deliveryMethods: {
    marginBottom: 20,
  },
  deliveryOption: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedDeliveryOption: {
    borderColor: '#FF4444',
    backgroundColor: '#FFF5F5',
  },
  deliveryOptionContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  deliveryInfo: {
    flex: 1,
  },
  deliveryTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  selectedDeliveryTitle: {
    color: '#FF4444',
  },
  deliveryDescription: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 2,
  },
  deliveryTime: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  deliveryPrice: {
    marginRight: 12,
  },
  priceText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  selectedPriceText: {
    color: '#FF4444',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedRadioButton: {
    borderColor: '#FF4444',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#FF4444',
  },
  addressSection: {
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    paddingTop: 16,
  },
  addressLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
    marginBottom: 8,
  },
  addressSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  selectedAddress: {
    flex: 1,
    fontSize: 14,
    color: '#2C2C2C',
    marginRight: 12,
  },
  changeAddressText: {
    fontSize: 14,
    color: '#FF4444',
    fontWeight: '500',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#666666',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  addressOption: {
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#F0F0F0',
    marginBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectedAddressOption: {
    borderColor: '#FF4444',
    backgroundColor: '#FFF5F5',
  },
  addressText: {
    fontSize: 14,
    color: '#2C2C2C',
    flex: 1,
  },
  selectedAddressText: {
    color: '#FF4444',
    fontWeight: '500',
  },
  checkmark: {
    fontSize: 16,
    color: '#FF4444',
    fontWeight: 'bold',
  },
  addAddressButton: {
    paddingVertical: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#FF4444',
    borderRadius: 8,
    borderStyle: 'dashed',
    marginTop: 8,
  },
  addAddressText: {
    fontSize: 14,
    color: '#FF4444',
    fontWeight: '500',
  },
});