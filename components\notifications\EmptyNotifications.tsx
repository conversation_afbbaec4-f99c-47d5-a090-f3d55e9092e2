import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { NotificationFilter } from '@/app/notifications';

interface EmptyNotificationsProps {
  filter: NotificationFilter;
  onClearFilter: () => void;
}

export default function EmptyNotifications({
  filter,
  onClearFilter,
}: EmptyNotificationsProps) {
  const getEmptyStateContent = () => {
    switch (filter) {
      case 'orders':
        return {
          icon: '🍽️',
          title: 'No order notifications',
          subtitle: 'Order updates will appear here',
          showExploreButton: true,
        };
      case 'offers':
        return {
          icon: '🎁',
          title: 'No offer notifications',
          subtitle: 'Promotions and deals will appear here',
          showExploreButton: true,
        };
      case 'updates':
        return {
          icon: '⭐',
          title: 'No update notifications',
          subtitle: 'App updates and news will appear here',
          showExploreButton: false,
        };
      default:
        return {
          icon: '🔔',
          title: 'No notifications yet',
          subtitle: "We'll notify you about orders and offers",
          showExploreButton: true,
        };
    }
  };

  const { icon, title, subtitle, showExploreButton } = getEmptyStateContent();

  const handleExplorePress = () => {
    router.push('/');
  };

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <Text style={styles.icon}>{icon}</Text>
        <Text style={styles.sleepIndicator}>💤</Text>
      </View>
      
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.subtitle}>{subtitle}</Text>

      <View style={styles.actions}>
        {filter !== 'all' && (
          <TouchableOpacity
            style={styles.clearFilterButton}
            onPress={onClearFilter}
            activeOpacity={0.7}
          >
            <Text style={styles.clearFilterText}>Clear filters</Text>
          </TouchableOpacity>
        )}

        {showExploreButton && (
          <TouchableOpacity
            style={styles.exploreButton}
            onPress={handleExplorePress}
            activeOpacity={0.8}
          >
            <Text style={styles.exploreButtonText}>Explore restaurants</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 80,
  },
  iconContainer: {
    position: 'relative',
    marginBottom: 24,
  },
  icon: {
    fontSize: 64,
    opacity: 0.6,
  },
  sleepIndicator: {
    position: 'absolute',
    top: -10,
    right: -10,
    fontSize: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  actions: {
    alignItems: 'center',
    gap: 16,
  },
  clearFilterButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FF4444',
  },
  clearFilterText: {
    fontSize: 14,
    color: '#FF4444',
    fontWeight: '600',
  },
  exploreButton: {
    backgroundColor: '#FF4444',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  exploreButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});