import { useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
  Animated,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface ChatMessage {
  id: string;
  text: string;
  sender: 'user' | 'agent' | 'system';
  timestamp: Date;
  agentName?: string;
  agentAvatar?: string;
  attachments?: string[];
  isTyping?: boolean;
}

interface SupportAgent {
  id: string;
  name: string;
  avatar: string;
  status: 'online' | 'busy' | 'offline';
}

export default function LiveChatScreen() {
  const router = useRouter();
  const scrollViewRef = useRef<ScrollView>(null);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [queuePosition, setQueuePosition] = useState(3);
  const [estimatedWait, setEstimatedWait] = useState(2);
  const [currentAgent, setCurrentAgent] = useState<SupportAgent | null>(null);
  const [isAgentTyping, setIsAgentTyping] = useState(false);
  const [animatedValue] = useState(new Animated.Value(0));

  const quickReplies = [
    'Track my order',
    'Cancel order',
    'Payment issue',
    'Delivery problem',
    'Account help',
    'Refund request',
  ];

  useEffect(() => {
    // Simulate connection process
    const connectTimer = setTimeout(() => {
      setIsConnected(true);
      setCurrentAgent({
        id: '1',
        name: 'Sarah',
        avatar: '👩‍💼',
        status: 'online',
      });
      
      setMessages([
        {
          id: '1',
          text: 'Hi! I\'m Sarah from customer support. How can I help you today?',
          sender: 'agent',
          timestamp: new Date(),
          agentName: 'Sarah',
          agentAvatar: '👩‍💼',
        },
      ]);
    }, 3000);

    // Animate queue updates
    const queueTimer = setInterval(() => {
      if (!isConnected && queuePosition > 0) {
        setQueuePosition(prev => Math.max(0, prev - 1));
        setEstimatedWait(prev => Math.max(0, prev - 0.5));
      }
    }, 1000);

    return () => {
      clearTimeout(connectTimer);
      clearInterval(queueTimer);
    };
  }, [isConnected, queuePosition]);

  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive
    if (messages.length > 0) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  const sendMessage = () => {
    if (message.trim() && isConnected) {
      const newMessage: ChatMessage = {
        id: Date.now().toString(),
        text: message.trim(),
        sender: 'user',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, newMessage]);
      setMessage('');

      // Simulate agent typing and response
      setIsAgentTyping(true);
      setTimeout(() => {
        setIsAgentTyping(false);
        const agentResponse: ChatMessage = {
          id: (Date.now() + 1).toString(),
          text: getAgentResponse(message.trim()),
          sender: 'agent',
          timestamp: new Date(),
          agentName: currentAgent?.name,
          agentAvatar: currentAgent?.avatar,
        };
        setMessages(prev => [...prev, agentResponse]);
      }, 2000);
    }
  };

  const getAgentResponse = (userMessage: string): string => {
    const lowerMessage = userMessage.toLowerCase();
    
    if (lowerMessage.includes('track') || lowerMessage.includes('order')) {
      return 'I can help you track your order. Could you please provide your order number? You can find it in your order confirmation email or in the "My Orders" section of the app.';
    } else if (lowerMessage.includes('cancel')) {
      return 'I understand you want to cancel your order. Let me check if it\'s still possible to cancel. Orders can typically be cancelled within 2 minutes of placing them if the restaurant hasn\'t started preparation.';
    } else if (lowerMessage.includes('payment') || lowerMessage.includes('refund')) {
      return 'I\'m here to help with payment and refund issues. Could you please describe the specific problem you\'re experiencing? This will help me provide the best solution.';
    } else if (lowerMessage.includes('delivery')) {
      return 'I can assist with delivery-related concerns. Are you experiencing a delay, or is there an issue with the delivery location? Please provide more details so I can help you better.';
    } else {
      return 'Thank you for reaching out. I\'m here to help with any questions or concerns you have. Could you please provide more details about your issue so I can assist you better?';
    }
  };

  const sendQuickReply = (reply: string) => {
    setMessage(reply);
    sendMessage();
  };

  const renderQueueStatus = () => (
    <View style={styles.queueContainer}>
      <View style={styles.queueHeader}>
        <Text style={styles.queueIcon}>⏳</Text>
        <Text style={styles.queueTitle}>Connecting you to support...</Text>
      </View>
      
      <View style={styles.queueInfo}>
        <Text style={styles.queuePosition}>
          You are #{queuePosition} in queue
        </Text>
        <Text style={styles.queueWait}>
          Estimated wait: {estimatedWait} minutes
        </Text>
      </View>
      
      <View style={styles.queueProgress}>
        <View style={[
          styles.queueProgressBar,
          { width: `${Math.max(10, (4 - queuePosition) * 25)}%` }
        ]} />
      </View>
      
      <Text style={styles.queueNote}>
        💡 While you wait, you can browse our FAQ section for quick answers
      </Text>
      
      <TouchableOpacity
        style={styles.browseFAQButton}
        onPress={() => router.push('/support/faq')}
        activeOpacity={0.7}
      >
        <Text style={styles.browseFAQText}>Browse FAQ</Text>
      </TouchableOpacity>
    </View>
  );

  const renderAgentInfo = () => (
    <View style={styles.agentInfo}>
      <View style={styles.agentAvatar}>
        <Text style={styles.agentAvatarText}>{currentAgent?.avatar}</Text>
      </View>
      <View style={styles.agentDetails}>
        <Text style={styles.agentName}>Connected to {currentAgent?.name}</Text>
        <View style={styles.agentStatus}>
          <Text style={styles.agentStatusDot}>🟢</Text>
          <Text style={styles.agentStatusText}>Online</Text>
        </View>
      </View>
      <TouchableOpacity
        style={styles.endChatButton}
        onPress={() => {/* End chat */}}
        activeOpacity={0.7}
      >
        <Text style={styles.endChatIcon}>✕</Text>
      </TouchableOpacity>
    </View>
  );

  const renderMessage = (msg: ChatMessage) => (
    <View
      key={msg.id}
      style={[
        styles.messageContainer,
        msg.sender === 'user' ? styles.userMessageContainer : styles.agentMessageContainer,
      ]}
    >
      {msg.sender === 'agent' && (
        <View style={styles.messageAvatar}>
          <Text style={styles.messageAvatarText}>{msg.agentAvatar}</Text>
        </View>
      )}
      
      <View style={[
        styles.messageBubble,
        msg.sender === 'user' ? styles.userMessageBubble : styles.agentMessageBubble,
      ]}>
        <Text style={[
          styles.messageText,
          msg.sender === 'user' ? styles.userMessageText : styles.agentMessageText,
        ]}>
          {msg.text}
        </Text>
        
        <Text style={[
          styles.messageTime,
          msg.sender === 'user' ? styles.userMessageTime : styles.agentMessageTime,
        ]}>
          {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </Text>
      </View>
    </View>
  );

  const renderTypingIndicator = () => (
    <View style={styles.typingContainer}>
      <View style={styles.messageAvatar}>
        <Text style={styles.messageAvatarText}>{currentAgent?.avatar}</Text>
      </View>
      <View style={styles.typingBubble}>
        <View style={styles.typingDots}>
          <Animated.View style={[styles.typingDot, { opacity: animatedValue }]} />
          <Animated.View style={[styles.typingDot, { opacity: animatedValue }]} />
          <Animated.View style={[styles.typingDot, { opacity: animatedValue }]} />
        </View>
      </View>
    </View>
  );

  const renderQuickReplies = () => (
    <View style={styles.quickRepliesContainer}>
      <Text style={styles.quickRepliesTitle}>Quick replies:</Text>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.quickRepliesScroll}
        contentContainerStyle={styles.quickRepliesContent}
      >
        {quickReplies.map((reply, index) => (
          <TouchableOpacity
            key={index}
            style={styles.quickReplyButton}
            onPress={() => sendQuickReply(reply)}
            activeOpacity={0.7}
          >
            <Text style={styles.quickReplyText}>{reply}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderChatInput = () => (
    <View style={styles.inputContainer}>
      <View style={styles.inputRow}>
        <TouchableOpacity
          style={styles.attachButton}
          activeOpacity={0.7}
        >
          <Text style={styles.attachIcon}>📎</Text>
        </TouchableOpacity>
        
        <TextInput
          style={styles.textInput}
          placeholder="Type your message..."
          value={message}
          onChangeText={setMessage}
          multiline
          maxLength={500}
          placeholderTextColor="#999999"
        />
        
        <TouchableOpacity
          style={[
            styles.sendButton,
            message.trim() ? styles.sendButtonActive : styles.sendButtonInactive,
          ]}
          onPress={sendMessage}
          disabled={!message.trim() || !isConnected}
          activeOpacity={0.7}
        >
          <Text style={styles.sendIcon}>➤</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Live Chat Support</Text>
        <TouchableOpacity
          style={styles.historyButton}
          onPress={() => router.push('/support')}
          activeOpacity={0.7}
        >
          <Text style={styles.historyIcon}>📋</Text>
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView 
        style={styles.chatContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {!isConnected ? (
          renderQueueStatus()
        ) : (
          <>
            {renderAgentInfo()}
            
            <ScrollView
              ref={scrollViewRef}
              style={styles.messagesContainer}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.messagesContent}
            >
              {messages.map(renderMessage)}
              {isAgentTyping && renderTypingIndicator()}
            </ScrollView>
            
            {messages.length === 1 && renderQuickReplies()}
            {renderChatInput()}
          </>
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C2C2C',
  },
  historyButton: {
    padding: 8,
  },
  historyIcon: {
    fontSize: 20,
  },
  chatContainer: {
    flex: 1,
  },
  queueContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  queueHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  queueIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  queueTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    textAlign: 'center',
  },
  queueInfo: {
    alignItems: 'center',
    marginBottom: 24,
  },
  queuePosition: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FF4444',
    marginBottom: 8,
  },
  queueWait: {
    fontSize: 14,
    color: '#666666',
  },
  queueProgress: {
    width: '100%',
    height: 4,
    backgroundColor: '#F0F0F0',
    borderRadius: 2,
    marginBottom: 24,
  },
  queueProgressBar: {
    height: '100%',
    backgroundColor: '#FF4444',
    borderRadius: 2,
  },
  queueNote: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 20,
  },
  browseFAQButton: {
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  browseFAQText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1976D2',
  },
  agentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  agentAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#4CAF50',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  agentAvatarText: {
    fontSize: 16,
  },
  agentDetails: {
    flex: 1,
  },
  agentName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 2,
  },
  agentStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  agentStatusDot: {
    fontSize: 8,
    marginRight: 4,
  },
  agentStatusText: {
    fontSize: 12,
    color: '#4CAF50',
    fontWeight: '500',
  },
  endChatButton: {
    padding: 8,
  },
  endChatIcon: {
    fontSize: 16,
    color: '#666666',
  },
  messagesContainer: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  messagesContent: {
    paddingVertical: 16,
  },
  messageContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  userMessageContainer: {
    justifyContent: 'flex-end',
  },
  agentMessageContainer: {
    justifyContent: 'flex-start',
  },
  messageAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  messageAvatarText: {
    fontSize: 16,
  },
  messageBubble: {
    maxWidth: '70%',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 4,
  },
  userMessageBubble: {
    backgroundColor: '#E0E0E0',
  },
  agentMessageBubble: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  messageText: {
    fontSize: 16,
    color: '#2C2C2C',
  },
  userMessageText: {
    color: '#000000',
  },
  agentMessageText: {
    color: '#2C2C2C',
  },
  messageTime: {
    fontSize: 12,
    color: '#888888',
    marginTop: 4,
  },
  userMessageTime: {
    textAlign: 'right',
  },
  agentMessageTime: {
    textAlign: 'left',
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  typingBubble: {
    marginLeft: 8,
  },
  typingDots: {
    flexDirection: 'row',
  },
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
    marginHorizontal: 2,
  },
  quickRepliesContainer: {
    marginVertical: 16,
    paddingHorizontal: 16,
  },
  quickRepliesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  quickRepliesScroll: {
    marginBottom: 16,
  },
  quickRepliesContent: {
    flexDirection: 'row',
    gap: 8,
  },
  quickReplyButton: {
    backgroundColor: '#E0E0E0',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  quickReplyText: {
    fontSize: 14,
    color: '#2C2C2C',
  },
  inputContainer: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  attachButton: {
    marginRight: 8,
  },
  attachIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#2C2C2C',
  },
  sendButton: {
    marginLeft: 8,
    padding: 8,
    borderRadius: 24,
  },
  sendButtonActive: {
    backgroundColor: '#4CAF50',
  },
  sendButtonInactive: {
    backgroundColor: '#E0E0E0',
  },
  sendIcon: {
    fontSize: 24,
    color: '#FFFFFF',
  },
});

