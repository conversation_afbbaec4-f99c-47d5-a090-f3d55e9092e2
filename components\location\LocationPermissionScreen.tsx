import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';
import * as Location from 'expo-location';
import { router } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
  Alert,
  Animated,
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';

import { useLocation } from '@/hooks/useLocation';
import LoadingButton from '../ui/LoadingButton';
import ManualAddressModal from './ManualAddressModal';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface LocationPermissionScreenProps {
  onLocationSet: (location: any) => void;
}

export default function LocationPermissionScreen({ onLocationSet }: LocationPermissionScreenProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showManualEntry, setShowManualEntry] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState<'idle' | 'granted' | 'denied'>('idle');

  const { setUserLocation } = useLocation();

  // Animation values
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const successAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Initial animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Start pulse animation
    startPulseAnimation();
  }, []);

  const startPulseAnimation = () => {
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]).start(() => {
        if (permissionStatus === 'idle') {
          pulse();
        }
      });
    };
    pulse();
  };

  const handleEnableLocation = async () => {
    setIsLoading(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    try {
      // Request permission
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status !== 'granted') {
        setPermissionStatus('denied');
        setIsLoading(false);
        Alert.alert(
          'Permission Denied',
          'Location permission is required to show nearby restaurants. You can enable it in Settings or enter your address manually.',
          [
            { text: 'Enter Manually', onPress: () => setShowManualEntry(true) },
            { text: 'Try Again', onPress: handleEnableLocation },
          ]
        );
        return;
      }

      // Get current location
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      // Get address from coordinates
      const address = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      const userLocation = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        address: address[0],
        timestamp: Date.now(),
      };

      setUserLocation(userLocation);
      setPermissionStatus('granted');
      
      // Success animation
      Animated.parallel([
        Animated.timing(successAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // Navigate to next screen after animation
        setTimeout(() => {
          onLocationSet(userLocation);
          router.replace('/(tabs)');
        }, 1000);
      });

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Location error:', error);
      Alert.alert(
        'Location Error',
        'Unable to get your location. Please try again or enter your address manually.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleManualAddress = (addressData: any) => {
    setUserLocation(addressData);
    setShowManualEntry(false);
    onLocationSet(addressData);
    router.replace('/(tabs)');
  };

  const handleSkip = () => {
    Alert.alert(
      'Skip Location Setup?',
      'You can set your location later in settings, but you\'ll miss out on personalized recommendations.',
      [
        { text: 'Go Back', style: 'cancel' },
        { 
          text: 'Skip', 
          onPress: () => {
            router.replace('/(tabs)');
          }
        },
      ]
    );
  };

  return (
    <View style={styles.container}>
      {/* Background Pattern */}
      <View style={styles.backgroundPattern}>
        <MapPattern />
      </View>

      {/* Main Content */}
      <Animated.View 
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {/* Illustration */}
        <View style={styles.illustrationContainer}>
          <LocationIllustration 
            pulseAnim={pulseAnim}
            successAnim={successAnim}
            permissionStatus={permissionStatus}
          />
        </View>

        {/* Text Content */}
        <View style={styles.textContainer}>
          <Text style={styles.headline}>Enable Location Services</Text>
          <Text style={styles.subtext}>
            We need your location to show nearby restaurants and accurate delivery times
          </Text>

          {/* Benefits List */}
          <View style={styles.benefitsList}>
            <BenefitItem 
              icon="🔍" 
              text="Find restaurants near you" 
              delay={0}
            />
            <BenefitItem 
              icon="⏱️" 
              text="Accurate delivery estimates" 
              delay={200}
            />
            <BenefitItem 
              icon="⭐" 
              text="Better recommendations" 
              delay={400}
            />
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <LoadingButton
            title="Enable Location"
            onPress={handleEnableLocation}
            loading={isLoading}
            style={styles.primaryButton}
            disabled={permissionStatus === 'granted'}
          />

          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={() => setShowManualEntry(true)}
            disabled={isLoading}
          >
            <Text style={styles.secondaryButtonText}>Enter Address Manually</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.skipButton}
            onPress={handleSkip}
            disabled={isLoading}
          >
            <Text style={styles.skipButtonText}>Skip for now</Text>
          </TouchableOpacity>
        </View>
      </Animated.View>

      {/* Manual Address Modal */}
      <ManualAddressModal
        visible={showManualEntry}
        onClose={() => setShowManualEntry(false)}
        onAddressSelected={handleManualAddress}
      />
    </View>
  );
}

// Location illustration component
interface LocationIllustrationProps {
  pulseAnim: Animated.Value;
  successAnim: Animated.Value;
  permissionStatus: 'idle' | 'granted' | 'denied';
}

function LocationIllustration({ pulseAnim, successAnim, permissionStatus }: LocationIllustrationProps) {
  return (
    <View style={styles.illustration}>
      {/* Map Background */}
      <View style={styles.mapBackground}>
        <Text style={styles.mapEmoji}>🗺️</Text>
      </View>

      {/* Location Pin */}
      <Animated.View
        style={[
          styles.locationPin,
          {
            transform: [{ scale: pulseAnim }],
          },
        ]}
      >
        <LinearGradient
          colors={['#FF4444', '#FF6B35']}
          style={styles.pinGradient}
        >
          <Text style={styles.pinIcon}>📍</Text>
        </LinearGradient>

        {/* Pulse Rings */}
        <PulseRings pulseAnim={pulseAnim} />
      </Animated.View>

      {/* Success Checkmark */}
      {permissionStatus === 'granted' && (
        <Animated.View
          style={[
            styles.successCheckmark,
            {
              opacity: successAnim,
              transform: [{ scale: successAnim }],
            },
          ]}
        >
          <View style={styles.checkmarkCircle}>
            <Text style={styles.checkmarkIcon}>✓</Text>
          </View>
        </Animated.View>
      )}
    </View>
  );
}

// Pulse rings animation
function PulseRings({ pulseAnim }: { pulseAnim: Animated.Value }) {
  return (
    <>
      {[1, 2, 3].map((ring) => (
        <Animated.View
          key={ring}
          style={[
            styles.pulseRing,
            {
              transform: [
                {
                  scale: pulseAnim.interpolate({
                    inputRange: [1, 1.1],
                    outputRange: [1 + ring * 0.2, 1.1 + ring * 0.2],
                  }),
                },
              ],
              opacity: pulseAnim.interpolate({
                inputRange: [1, 1.1],
                outputRange: [0.3 - ring * 0.1, 0],
              }),
            },
          ]}
        />
      ))}
    </>
  );
}

// Benefit item component
interface BenefitItemProps {
  icon: string;
  text: string;
  delay: number;
}

function BenefitItem({ icon, text, delay }: BenefitItemProps) {
  const slideAnim = useRef(new Animated.Value(20)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.sequence([
      Animated.delay(delay),
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, []);

  return (
    <Animated.View
      style={[
        styles.benefitItem,
        {
          opacity: fadeAnim,
          transform: [{ translateX: slideAnim }],
        },
      ]}
    >
      <View style={styles.benefitIcon}>
        <Text style={styles.benefitEmoji}>{icon}</Text>
      </View>
      <Text style={styles.benefitText}>{text}</Text>
    </Animated.View>
  );
}

// Background map pattern
function MapPattern() {
  return (
    <View style={styles.mapPattern}>
      {/* Grid lines */}
      {Array.from({ length: 10 }).map((_, i) => (
        <View key={`h-${i}`} style={[styles.gridLine, styles.horizontalLine, { top: i * 60 }]} />
      ))}
      {Array.from({ length: 8 }).map((_, i) => (
        <View key={`v-${i}`} style={[styles.gridLine, styles.verticalLine, { left: i * 50 }]} />
      ))}
      
      {/* Scattered location pins */}
      <Text style={[styles.patternPin, { top: 100, left: 50 }]}>📍</Text>
      <Text style={[styles.patternPin, { top: 200, right: 80 }]}>🏪</Text>
      <Text style={[styles.patternPin, { bottom: 150, left: 100 }]}>🍕</Text>
      <Text style={[styles.patternPin, { top: 300, right: 120 }]}>📍</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  backgroundPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.03,
  },
  mapPattern: {
    flex: 1,
    position: 'relative',
  },
  gridLine: {
    position: 'absolute',
    backgroundColor: '#FF4444',
  },
  horizontalLine: {
    width: '100%',
    height: 1,
  },
  verticalLine: {
    height: '100%',
    width: 1,
  },
  patternPin: {
    position: 'absolute',
    fontSize: 20,
    opacity: 0.5,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 80,
    paddingBottom: 40,
  },
  illustrationContainer: {
    flex: 0.4,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
  },
  illustration: {
    width: 200,
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  mapBackground: {
    position: 'absolute',
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E9ECEF',
  },
  mapEmoji: {
    fontSize: 60,
    opacity: 0.3,
  },
  locationPin: {
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    zIndex: 2,
  },
  pinGradient: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#FF4444',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  pinIcon: {
    fontSize: 24,
  },
  pulseRing: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 2,
    borderColor: '#FF4444',
  },
  successCheckmark: {
    position: 'absolute',
    top: -10,
    right: -10,
    zIndex: 3,
  },
  checkmarkCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#34A853',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmarkIcon: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  textContainer: {
    flex: 0.4,
    alignItems: 'center',
  },
  headline: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C2C2C',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtext: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  benefitsList: {
    alignSelf: 'stretch',
    paddingHorizontal: 20,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  benefitIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#FFF5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  benefitEmoji: {
    fontSize: 16,
  },
  benefitText: {
    fontSize: 16,
    color: '#2C2C2C',
    flex: 1,
  },
  buttonContainer: {
    flex: 0.2,
    justifyContent: 'flex-end',
    gap: 12,
  },
  primaryButton: {
    marginBottom: 8,
  },
  secondaryButton: {
    height: 48,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#FF4444',
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF4444',
  },
  skipButton: {
    alignItems: 'center',
    paddingVertical: 12,
    marginTop: 8,
  },
  skipButtonText: {
    fontSize: 14,
    color: '#666666',
  },
});




