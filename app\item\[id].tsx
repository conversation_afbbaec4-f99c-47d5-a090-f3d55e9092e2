import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';

import ItemHero from '@/components/item/ItemHero';
import ItemInformation from '@/components/item/ItemInformation';
import CustomizationOptions from '@/components/item/CustomizationOptions';
import QuantityCart from '@/components/item/QuantityCart';
import RelatedItems from '@/components/item/RelatedItems';
import ItemReviews from '@/components/item/ItemReviews';
import { useItemDetail } from '@/hooks/useItemDetail';
import { useCart } from '@/hooks/useCart';

const { width, height } = Dimensions.get('window');

export interface ItemCustomization {
  size: 'small' | 'medium' | 'large';
  toppings: string[];
  spiceLevel: 'mild' | 'medium' | 'hot' | 'extra-hot';
  specialInstructions: string;
}

export default function ItemDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  
  const [quantity, setQuantity] = useState(1);
  const [customization, setCustomization] = useState<ItemCustomization>({
    size: 'medium',
    toppings: [],
    spiceLevel: 'medium',
    specialInstructions: '',
  });
  const [isAddingToCart, setIsAddingToCart] = useState(false);

  const { item, relatedItems, reviews, loading } = useItemDetail(id);
  const { addToCart } = useCart();

  useEffect(() => {
    StatusBar.setBarStyle('light-content');
    return () => StatusBar.setBarStyle('default');
  }, []);

  const handleBack = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.back();
  };

  const handleShare = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    console.log('Share item');
  };

  const calculateTotalPrice = () => {
    if (!item) return 0;
    
    let basePrice = item.price;
    
    // Size price adjustment
    const sizeMultiplier = {
      small: 0.8,
      medium: 1,
      large: 1.3,
    };
    basePrice *= sizeMultiplier[customization.size];
    
    // Toppings price
    const toppingsPrice = customization.toppings.length * 1.5; // $1.5 per topping
    
    return (basePrice + toppingsPrice) * quantity;
  };

  const handleAddToCart = async () => {
    if (!item) return;
    
    setIsAddingToCart(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const customizedItem = {
      ...item,
      customization,
      quantity,
      totalPrice: calculateTotalPrice(),
    };
    
    addToCart(customizedItem);
    setIsAddingToCart(false);
    
    // Success feedback
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    
    // Navigate back or show success message
    router.back();
  };

  const updateCustomization = (updates: Partial<ItemCustomization>) => {
    setCustomization(prev => ({ ...prev, ...updates }));
  };

  if (loading || !item) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading...</Text>
      </View>
    );
  }

  const totalPrice = calculateTotalPrice();

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      
      {/* Item Hero */}
      <ItemHero
        item={item}
        onBack={handleBack}
        onShare={handleShare}
      />

      {/* Content */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Item Information */}
        <ItemInformation item={item} />

        {/* Customization Options */}
        <CustomizationOptions
          customization={customization}
          onUpdate={updateCustomization}
        />

        {/* Related Items */}
        <RelatedItems
          items={relatedItems}
          onItemPress={(relatedItem) => {
            router.push(`/item/${relatedItem.id}`);
          }}
        />

        {/* Reviews */}
        <ItemReviews reviews={reviews} />

        {/* Bottom padding for fixed button */}
        <View style={{ height: 100 }} />
      </ScrollView>

      {/* Fixed Quantity & Cart Section */}
      <QuantityCart
        quantity={quantity}
        totalPrice={totalPrice}
        isLoading={isAddingToCart}
        onQuantityChange={setQuantity}
        onAddToCart={handleAddToCart}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingTop: 250, // Account for hero height
  },
});