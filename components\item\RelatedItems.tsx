import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Image } from 'expo-image';
import * as Haptics from 'expo-haptics';

interface RelatedItem {
  id: string;
  name: string;
  price: number;
  image: string;
  isVeg: boolean;
}

interface RelatedItemsProps {
  items: RelatedItem[];
  onItemPress: (item: RelatedItem) => void;
}

export default function RelatedItems({ items, onItemPress }: RelatedItemsProps) {
  if (!items || items.length === 0) {
    return null;
  }

  const handleItemPress = (item: RelatedItem) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onItemPress(item);
  };

  const handleQuickAdd = (item: RelatedItem, event: any) => {
    event.stopPropagation();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    console.log('Quick add:', item.name);
    // Add to cart logic here
  };

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>You might also like</Text>
      
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {items.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={styles.itemCard}
            onPress={() => handleItemPress(item)}
            activeOpacity={0.9}
          >
            <View style={styles.imageContainer}>
              <Image
                source={{ uri: item.image }}
                style={styles.itemImage}
                contentFit="cover"
              />
              
              {/* Veg/Non-veg indicator */}
              <View style={[
                styles.vegIndicator,
                { borderColor: item.isVeg ? '#34A853' : '#FF4444' }
              ]}>
                <View style={[
                  styles.vegDot,
                  { backgroundColor: item.isVeg ? '#34A853' : '#FF4444' }
                ]} />
              </View>
            </View>
            
            <View style={styles.itemInfo}>
              <Text style={styles.itemName} numberOfLines={2}>
                {item.name}
              </Text>
              <Text style={styles.itemPrice}>
                ${item.price.toFixed(2)}
              </Text>
            </View>
            
            <TouchableOpacity
              style={styles.quickAddButton}
              onPress={(event) => handleQuickAdd(item, event)}
              activeOpacity={0.7}
            >
              <Text style={styles.quickAddText}>+</Text>
            </TouchableOpacity>
          </TouchableOpacity>
        ))}
      </ScrollView>
      
      {/* Frequently Bought Together */}
      <View style={styles.frequentlyBoughtContainer}>
        <Text style={styles.frequentlyBoughtTitle}>
          Frequently bought together
        </Text>
        <View style={styles.comboSuggestion}>
          <View style={styles.comboItems}>
            <Text style={styles.comboText}>
              This item + Garlic Bread + Coke
            </Text>
            <Text style={styles.comboPrice}>
              Save $2.50 • Total: $18.99
            </Text>
          </View>
          <TouchableOpacity
            style={styles.addComboButton}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              console.log('Add combo');
            }}
            activeOpacity={0.7}
          >
            <Text style={styles.addComboText}>Add All</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  scrollContent: {
    paddingHorizontal: 16,
  },
  itemCard: {
    width: 140,
    marginRight: 12,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  imageContainer: {
    position: 'relative',
  },
  itemImage: {
    width: '100%',
    height: 100,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  vegIndicator: {
    position: 'absolute',
    top: 8,
    left: 8,
    width: 16,
    height: 16,
    borderWidth: 1.5,
    borderRadius: 2,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  vegDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  itemInfo: {
    padding: 12,
    paddingBottom: 8,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 4,
    lineHeight: 18,
  },
  itemPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FF4444',
  },
  quickAddButton: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  quickAddText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  frequentlyBoughtContainer: {
    marginTop: 20,
    paddingHorizontal: 16,
  },
  frequentlyBoughtTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 12,
  },
  comboSuggestion: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
  },
  comboItems: {
    flex: 1,
  },
  comboText: {
    fontSize: 14,
    color: '#2C2C2C',
    marginBottom: 4,
  },
  comboPrice: {
    fontSize: 12,
    color: '#34A853',
    fontWeight: '600',
  },
  addComboButton: {
    backgroundColor: '#FF4444',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  addComboText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});