import * as Haptics from 'expo-haptics';
import React, { useEffect, useRef, useState } from 'react';
import {
  Animated,
  Dimensions,
  RefreshControl,
  StyleSheet,
  Text,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import CategoriesGrid from '@/components/home/<USER>';
import HomeHeader from '@/components/home/<USER>';
import PromotionalBanner from '@/components/home/<USER>';
import QuickActions from '@/components/home/<USER>';
import RestaurantList from '@/components/home/<USER>';
import SearchBar from '@/components/home/<USER>';
import { useLocation } from '@/hooks/useLocation';
import { useRestaurants } from '@/hooks/useRestaurants';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function HomeScreen() {
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  
  const { userLocation } = useLocation();
  const { restaurants, loading, refreshRestaurants } = useRestaurants();
  
  const scrollY = useRef(new Animated.Value(0)).current;
  const headerHeight = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Initial data load
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      await refreshRestaurants();
    } catch (error) {
      console.error('Error loading initial data:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    try {
      await refreshRestaurants();
    } catch (error) {
      console.error('Error refreshing:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // Search will be handled by RestaurantList component
  };

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId === selectedCategory ? null : categoryId);
    Haptics.selectionAsync();
  };

  const handleScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { y: scrollY } } }],
    { 
      useNativeDriver: false,
      listener: (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        const newHeaderHeight = Math.max(0.7, 1 - offsetY / 200);
        
        Animated.timing(headerHeight, {
          toValue: newHeaderHeight,
          duration: 0,
          useNativeDriver: false,
        }).start();
      },
    }
  );

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      {/* Animated Header */}
      <Animated.View
        style={[
          styles.headerContainer,
          {
            transform: [{
              scaleY: headerHeight,
            }],
          },
        ]}
      >
        <HomeHeader />
      </Animated.View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <SearchBar
          value={searchQuery}
          onChangeText={handleSearch}
          placeholder="Search for restaurants or dishes"
        />
      </View>

      {/* Main Content */}
      <Animated.ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor="#FF4444"
            colors={['#FF4444']}
          />
        }
      >
        {/* Promotional Banner */}
        <View style={styles.section}>
          <PromotionalBanner />
        </View>

        {/* Categories Grid */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Categories</Text>
          <CategoriesGrid
            selectedCategory={selectedCategory}
            onCategorySelect={handleCategorySelect}
          />
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <QuickActions />
        </View>

        {/* Restaurant Listings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {selectedCategory ? `${selectedCategory} Restaurants` : 'Restaurants Near You'}
          </Text>
          <RestaurantList
            searchQuery={searchQuery}
            selectedCategory={selectedCategory}
            userLocation={userLocation}
          />
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </Animated.ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  headerContainer: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 10,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2C2C2C',
    marginHorizontal: 16,
    marginBottom: 16,
  },
  bottomSpacing: {
    height: 100,
  },
});


