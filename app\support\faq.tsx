import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: 'orders' | 'payments' | 'account' | 'delivery';
  helpful?: boolean;
}

interface FAQCategory {
  id: string;
  title: string;
  icon: string;
  items: FAQItem[];
}

export default function FAQScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const faqData: FAQCategory[] = [
    {
      id: 'orders',
      title: 'Orders & Delivery',
      icon: '🍽️',
      items: [
        {
          id: 'track_order',
          question: 'How do I track my order?',
          answer: 'You can track your order in real-time by going to "My Orders" and selecting your current order. You\'ll see live updates including preparation, pickup, and delivery status.',
          category: 'orders',
        },
        {
          id: 'delivery_time',
          question: 'How long does delivery take?',
          answer: 'Delivery times vary by restaurant and location, typically 25-45 minutes. You\'ll see an estimated delivery time before placing your order.',
          category: 'orders',
        },
        {
          id: 'modify_order',
          question: 'Can I modify my order after placing it?',
          answer: 'You can modify your order within 2 minutes of placing it, before the restaurant starts preparation. After that, please contact support for assistance.',
          category: 'orders',
        },
        {
          id: 'cancel_order',
          question: 'How do I cancel my order?',
          answer: 'You can cancel your order before the restaurant starts preparation. Go to "My Orders", select your order, and tap "Cancel Order". Refunds are processed automatically.',
          category: 'orders',
        },
      ],
    },
    {
      id: 'payments',
      title: 'Payments & Refunds',
      icon: '💳',
      items: [
        {
          id: 'payment_methods',
          question: 'What payment methods do you accept?',
          answer: 'We accept all major credit cards, debit cards, PayPal, Apple Pay, Google Pay, and cash on delivery (where available).',
          category: 'payments',
        },
        {
          id: 'refund_process',
          question: 'How do refunds work?',
          answer: 'Refunds are processed automatically to your original payment method within 3-5 business days. For cash orders, refunds are issued as app credits.',
          category: 'payments',
        },
        {
          id: 'billing_issues',
          question: 'I was charged incorrectly',
          answer: 'If you notice an incorrect charge, please contact our support team immediately. We\'ll investigate and process a refund if necessary.',
          category: 'payments',
        },
        {
          id: 'promo_codes',
          question: 'How do I use promotional codes?',
          answer: 'Enter your promo code at checkout in the "Promo Code" field. The discount will be applied automatically if the code is valid and meets the requirements.',
          category: 'payments',
        },
      ],
    },
    {
      id: 'account',
      title: 'Account & Profile',
      icon: '👤',
      items: [
        {
          id: 'create_account',
          question: 'How do I create an account?',
          answer: 'Tap "Sign Up" on the login screen and enter your email, phone number, and create a password. You can also sign up using Google or Facebook.',
          category: 'account',
        },
        {
          id: 'reset_password',
          question: 'I forgot my password',
          answer: 'Tap "Forgot Password" on the login screen and enter your email. We\'ll send you a reset link to create a new password.',
          category: 'account',
        },
        {
          id: 'update_profile',
          question: 'How do I update my profile information?',
          answer: 'Go to "Profile" in the app menu, then tap "Edit Profile". You can update your name, email, phone number, and delivery addresses.',
          category: 'account',
        },
        {
          id: 'delete_account',
          question: 'How do I delete my account?',
          answer: 'Go to "Profile" > "Settings" > "Account Settings" > "Delete Account". This action is permanent and cannot be undone.',
          category: 'account',
        },
      ],
    },
  ];

  const allFAQs = faqData.flatMap(category => category.items);

  const filteredFAQs = allFAQs.filter(item => {
    const matchesSearch = searchQuery === '' || 
      item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleHelpfulFeedback = (itemId: string, isHelpful: boolean) => {
    // Update helpful status
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    // In a real app, this would send feedback to the server
  };

  const renderFAQItem = (item: FAQItem) => {
    const isExpanded = expandedItems.includes(item.id);

    return (
      <View key={item.id} style={styles.faqItem}>
        <TouchableOpacity
          style={styles.faqQuestion}
          onPress={() => toggleExpanded(item.id)}
          activeOpacity={0.8}
        >
          <Text style={styles.questionText}>{item.question}</Text>
          <Text style={[
            styles.expandIcon,
            isExpanded && styles.expandIconRotated,
          ]}>
            +
          </Text>
        </TouchableOpacity>

        {isExpanded && (
          <Animated.View style={styles.faqAnswer}>
            <Text style={styles.answerText}>{item.answer}</Text>
            
            <View style={styles.feedbackContainer}>
              <Text style={styles.feedbackText}>Was this helpful?</Text>
              <View style={styles.feedbackButtons}>
                <TouchableOpacity
                  style={styles.feedbackButton}
                  onPress={() => handleHelpfulFeedback(item.id, true)}
                  activeOpacity={0.7}
                >
                  <Text style={styles.feedbackIcon}>👍</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.feedbackButton}
                  onPress={() => handleHelpfulFeedback(item.id, false)}
                  activeOpacity={0.7}
                >
                  <Text style={styles.feedbackIcon}>👎</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Animated.View>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Frequently Asked Questions</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Text style={styles.searchIcon}>🔍</Text>
        <TextInput
          style={styles.searchInput}
          placeholder="Search FAQs..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999999"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={() => setSearchQuery('')}
            activeOpacity={0.7}
          >
            <Text style={styles.clearIcon}>✕</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Category Filters */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesContainer}
        contentContainerStyle={styles.categoriesContent}
      >
        <TouchableOpacity
          style={[
            styles.categoryButton,
            selectedCategory === 'all' && styles.categoryButtonActive,
          ]}
          onPress={() => setSelectedCategory('all')}
          activeOpacity={0.8}
        >
          <Text style={[
            styles.categoryText,
            selectedCategory === 'all' && styles.categoryTextActive,
          ]}>
            All
          </Text>
        </TouchableOpacity>

        {faqData.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryButton,
              selectedCategory === category.id && styles.categoryButtonActive,
            ]}
            onPress={() => setSelectedCategory(category.id)}
            activeOpacity={0.8}
          >
            <Text style={styles.categoryIcon}>{category.icon}</Text>
            <Text style={[
              styles.categoryText,
              selectedCategory === category.id && styles.categoryTextActive,
            ]}>
              {category.title}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* FAQ List */}
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {filteredFAQs.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyIcon}>🔍</Text>
            <Text style={styles.emptyTitle}>No results found</Text>
            <Text style={styles.emptySubtitle}>
              Try different keywords or browse categories
            </Text>
          </View>
        ) : (
          <View style={styles.faqList}>
            {filteredFAQs.map(renderFAQItem)}
          </View>
        )}

        {/* Contact Support */}
        <View style={styles.contactSection}>
          <Text style={styles.contactTitle}>Still need help?</Text>
          <Text style={styles.contactSubtitle}>
            Can't find what you're looking for? Our support team is here to help.
          </Text>
          
          <TouchableOpacity
            style={styles.contactButton}
            onPress={() => router.push('/support/chat/general')}
            activeOpacity={0.8}
          >
            <Text style={styles.contactButtonText}>Contact Support</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  headerRight: {
    width: 40,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginVertical: 16,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchIcon: {
    fontSize: 16,
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#2C2C2C',
  },
  clearButton: {
    padding: 4,
  },
  clearIcon: {
    fontSize: 14,
    color: '#999999',
  },
  categoriesContainer: {
    maxHeight: 60,
  },
  categoriesContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  categoryButtonActive: {
    backgroundColor: '#FF4444',
    borderColor: '#FF4444',
  },
  categoryIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  categoryTextActive: {
    color: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  faqList: {
    paddingHorizontal: 16,
  },
  faqItem: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    marginBottom: 12,
    overflow: 'hidden',
  },
  faqQuestion: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  questionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2C',
    flex: 1,
    marginRight: 12,
  },
  expandIcon: {
    fontSize: 20,
    color: '#666666',
    fontWeight: 'bold',
    transform: [{ rotate: '0deg' }],
  },
  expandIconRotated: {
    transform: [{ rotate: '45deg' }],
  },
  faqAnswer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  answerText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 22,
    marginBottom: 16,
  },
  feedbackContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  feedbackText: {
    fontSize: 14,
    color: '#666666',
  },
  feedbackButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  feedbackButton: {
    padding: 8,
  },
  feedbackIcon: {
    fontSize: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
    opacity: 0.6,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
  },
  contactSection: {
    margin: 16,
    padding: 24,
    backgroundColor: '#F8F8F8',
    borderRadius: 16,
    alignItems: 'center',
  },
  contactTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 8,
  },
  contactSubtitle: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
  },
  contactButton: {
    backgroundColor: '#FF4444',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  contactButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});