import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  FlatList,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface MyReview {
  id: string;
  restaurantId: string;
  restaurantName: string;
  restaurantLogo: string;
  orderId: string;
  rating: number;
  date: string;
  text: string;
  photos: string[];
  helpfulVotes: number;
  canEdit: boolean;
  canDelete: boolean;
  status: 'published' | 'pending' | 'flagged';
}

interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  helpfulVotes: number;
  reviewStreak: number;
  mostReviewedCuisine: string;
}

export default function MyReviewsScreen() {
  const [selectedTab, setSelectedTab] = useState<'reviews' | 'stats'>('reviews');
  const [expandedReviews, setExpandedReviews] = useState<string[]>([]);

  const reviewStats: ReviewStats = {
    totalReviews: 47,
    averageRating: 4.2,
    helpfulVotes: 156,
    reviewStreak: 12,
    mostReviewedCuisine: 'Italian',
  };

  const myReviews: MyReview[] = [
    {
      id: '1',
      restaurantId: 'rest1',
      restaurantName: "Mario's Pizza Palace",
      restaurantLogo: '🍕',
      orderId: 'ORD123',
      rating: 5,
      date: '2024-01-15',
      text: 'Amazing pizza! The crust was perfectly crispy and the toppings were fresh. Delivery was super fast too. Definitely ordering again!',
      photos: ['photo1', 'photo2'],
      helpfulVotes: 12,
      canEdit: true,
      canDelete: true,
      status: 'published',
    },
    {
      id: '2',
      restaurantId: 'rest2',
      restaurantName: 'Sushi Express',
      restaurantLogo: '🍣',
      orderId: 'ORD124',
      rating: 4,
      date: '2024-01-10',
      text: 'Fresh sushi and good variety. The salmon was particularly good. Packaging could be better though.',
      photos: [],
      helpfulVotes: 8,
      canEdit: false,
      canDelete: false,
      status: 'published',
    },
    {
      id: '3',
      restaurantId: 'rest3',
      restaurantName: 'Burger Junction',
      restaurantLogo: '🍔',
      orderId: 'ORD125',
      rating: 3,
      date: '2024-01-08',
      text: 'Average burger. Nothing special but not bad either. Fries were cold when delivered.',
      photos: ['photo3'],
      helpfulVotes: 3,
      canEdit: true,
      canDelete: true,
      status: 'published',
    },
  ];

  const achievements = [
    { id: 'first_review', title: 'First Review', description: 'Wrote your first review', earned: true },
    { id: 'helpful_reviewer', title: 'Helpful Reviewer', description: '50+ helpful votes', earned: true },
    { id: 'photo_enthusiast', title: 'Photo Enthusiast', description: 'Added photos to 10 reviews', earned: true },
    { id: 'streak_master', title: 'Streak Master', description: '10 consecutive reviews', earned: true },
    { id: 'top_reviewer', title: 'Top Reviewer', description: '100+ reviews written', earned: false },
  ];

  const handleEditReview = (reviewId: string) => {
    const review = myReviews.find(r => r.id === reviewId);
    if (review) {
      router.push(`/reviews/create/${review.orderId}?edit=${reviewId}`);
    }
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleDeleteReview = (reviewId: string) => {
    Alert.alert(
      'Delete Review',
      'Are you sure you want to delete this review? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // Delete review logic
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          },
        },
      ]
    );
  };

  const toggleReviewExpansion = (reviewId: string) => {
    setExpandedReviews(prev => 
      prev.includes(reviewId)
        ? prev.filter(id => id !== reviewId)
        : [...prev, reviewId]
    );
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const renderStars = (rating: number, size: number = 16) => {
    return (
      <View style={styles.starsContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <Text
            key={star}
            style={[
              styles.star,
              { 
                fontSize: size,
                color: star <= rating ? '#FFD700' : '#E0E0E0',
              },
            ]}
          >
            ★
          </Text>
        ))}
      </View>
    );
  };

  const renderStatsCard = (title: string, value: string | number, subtitle?: string) => (
    <View style={styles.statsCard}>
      <Text style={styles.statsValue}>{value}</Text>
      <Text style={styles.statsTitle}>{title}</Text>
      {subtitle && <Text style={styles.statsSubtitle}>{subtitle}</Text>}
    </View>
  );

  const renderReview = ({ item }: { item: MyReview }) => {
    const isExpanded = expandedReviews.includes(item.id);
    const shouldTruncate = item.text.length > 100;
    const displayText = isExpanded || !shouldTruncate 
      ? item.text 
      : `${item.text.substring(0, 100)}...`;

    return (
      <View style={styles.reviewCard}>
        {/* Restaurant Info */}
        <View style={styles.reviewHeader}>
          <View style={styles.restaurantInfo}>
            <View style={styles.restaurantLogo}>
              <Text style={styles.restaurantLogoText}>{item.restaurantLogo}</Text>
            </View>
            <View style={styles.restaurantDetails}>
              <Text style={styles.restaurantName}>{item.restaurantName}</Text>
              <Text style={styles.orderInfo}>
                Order #{item.orderId} • {new Date(item.date).toLocaleDateString()}
              </Text>
            </View>
          </View>
          
          <View style={styles.reviewActions}>
            {item.canEdit && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleEditReview(item.id)}
                activeOpacity={0.7}
              >
                <Text style={styles.actionIcon}>✏️</Text>
              </TouchableOpacity>
            )}
            
            {item.canDelete && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleDeleteReview(item.id)}
                activeOpacity={0.7}
              >
                <Text style={styles.actionIcon}>🗑️</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Rating and Status */}
        <View style={styles.ratingRow}>
          {renderStars(item.rating, 18)}
          <View style={[
            styles.statusBadge,
            item.status === 'published' && styles.statusPublished,
            item.status === 'pending' && styles.statusPending,
            item.status === 'flagged' && styles.statusFlagged,
          ]}>
            <Text style={[
              styles.statusText,
              item.status === 'published' && styles.statusTextPublished,
              item.status === 'pending' && styles.statusTextPending,
              item.status === 'flagged' && styles.statusTextFlagged,
            ]}>
              {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
            </Text>
          </View>
        </View>

        {/* Review Text */}
        <Text style={styles.reviewText}>{displayText}</Text>
        
        {shouldTruncate && (
          <TouchableOpacity
            style={styles.readMoreButton}
            onPress={() => toggleReviewExpansion(item.id)}
            activeOpacity={0.7}
          >
            <Text style={styles.readMoreText}>
              {isExpanded ? 'Show less' : 'Show more'}
            </Text>
          </TouchableOpacity>
        )}

        {/* Photos */}
        {item.photos.length > 0 && (
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.photosContainer}
          >
            {item.photos.map((photo, index) => (
              <View key={index} style={styles.photoItem}>
                <View style={styles.photoPreview}>
                  <Text style={styles.photoIcon}>📷</Text>
                </View>
              </View>
            ))}
          </ScrollView>
        )}

        {/* Helpful Votes */}
        <View style={styles.helpfulContainer}>
          <Text style={styles.helpfulIcon}>👍</Text>
          <Text style={styles.helpfulText}>
            {item.helpfulVotes} people found this helpful
          </Text>
        </View>
      </View>
    );
  };

  const renderStatsTab = () => (
    <ScrollView 
      style={styles.statsContainer}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.statsContent}
    >
      {/* Overview Stats */}
      <View style={styles.statsGrid}>
        {renderStatsCard('Total Reviews', reviewStats.totalReviews)}
        {renderStatsCard('Average Rating', reviewStats.averageRating.toFixed(1), '⭐')}
        {renderStatsCard('Helpful Votes', reviewStats.helpfulVotes)}
        {renderStatsCard('Review Streak', reviewStats.reviewStreak, 'consecutive')}
      </View>

      {/* Most Reviewed Cuisine */}
      <View style={styles.cuisineSection}>
        <Text style={styles.sectionTitle}>Most Reviewed Cuisine</Text>
        <View style={styles.cuisineCard}>
          <Text style={styles.cuisineIcon}>🍝</Text>
          <Text style={styles.cuisineName}>{reviewStats.mostReviewedCuisine}</Text>
          <Text style={styles.cuisineCount}>12 reviews</Text>
        </View>
      </View>

      {/* Achievements */}
      <View style={styles.achievementsSection}>
        <Text style={styles.sectionTitle}>Achievements</Text>
        <View style={styles.achievementsList}>
          {achievements.map((achievement) => (
            <View 
              key={achievement.id} 
              style={[
                styles.achievementCard,
                !achievement.earned && styles.achievementCardLocked,
              ]}
            >
              <View style={styles.achievementIcon}>
                <Text style={styles.achievementIconText}>
                  {achievement.earned ? '🏆' : '🔒'}
                </Text>
              </View>
              <View style={styles.achievementDetails}>
                <Text style={[
                  styles.achievementTitle,
                  !achievement.earned && styles.achievementTitleLocked,
                ]}>
                  {achievement.title}
                </Text>
                <Text style={[
                  styles.achievementDescription,
                  !achievement.earned && styles.achievementDescriptionLocked,
                ]}>
                  {achievement.description}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    </ScrollView>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>My Reviews</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            selectedTab === 'reviews' && styles.tabButtonActive,
          ]}
          onPress={() => setSelectedTab('reviews')}
          activeOpacity={0.8}
        >
          <Text style={[
            styles.tabText,
            selectedTab === 'reviews' && styles.tabTextActive,
          ]}>
            Reviews ({myReviews.length})
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tabButton,
            selectedTab === 'stats' && styles.tabButtonActive,
          ]}
          onPress={() => setSelectedTab('stats')}
          activeOpacity={0.8}
        >
          <Text style={[
            styles.tabText,
            selectedTab === 'stats' && styles.tabTextActive,
          ]}>
            Stats & Achievements
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      {selectedTab === 'reviews' ? (
        <FlatList
          data={myReviews}
          renderItem={renderReview}
          keyExtractor={(item) => item.id}
          style={styles.reviewsList}
          contentContainerStyle={styles.reviewsContent}
          showsVerticalScrollIndicator={false}
          ItemSeparatorComponent={() => <View style={styles.reviewSeparator} />}
          ListEmptyComponent={() => (
            <View style={styles.emptyState}>
              <Text style={styles.emptyIcon}>📝</Text>
              <Text style={styles.emptyTitle}>No reviews yet</Text>
              <Text style={styles.emptySubtitle}>
                Start writing reviews to help other customers
              </Text>
            </View>
          )}
        />
      ) : (
        renderStatsTab()
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
  },
  headerRight: {
    width: 24,
  },
  tabsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#F0F0F0',
    paddingVertical: 8,
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
  },
  tabButtonActive: {
    borderBottomColor: '#FFA500',
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 16,
    color: '#555',
  },
  tabTextActive: {
    color: '#FFA500',
  },
  reviewsList: {
    flex: 1,
  },
  reviewsContent: {
    padding: 16,
  },
  reviewCard: {
    backgroundColor: '#FFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  restaurantInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  restaurantLogo: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  restaurantLogoText: {
    fontSize: 24,
    color: '#888',
  },
  restaurantDetails: {
    flex: 1,
  },
  restaurantName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  orderInfo: {
    fontSize: 14,
    color: '#777',
  },
  reviewActions: {
    flexDirection: 'row',
  },
  actionButton: {
    marginLeft: 12,
  },
  actionIcon: {
    fontSize: 20,
    color: '#FFA500',
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
  },
  starsContainer: {
    flexDirection: 'row',
    marginRight: 8,
  },
  star: {
    marginRight: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    borderWidth: 1,
  },
  statusPublished: {
    borderColor: '#4CAF50',
  },
  statusPending: {
    borderColor: '#FFC107',
  },
  statusFlagged: {
    borderColor: '#F44336',
  },
  statusText: {
    fontSize: 12,
  },
  statusTextPublished: {
    color: '#4CAF50',
  },
  statusTextPending: {
    color: '#FFC107',
  },
  statusTextFlagged: {
    color: '#F44336',
  },
  reviewText: {
    fontSize: 14,
    color: '#333',
  },
  readMoreButton: {
    marginTop: 8,
  },
  readMoreText: {
    fontSize: 14,
    color: '#FFA500',
  },
  photosContainer: {
    flexDirection: 'row',
    marginVertical: 8,
  },
  photoItem: {
    marginRight: 8,
  },
  photoPreview: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  photoIcon: {
    fontSize: 24,
    color: '#888',
  },
  helpfulContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
  },
  helpfulIcon: {
    fontSize: 20,
    color: '#FFA500',
    marginRight: 4,
  },
  helpfulText: {
    fontSize: 14,
    color: '#333',
  },
  statsContainer: {
    flex: 1,
  },
  statsContent: {
    padding: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statsCard: {
    width: '48%',
    marginBottom: 16,
    backgroundColor: '#FFF',
    borderRadius: 8,
    padding: 16,
    elevation: 2,
  },
  statsValue: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333',
  },
  statsTitle: {
    fontSize: 14,
    color: '#777',
  },
  statsSubtitle: {
    fontSize: 12,
    color: '#999',
  },
  cuisineSection: {
    marginBottom: 16,
  },
  cuisineCard: {
    backgroundColor: '#FFF',
    borderRadius: 8,
    padding: 16,
    elevation: 2,
  },
  cuisineIcon: {
    fontSize: 24,
    color: '#FFA500',
    marginRight: 8,
  },
  cuisineName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  cuisineCount: {
    fontSize: 14,
    color: '#777',
  },
  achievementsSection: {
    marginBottom: 16,
  },
  achievementsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  achievementCard: {
    width: '48%',
    marginBottom: 16,
    backgroundColor: '#FFF',
    borderRadius: 8,
    padding: 16,
    elevation: 2,
  },
  achievementCardLocked: {
    backgroundColor: '#F0F0F0',
  },
  achievementIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFA500',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  achievementIconText: {
    fontSize: 24,
    color: '#FFF',
  },
  achievementDetails: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  achievementTitleLocked: {
    color: '#888',
  },
  achievementDescription: {
    fontSize: 14,
    color: '#777',
  },
  achievementDescriptionLocked: {
    color: '#888',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyIcon: {
    fontSize: 48,
    color: '#888',
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#777',
  },
  reviewSeparator: {
    height: 1,
    backgroundColor: '#F0F0F0',
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    marginBottom: 16,
  },
});



