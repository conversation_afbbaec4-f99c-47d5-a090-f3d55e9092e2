import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import * as Haptics from 'expo-haptics';

interface Review {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  rating: number;
  date: string;
  text: string;
  photos: string[];
  helpfulCount: number;
  isHelpful: boolean;
  isVerified: boolean;
  orderItems: string[];
  restaurantResponse?: {
    text: string;
    date: string;
  };
}

interface RatingDistribution {
  rating: number;
  count: number;
  percentage: number;
}

export default function RestaurantReviewsScreen() {
  const { restaurantId } = useLocalSearchParams<{ restaurantId: string }>();
  const [selectedFilter, setSelectedFilter] = useState('recent');
  const [expandedReviews, setExpandedReviews] = useState<string[]>([]);
  const [helpfulReviews, setHelpfulReviews] = useState<string[]>([]);

  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Mock data
  const restaurantInfo = {
    name: "Mario's Pizza Palace",
    overallRating: 4.3,
    totalReviews: 1234,
    ratingTrend: 'up', // 'up', 'down', 'stable'
  };

  const ratingDistribution: RatingDistribution[] = [
    { rating: 5, count: 620, percentage: 50.2 },
    { rating: 4, count: 370, percentage: 30.0 },
    { rating: 3, count: 148, percentage: 12.0 },
    { rating: 2, count: 62, percentage: 5.0 },
    { rating: 1, count: 34, percentage: 2.8 },
  ];

  const reviews: Review[] = [
    {
      id: '1',
      userId: 'user1',
      userName: 'Sarah Johnson',
      userAvatar: '👩',
      rating: 5,
      date: '2024-01-15',
      text: 'Amazing pizza! The crust was perfectly crispy and the toppings were fresh. Delivery was super fast too. Definitely ordering again!',
      photos: ['photo1', 'photo2'],
      helpfulCount: 12,
      isHelpful: false,
      isVerified: true,
      orderItems: ['Margherita Pizza', 'Garlic Bread'],
      restaurantResponse: {
        text: 'Thank you for the wonderful review, Sarah! We\'re thrilled you enjoyed our pizza.',
        date: '2024-01-16',
      },
    },
    {
      id: '2',
      userId: 'user2',
      userName: 'Mike Chen',
      userAvatar: '👨',
      rating: 4,
      date: '2024-01-14',
      text: 'Good food overall. The pizza was tasty but took a bit longer than expected. Still worth it though!',
      photos: [],
      helpfulCount: 8,
      isHelpful: true,
      isVerified: true,
      orderItems: ['Pepperoni Pizza', 'Caesar Salad'],
    },
    {
      id: '3',
      userId: 'user3',
      userName: 'Emma Davis',
      userAvatar: '👩‍🦰',
      rating: 2,
      date: '2024-01-13',
      text: 'Pizza arrived cold and the cheese was congealed. Not what I expected for the price. Customer service was helpful though.',
      photos: ['photo3'],
      helpfulCount: 15,
      isHelpful: false,
      isVerified: true,
      orderItems: ['Supreme Pizza'],
      restaurantResponse: {
        text: 'We apologize for this experience, Emma. We\'ve addressed this with our kitchen team and would like to make it right.',
        date: '2024-01-13',
      },
    },
  ];

  const filters = [
    { id: 'recent', label: 'Most Recent', icon: '🕒' },
    { id: 'helpful', label: 'Most Helpful', icon: '👍' },
    { id: 'highest', label: 'Highest Rated', icon: '⭐' },
    { id: 'lowest', label: 'Lowest Rated', icon: '⭐' },
    { id: 'photos', label: 'With Photos', icon: '📷' },
    { id: 'verified', label: 'Verified Only', icon: '✅' },
  ];

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  const handleFilterChange = (filterId: string) => {
    setSelectedFilter(filterId);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const toggleReviewExpansion = (reviewId: string) => {
    setExpandedReviews(prev => 
      prev.includes(reviewId)
        ? prev.filter(id => id !== reviewId)
        : [...prev, reviewId]
    );
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleHelpfulVote = (reviewId: string) => {
    setHelpfulReviews(prev => 
      prev.includes(reviewId)
        ? prev.filter(id => id !== reviewId)
        : [...prev, reviewId]
    );
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const renderStars = (rating: number, size: number = 16) => {
    return (
      <View style={styles.starsContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <Text
            key={star}
            style={[
              styles.star,
              { 
                fontSize: size,
                color: star <= rating ? '#FFD700' : '#E0E0E0',
              },
            ]}
          >
            ★
          </Text>
        ))}
      </View>
    );
  };

  const renderRatingDistribution = () => {
    return (
      <View style={styles.distributionContainer}>
        {ratingDistribution.map((item) => (
          <View key={item.rating} style={styles.distributionRow}>
            <Text style={styles.distributionRating}>{item.rating}</Text>
            <Text style={styles.distributionStar}>★</Text>
            <View style={styles.distributionBarContainer}>
              <View 
                style={[
                  styles.distributionBar,
                  { width: `${item.percentage}%` }
                ]} 
              />
            </View>
            <Text style={styles.distributionCount}>{item.count}</Text>
          </View>
        ))}
      </View>
    );
  };

  const renderReview = ({ item }: { item: Review }) => {
    const isExpanded = expandedReviews.includes(item.id);
    const isHelpfulVoted = helpfulReviews.includes(item.id);
    const shouldTruncate = item.text.length > 150;
    const displayText = isExpanded || !shouldTruncate 
      ? item.text 
      : `${item.text.substring(0, 150)}...`;

    return (
      <View style={styles.reviewCard}>
        {/* User Info */}
        <View style={styles.reviewHeader}>
          <View style={styles.userInfo}>
            <View style={styles.userAvatar}>
              <Text style={styles.userAvatarText}>{item.userAvatar}</Text>
            </View>
            <View style={styles.userDetails}>
              <View style={styles.userNameRow}>
                <Text style={styles.userName}>{item.userName}</Text>
                {item.isVerified && (
                  <View style={styles.verifiedBadge}>
                    <Text style={styles.verifiedIcon}>✓</Text>
                  </View>
                )}
              </View>
              <Text style={styles.reviewDate}>
                {new Date(item.date).toLocaleDateString()}
              </Text>
            </View>
          </View>
          
          <TouchableOpacity
            style={styles.moreButton}
            onPress={() => {
              // Show report options
            }}
            activeOpacity={0.7}
          >
            <Text style={styles.moreIcon}>⋯</Text>
          </TouchableOpacity>
        </View>

        {/* Rating */}
        <View style={styles.reviewRating}>
          {renderStars(item.rating, 18)}
          <Text style={styles.reviewRatingText}>
            {item.rating}/5
          </Text>
        </View>

        {/* Order Info */}
        {item.isVerified && (
          <View style={styles.orderInfo}>
            <Text style={styles.orderLabel}>Verified Purchase:</Text>
            <Text style={styles.orderItems}>
              {item.orderItems.join(', ')}
            </Text>
          </View>
        )}

        {/* Review Text */}
        <Text style={styles.reviewText}>{displayText}</Text>
        
        {shouldTruncate && (
          <TouchableOpacity
            style={styles.readMoreButton}
            onPress={() => toggleReviewExpansion(item.id)}
            activeOpacity={0.7}
          >
            <Text style={styles.readMoreText}>
              {isExpanded ? 'Read less' : 'Read more'}
            </Text>
          </TouchableOpacity>
        )}

        {/* Photos */}
        {item.photos.length > 0 && (
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.photosContainer}
          >
            {item.photos.map((photo, index) => (
              <TouchableOpacity
                key={index}
                style={styles.photoItem}
                onPress={() => {
                  // Open photo viewer
                }}
                activeOpacity={0.8}
              >
                <View style={styles.photoPreview}>
                  <Text style={styles.photoIcon}>📷</Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        )}

        {/* Restaurant Response */}
        {item.restaurantResponse && (
          <View style={styles.restaurantResponse}>
            <View style={styles.responseHeader}>
              <Text style={styles.responseLabel}>Response from restaurant</Text>
              <Text style={styles.responseDate}>
                {new Date(item.restaurantResponse.date).toLocaleDateString()}
              </Text>
            </View>
            <Text style={styles.responseText}>
              {item.restaurantResponse.text}
            </Text>
          </View>
        )}

        {/* Actions */}
        <View style={styles.reviewActions}>
          <TouchableOpacity
            style={[
              styles.helpfulButton,
              isHelpfulVoted && styles.helpfulButtonActive,
            ]}
            onPress={() => handleHelpfulVote(item.id)}
            activeOpacity={0.8}
          >
            <Text style={[
              styles.helpfulIcon,
              isHelpfulVoted && styles.helpfulIconActive,
            ]}>
              👍
            </Text>
            <Text style={[
              styles.helpfulText,
              isHelpfulVoted && styles.helpfulTextActive,
            ]}>
              Helpful ({item.helpfulCount + (isHelpfulVoted ? 1 : 0)})
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.shareButton}
            onPress={() => {
              // Share review
            }}
            activeOpacity={0.8}
          >
            <Text style={styles.shareIcon}>📤</Text>
            <Text style={styles.shareText}>Share</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Reviews</Text>
        <View style={styles.headerRight} />
      </View>

      <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
        {/* Rating Summary */}
        <View style={styles.summaryContainer}>
          <View style={styles.overallRating}>
            <Text style={styles.ratingNumber}>{restaurantInfo.overallRating}</Text>
            <View style={styles.ratingDetails}>
              {renderStars(Math.round(restaurantInfo.overallRating), 20)}
              <Text style={styles.reviewCount}>
                Based on {restaurantInfo.totalReviews.toLocaleString()} reviews
              </Text>
              <View style={styles.trendContainer}>
                <Text style={[
                  styles.trendIcon,
                  restaurantInfo.ratingTrend === 'up' && styles.trendUp,
                  restaurantInfo.ratingTrend === 'down' && styles.trendDown,
                ]}>
                  {restaurantInfo.ratingTrend === 'up' ? '↗' : 
                   restaurantInfo.ratingTrend === 'down' ? '↘' : '→'}
                </Text>
                <Text style={styles.trendText}>Recent trend</Text>
              </View>
            </View>
          </View>

          {renderRatingDistribution()}
        </View>

        {/* Filters */}
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.filtersContainer}
          contentContainerStyle={styles.filtersContent}
        >
          {filters.map((filter) => (
            <TouchableOpacity
              key={filter.id}
              style={[
                styles.filterButton,
                selectedFilter === filter.id && styles.filterButtonActive,
              ]}
              onPress={() => handleFilterChange(filter.id)}
              activeOpacity={0.8}
            >
              <Text style={styles.filterIcon}>{filter.icon}</Text>
              <Text style={[
                styles.filterText,
                selectedFilter === filter.id && styles.filterTextActive,
              ]}>
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Reviews List */}
        <FlatList
          data={reviews}
          renderItem={renderReview}
          keyExtractor={(item) => item.id}
          style={styles.reviewsList}
          contentContainerStyle={styles.reviewsContent}
          showsVerticalScrollIndicator={false}
          ItemSeparatorComponent={() => <View style={styles.reviewSeparator} />}
        />
      </Animated.View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#2C2C2C',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  summaryContainer: {
    backgroundColor: '#F8F8F8',
    padding: 20,
    marginBottom: 16,
  },
  overallRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  ratingNumber: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#2C2C2C',
    marginRight: 20,
  },
  ratingDetails: {
    flex: 1,
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  star: {
    marginRight: 2,
  },
  reviewCount: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  trendUp: {
    color: '#34A853',
  },
  trendDown: {
    color: '#EA4335',
  },
  trendText: {
    fontSize: 12,
    color: '#666666',
  },
  distributionContainer: {
    gap: 8,
  },
  distributionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  distributionRating: {
    fontSize: 14,
    color: '#2C2C2C',
    width: 12,
  },
  distributionStar: {
    fontSize: 14,
    color: '#FFD700',
  },
  distributionBarContainer: {
    flex: 1,
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  distributionBar: {
    height: '100%',
    backgroundColor: '#FFD700',
  },
  distributionCount: {
    fontSize: 12,
    color: '#666666',
    width: 40,
    textAlign: 'right',
  },
  filtersContainer: {
    maxHeight: 60,
  },
  filtersContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  filterButtonActive: {
    backgroundColor: '#FF4444',
    borderColor: '#FF4444',
  },
  filterIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  filterTextActive: {
    color: '#FFFFFF',
  },
  reviewsList: {
    flex: 1,
  },
  reviewsContent: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  reviewCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#F0F0F0',
  },
  reviewSeparator: {
    height: 12,
  },
  reviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  userAvatarText: {
    fontSize: 20,
  },
  userDetails: {
    flex: 1,
  },
  userNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    marginRight: 8,
  },
  verifiedBadge: {
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: '#34A853',
    justifyContent: 'center',
    alignItems: 'center',
  },
  verifiedIcon: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  reviewDate: {
    fontSize: 12,
    color: '#666666',
  },
  moreButton: {
    padding: 8,
  },
  moreIcon: {
    fontSize: 16,
    color: '#666666',
  },
  reviewRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  reviewRatingText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 8,
  },
  orderInfo: {
    backgroundColor: '#E8F5E8',
    padding: 8,
    borderRadius: 8,
    marginBottom: 12,
  },
  orderLabel: {
    fontSize: 12,
    color: '#34A853',
    fontWeight: '600',
    marginBottom: 2,
  },
  orderItems: {
    fontSize: 12,
    color: '#34A853',
  },
  reviewText: {
    fontSize: 14,
    color: '#2C2C2C',
    lineHeight: 20,
    marginBottom: 8,
  },
  readMoreButton: {
    alignSelf: 'flex-start',
    marginBottom: 12,
  },
  readMoreText: {
    fontSize: 14,
    color: '#FF4444',
    fontWeight: '500',
  },
  photosContainer: {
    marginBottom: 12,
  },
  photoItem: {
    marginRight: 8,
  },
  photoPreview: {
    width: 60,
    height: 60,
    backgroundColor: '#F0F0F0',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoIcon: {
    fontSize: 24,
  },
  restaurantResponse: {
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    borderLeftWidth: 3,
    borderLeftColor: '#FF4444',
  },
  responseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  responseLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FF4444',
  },
  responseDate: {
    fontSize: 12,
    color: '#666666',
  },
  responseText: {
    fontSize: 14,
    color: '#2C2C2C',
    lineHeight: 18,
  },
  reviewActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  helpfulButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F8F8F8',
  },
  helpfulButtonActive: {
    backgroundColor: '#E8F5E8',
  },
  helpfulIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  helpfulIconActive: {
    color: '#34A853',
  },
  helpfulText: {
    fontSize: 12,
    color: '#666666',
  },
  helpfulTextActive: {
    color: '#34A853',
  },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F8F8F8',
  },
  shareIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  shareText: {
    fontSize: 12,
    color: '#666666',
  },
});