import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Location from 'expo-location';
import React, { createContext, useContext, useEffect, useState } from 'react';

interface LocationData {
  latitude: number;
  longitude: number;
  address?: Location.LocationGeocodedAddress;
  timestamp: number;
  title?: string;
  subtitle?: string;
  fullAddress?: string;
}

interface LocationContextType {
  userLocation: LocationData | null;
  isLocationLoading: boolean;
  hasLocationPermission: boolean;
  setUserLocation: (location: LocationData) => void;
  getCurrentLocation: () => Promise<LocationData | null>;
  requestLocationPermission: () => Promise<boolean>;
  clearLocation: () => void;
}

const LocationContext = createContext<LocationContextType | undefined>(undefined);

export function useLocation() {
  const context = useContext(LocationContext);
  if (!context) {
    throw new Error('useLocation must be used within a LocationProvider');
  }
  return context;
}

const LOCATION_STORAGE_KEY = 'userLocation';
const PERMISSION_STORAGE_KEY = 'locationPermission';

export function useLocationProvider(): LocationContextType {
  const [userLocation, setUserLocationState] = useState<LocationData | null>(null);
  const [isLocationLoading, setIsLocationLoading] = useState(false);
  const [hasLocationPermission, setHasLocationPermission] = useState(false);

  useEffect(() => {
    initializeLocation();
  }, []);

  const initializeLocation = async () => {
    try {
      // Check stored location
      const storedLocation = await AsyncStorage.getItem(LOCATION_STORAGE_KEY);
      if (storedLocation) {
        setUserLocationState(JSON.parse(storedLocation));
      }

      // Check permission status
      const { status } = await Location.getForegroundPermissionsAsync();
      setHasLocationPermission(status === 'granted');
    } catch (error) {
      console.error('Error initializing location:', error);
    }
  };

  const setUserLocation = async (location: LocationData) => {
    try {
      setUserLocationState(location);
      await AsyncStorage.setItem(LOCATION_STORAGE_KEY, JSON.stringify(location));
    } catch (error) {
      console.error('Error saving location:', error);
    }
  };

  const getCurrentLocation = async (): Promise<LocationData | null> => {
    try {
      setIsLocationLoading(true);

      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setHasLocationPermission(false);
        return null;
      }

      setHasLocationPermission(true);

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      const address = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        address: address[0],
        timestamp: Date.now(),
      };

      await setUserLocation(locationData);
      return locationData;
    } catch (error) {
      console.error('Error getting current location:', error);
      return null;
    } finally {
      setIsLocationLoading(false);
    }
  };

  const requestLocationPermission = async (): Promise<boolean> => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      const granted = status === 'granted';
      setHasLocationPermission(granted);
      
      if (granted) {
        await AsyncStorage.setItem(PERMISSION_STORAGE_KEY, 'granted');
      }
      
      return granted;
    } catch (error) {
      console.error('Error requesting location permission:', error);
      return false;
    }
  };

  const clearLocation = async () => {
    try {
      setUserLocationState(null);
      await AsyncStorage.removeItem(LOCATION_STORAGE_KEY);
    } catch (error) {
      console.error('Error clearing location:', error);
    }
  };

  return {
    userLocation,
    isLocationLoading,
    hasLocationPermission,
    setUserLocation,
    getCurrentLocation,
    requestLocationPermission,
    clearLocation,
  };
}

export function LocationProvider({ children }: { children: React.ReactNode }) {
  const locationValue = useLocationProvider();
  
  return (
    <LocationContext.Provider value={locationValue}>
      {children}
    </LocationContext.Provider>
  );
}


