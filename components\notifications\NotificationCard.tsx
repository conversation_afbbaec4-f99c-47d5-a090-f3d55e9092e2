import React, { useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  PanGestureHandler,
  State,
} from 'react-native';
import { Swipeable, GestureHandlerRootView } from 'react-native-gesture-handler';
import * as Haptics from 'expo-haptics';
import { Notification } from '@/app/notifications';

interface NotificationCardProps {
  notification: Notification;
  isSelected: boolean;
  isSelectionMode: boolean;
  onPress: () => void;
  onLongPress: () => void;
  onToggleRead: (id: string) => void;
  onDelete: (id: string) => void;
}

export default function NotificationCard({
  notification,
  isSelected,
  isSelectionMode,
  onPress,
  onLongPress,
  onToggleRead,
  onDelete,
}: NotificationCardProps) {
  const swipeableRef = useRef<Swipeable>(null);

  const getNotificationIcon = () => {
    const iconMap = {
      // Order notifications
      confirmed: { icon: '✅', color: '#34A853' },
      preparing: { icon: '👨‍🍳', color: '#FF6B35' },
      delivery: { icon: '🚚', color: '#4285F4' },
      delivered: { icon: '📦', color: '#34A853' },
      cancelled: { icon: '❌', color: '#FF4444' },
      
      // Promotional notifications
      new_offer: { icon: '🎁', color: '#FFD700' },
      promotion: { icon: '💯', color: '#FF4444' },
      cashback: { icon: '💰', color: '#34A853' },
      referral: { icon: '👥', color: '#4285F4' },
      
      // App updates
      feature: { icon: '⭐', color: '#FF4444' },
      maintenance: { icon: '🔧', color: '#666666' },
      policy: { icon: '📄', color: '#666666' },
    };

    return iconMap[notification.category] || { icon: '🔔', color: '#666666' };
  };

  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  const renderRightActions = () => {
    return (
      <View style={styles.rightActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.readButton]}
          onPress={() => {
            onToggleRead(notification.id);
            swipeableRef.current?.close();
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }}
          activeOpacity={0.8}
        >
          <Text style={styles.actionIcon}>
            {notification.isRead ? '👁️‍🗨️' : '👁️'}
          </Text>
          <Text style={styles.actionText}>
            {notification.isRead ? 'Unread' : 'Read'}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderLeftActions = () => {
    return (
      <View style={styles.leftActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => {
            onDelete(notification.id);
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          }}
          activeOpacity={0.8}
        >
          <Text style={styles.actionIcon}>🗑️</Text>
          <Text style={styles.actionText}>Delete</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const { icon, color } = getNotificationIcon();

  return (
    <GestureHandlerRootView>
      <Swipeable
        ref={swipeableRef}
        renderRightActions={renderRightActions}
        renderLeftActions={renderLeftActions}
        enabled={!isSelectionMode}
      >
        <TouchableOpacity
          style={[
            styles.container,
            !notification.isRead && styles.unreadContainer,
            notification.isImportant && styles.importantContainer,
            isSelected && styles.selectedContainer,
          ]}
          onPress={onPress}
          onLongPress={onLongPress}
          activeOpacity={0.8}
        >
          {/* Selection Checkbox */}
          {isSelectionMode && (
            <View style={styles.checkboxContainer}>
              <View style={[
                styles.checkbox,
                isSelected && styles.checkboxSelected,
              ]}>
                {isSelected && <Text style={styles.checkmark}>✓</Text>}
              </View>
            </View>
          )}

          {/* Unread Indicator */}
          {!notification.isRead && !isSelectionMode && (
            <View style={styles.unreadIndicator} />
          )}

          {/* Notification Icon */}
          <View style={[styles.iconContainer, { backgroundColor: `${color}15` }]}>
            <Text style={[styles.icon, { color }]}>{icon}</Text>
          </View>

          {/* Notification Content */}
          <View style={styles.content}>
            <View style={styles.header}>
              <Text style={[
                styles.title,
                notification.isExpired && styles.expiredText,
              ]}>
                {notification.title}
              </Text>
              <Text style={styles.timestamp}>
                {formatTimestamp(notification.timestamp)}
              </Text>
            </View>

            <Text style={[
              styles.message,
              notification.isExpired && styles.expiredText,
            ]}>
              {notification.message}
            </Text>

            {/* Action Button */}
            {notification.actionButton && !notification.isExpired && (
              <TouchableOpacity
                style={styles.actionButtonContainer}
                onPress={(e) => {
                  e.stopPropagation();
                  notification.actionButton?.action();
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
                activeOpacity={0.7}
              >
                <Text style={styles.actionButtonText}>
                  {notification.actionButton.text}
                </Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Important Border */}
          {notification.isImportant && (
            <View style={styles.importantBorder} />
          )}
        </TouchableOpacity>
      </Swipeable>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#F9F9F9',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    position: 'relative',
  },
  unreadContainer: {
    backgroundColor: '#FFFFFF',
  },
  importantContainer: {
    borderLeftWidth: 4,
    borderLeftColor: '#FFD700',
  },
  selectedContainer: {
    backgroundColor: '#E3F2FD',
  },
  checkboxContainer: {
    marginRight: 12,
    justifyContent: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#CCCCCC',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: '#FF4444',
    borderColor: '#FF4444',
  },
  checkmark: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  unreadIndicator: {
    position: 'absolute',
    left: 8,
    top: 20,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4285F4',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  icon: {
    fontSize: 20,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2C',
    flex: 1,
    marginRight: 8,
  },
  timestamp: {
    fontSize: 12,
    color: '#999999',
  },
  message: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 8,
  },
  expiredText: {
    textDecorationLine: 'line-through',
    opacity: 0.6,
  },
  actionButtonContainer: {
    alignSelf: 'flex-start',
    backgroundColor: '#FF4444',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginTop: 4,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  importantBorder: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: 4,
    backgroundColor: '#FFD700',
  },
  rightActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 80,
    height: '100%',
  },
  readButton: {
    backgroundColor: '#4285F4',
  },
  deleteButton: {
    backgroundColor: '#FF4444',
  },
  actionIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  actionText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});